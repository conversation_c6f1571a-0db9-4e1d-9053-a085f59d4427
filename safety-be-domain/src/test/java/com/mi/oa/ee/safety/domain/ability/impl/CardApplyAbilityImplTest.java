package com.mi.oa.ee.safety.domain.ability.impl;

import com.google.common.cache.Cache;
import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.common.enums.AccountTypeEnum;
import com.mi.oa.ee.safety.common.enums.card.CardApplyStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.exception.BizException;
import com.mi.oa.ee.safety.domain.errorcode.CardApplyDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.CardApplyConfigDo;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;
import com.mi.oa.ee.safety.infra.remote.sdk.IdmSdk;
import com.mi.oa.ee.safety.infra.repository.CardApplyConfigRepository;
import com.mi.oa.ee.safety.infra.repository.CardApplyRepository;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.concurrent.Callable;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;


class CardApplyAbilityImplTest {

    @InjectMocks
    private CardApplyAbilityImpl cardApplyAbility;

    @Mock
    private CardApplyRepository cardApplyRepository;

    @Mock
    private IdmSdk idmSdk;

    @Mock(name = "avatarUrlCache")
    private Cache<String, Object> avatarUrlCache;

    @Mock
    private RedisTemplate redisTemplate;

    @Mock
    private CardApplyConfigRepository cardApplyConfigRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        // Set up innerLandIds field for testing
        ReflectionTestUtils.setField(cardApplyAbility, "innerLandIds", Lists.newArrayList("1", "2"));
    }

    @Test
    void findAvatarUrlByUidList_WhenCacheHit_ShouldReturnFromCache() throws Exception {
        // 准备测试数据
        List<String> uidList = Lists.newArrayList("uid1", "uid2");
        when(avatarUrlCache.get(eq("uid1"), any(Callable.class))).thenReturn("photo1");
        when(avatarUrlCache.get(eq("uid2"), any(Callable.class))).thenReturn("photo2");

        // 执行测试
        List<CardApplyDo> result = cardApplyAbility.findAvatarUrlByUidList(uidList);

        // 验证结果
        assertEquals(2, result.size());
        assertEquals("uid1", result.get(0).getUid());
        assertEquals("photo1", result.get(0).getPhotoUrl());
        assertEquals("uid2", result.get(1).getUid());
        assertEquals("photo2", result.get(1).getPhotoUrl());
        Mockito.verify(avatarUrlCache, Mockito.times(2)).get(anyString(), any(Callable.class));
    }

    @Test
    void findAvatarUrlByUidList_WhenCacheMiss_ShouldLoadFromRepository() throws Exception {
        // 准备静态mock
        MockedStatic<RedisUtils> mockedStatic = mockStatic(RedisUtils.class);
        // 准备测试数据
        List<String> uidList = Lists.newArrayList("uid1");
        CardApplyDo cardApplyDo = createCardApplyDo("uid1", "photo1");
        when(avatarUrlCache.get(eq("uid1"), any(Callable.class))).thenAnswer(invocation -> {
            Callable<String> loader = invocation.getArgument(1);
            return loader.call();
        });
        when(cardApplyRepository.findAvatarUrlByUid("uid1")).thenReturn(cardApplyDo);
        mockedStatic.when(() -> RedisUtils.get(anyString())).thenReturn(null);
        // 执行测试
        List<CardApplyDo> result = cardApplyAbility.findAvatarUrlByUidList(uidList);

        // 验证结果
        assertEquals(1, result.size());
        assertEquals("uid1", result.get(0).getUid());
        assertEquals("photo1", result.get(0).getPhotoUrl());
        Mockito.verify(avatarUrlCache).get(anyString(), any(Callable.class));
        Mockito.verify(cardApplyRepository).findAvatarUrlByUid("uid1");
    }

    @Test
    void findAvatarUrlByUidList_WhenCacheError_ShouldHandleException() throws Exception {
        // 准备测试数据
        List<String> uidList = Lists.newArrayList("uid1");
        when(avatarUrlCache.get(eq("uid1"), any(Callable.class))).thenThrow(new RuntimeException("Cache error"));

        // 执行测试
        List<CardApplyDo> result = cardApplyAbility.findAvatarUrlByUidList(uidList);

        // 验证结果
        assertEquals(1, result.size());
        assertEquals("uid1", result.get(0).getUid());
        assertEquals("", result.get(0).getPhotoUrl());
        Mockito.verify(avatarUrlCache).get(anyString(), any(Callable.class));
    }

    @Test
    void findAvatarUrlByUidList_WhenEmptyUid_ShouldSkip() throws Exception {
        // 准备测试数据
        List<String> uidList = Lists.newArrayList("", null, "uid1");
        when(avatarUrlCache.get(eq("uid1"), any(Callable.class))).thenReturn("photo1");

        // 执行测试
        List<CardApplyDo> result = cardApplyAbility.findAvatarUrlByUidList(uidList);

        // 验证结果
        assertEquals(1, result.size());
        assertEquals("uid1", result.get(0).getUid());
        assertEquals("photo1", result.get(0).getPhotoUrl());
        Mockito.verify(avatarUrlCache, Mockito.times(1)).get(anyString(), any(Callable.class));
    }

    private CardApplyDo createCardApplyDo(String uid, String photoUrl) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setUid(uid);
        cardApplyDo.setPhotoUrl(photoUrl);
        return cardApplyDo;
    }

    // ========== fillEmpApplyInfo 测试用例 ==========

    @Test
    void fillEmpApplyInfo_WhenIsSync_ShouldSetCompletedStatusAndReturn() {
        // 准备测试数据
        CardApplyDo cardApplyDo = createEmpCardApplyDo();
        cardApplyDo.putExtField("isSync", true);

        // 执行测试
        cardApplyAbility.fillEmpApplyInfo(cardApplyDo);

        // 验证结果
        assertEquals(CardTypeEnum.EMPLOYEE_CARD.getNumber(), cardApplyDo.getApplyType());
        assertEquals(AccountTypeEnum.EMPLOYEE.getValue(), cardApplyDo.getEmpType());
        assertEquals(CardApplyStatusEnum.COMPLETED.getCode(), cardApplyDo.getApplyStatus());
        assertNotNull(cardApplyDo.getStartTime());
        assertNotNull(cardApplyDo.getEndTime());
    }

    @Test
    void fillEmpApplyInfo_WhenParkCodeExistsWithConfig_ShouldUseConfigStatus() {
        // 准备测试数据
        CardApplyDo cardApplyDo = createEmpCardApplyDo();
        cardApplyDo.setParkCode("TEST_PARK");
        cardApplyDo.putExtField("isSync", false);

        CardApplyConfigDo configDo = new CardApplyConfigDo();
        configDo.setApplyInitialStatus(CardApplyStatusEnum.WAIT_PRINT_CARD.getCode());
        configDo.setCountryId("3");

        when(cardApplyConfigRepository.findByParkCodeAndEmpType("TEST_PARK", "正式员工"))
                .thenReturn(configDo);

        // 执行测试
        cardApplyAbility.fillEmpApplyInfo(cardApplyDo);

        // 验证结果
        assertEquals(CardApplyStatusEnum.WAIT_PRINT_CARD.getCode(), cardApplyDo.getApplyStatus());
        assertEquals("3", cardApplyDo.getCountryId());
    }

    @Test
    void fillEmpApplyInfo_WhenParkCodeExistsNoConfigButEmployee_ShouldUseFallback() {
        // 准备测试数据
        CardApplyDo cardApplyDo = createEmpCardApplyDo();
        cardApplyDo.setParkCode("TEST_PARK");
        cardApplyDo.setCountryId("1"); // 内陆国家
        cardApplyDo.putExtField("isSync", false);

        when(cardApplyConfigRepository.findByParkCodeAndEmpType("TEST_PARK", "正式员工"))
                .thenReturn(null);

        // 执行测试
        cardApplyAbility.fillEmpApplyInfo(cardApplyDo);

        // 验证结果
        assertEquals(CardApplyStatusEnum.WAIT_UPLOAD.getCode(), cardApplyDo.getApplyStatus());
    }

    @Test
    void fillEmpApplyInfo_WhenParkCodeExistsNoConfigButVendor_ShouldUseFallback() {
        // 准备测试数据
        CardApplyDo cardApplyDo = createVendorCardApplyDo();
        cardApplyDo.setParkCode("TEST_PARK");
        cardApplyDo.setCountryId("5"); // 外国
        cardApplyDo.putExtField("isSync", false);

        when(cardApplyConfigRepository.findByParkCodeAndEmpType("TEST_PARK", "外包员工"))
                .thenReturn(null);

        // 执行测试
        cardApplyAbility.fillEmpApplyInfo(cardApplyDo);

        // 验证结果
        assertEquals(CardApplyStatusEnum.WAIT_OPEN_CARD.getCode(), cardApplyDo.getApplyStatus());
    }

    @Test
    void fillEmpApplyInfo_WhenParkCodeExistsNoConfigAndInvalidEmpType_ShouldThrowException() {
        // 准备测试数据
        CardApplyDo cardApplyDo = createPartnerCardApplyDo();
        cardApplyDo.setParkCode("TEST_PARK");
        cardApplyDo.putExtField("isSync", false);

        when(cardApplyConfigRepository.findByParkCodeAndEmpType("TEST_PARK", "合作伙伴"))
                .thenReturn(null);

        // 执行测试并验证异常
        BizException exception = assertThrows(BizException.class, () -> {
            cardApplyAbility.fillEmpApplyInfo(cardApplyDo);
        });

        assertEquals(CardApplyDomainErrorCodeEnum.EMP_TYPE_NOT_NEED_EMP_CARD.getErrorCode(),
                     exception.getErrorCode());
    }

    @Test
    void fillEmpApplyInfo_WhenNoParkCodeButEmployee_ShouldUseFallback() {
        // 准备测试数据
        CardApplyDo cardApplyDo = createEmpCardApplyDo();
        cardApplyDo.setParkCode(null);
        cardApplyDo.setCountryId("1"); // 内陆国家
        cardApplyDo.putExtField("isSync", false);

        // 执行测试
        cardApplyAbility.fillEmpApplyInfo(cardApplyDo);

        // 验证结果
        assertEquals(CardApplyStatusEnum.WAIT_UPLOAD.getCode(), cardApplyDo.getApplyStatus());
    }

    @Test
    void fillEmpApplyInfo_WhenNoParkCodeButVendor_ShouldUseFallback() {
        // 准备测试数据
        CardApplyDo cardApplyDo = createVendorCardApplyDo();
        cardApplyDo.setParkCode(null);
        cardApplyDo.setCountryId("5"); // 外国
        cardApplyDo.putExtField("isSync", false);

        // 执行测试
        cardApplyAbility.fillEmpApplyInfo(cardApplyDo);

        // 验证结果
        assertEquals(CardApplyStatusEnum.WAIT_OPEN_CARD.getCode(), cardApplyDo.getApplyStatus());
    }

    @Test
    void fillEmpApplyInfo_WhenNoParkCodeAndInvalidEmpType_ShouldThrowException() {
        // 准备测试数据
        CardApplyDo cardApplyDo = createPartnerCardApplyDo();
        cardApplyDo.setParkCode(null);
        cardApplyDo.putExtField("isSync", false);

        // 执行测试并验证异常
        BizException exception = assertThrows(BizException.class, () -> {
            cardApplyAbility.fillEmpApplyInfo(cardApplyDo);
        });

        assertEquals(CardApplyDomainErrorCodeEnum.EMP_TYPE_NOT_NEED_EMP_CARD.getErrorCode(),
                     exception.getErrorCode());
    }

    @Test
    void fillEmpApplyInfo_WhenConfigExistsButCountryIdBlank_ShouldSetCountryIdFromConfig() {
        // 准备测试数据
        CardApplyDo cardApplyDo = createEmpCardApplyDo();
        cardApplyDo.setParkCode("TEST_PARK");
        cardApplyDo.setCountryId(""); // 空的国家ID
        cardApplyDo.putExtField("isSync", false);

        CardApplyConfigDo configDo = new CardApplyConfigDo();
        configDo.setApplyInitialStatus(CardApplyStatusEnum.WAIT_PRINT_CARD.getCode());
        configDo.setCountryId("3");

        when(cardApplyConfigRepository.findByParkCodeAndEmpType("TEST_PARK", "正式员工"))
                .thenReturn(configDo);

        // 执行测试
        cardApplyAbility.fillEmpApplyInfo(cardApplyDo);

        // 验证结果
        assertEquals("3", cardApplyDo.getCountryId());
        assertEquals(CardApplyStatusEnum.WAIT_PRINT_CARD.getCode(), cardApplyDo.getApplyStatus());
    }

    // ========== 辅助方法 ==========

    private CardApplyDo createEmpCardApplyDo() {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setUid("test_uid");

        SafetyPersonDo personInfo = new SafetyPersonDo();
        personInfo.setAccountType(AccountTypeEnum.EMPLOYEE);
        cardApplyDo.setPersonInfo(personInfo);

        return cardApplyDo;
    }

    private CardApplyDo createVendorCardApplyDo() {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setUid("test_uid");

        SafetyPersonDo personInfo = new SafetyPersonDo();
        personInfo.setAccountType(AccountTypeEnum.VENDOR);
        cardApplyDo.setPersonInfo(personInfo);

        return cardApplyDo;
    }

    private CardApplyDo createPartnerCardApplyDo() {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setUid("test_uid");

        SafetyPersonDo personInfo = new SafetyPersonDo();
        personInfo.setAccountType(AccountTypeEnum.PARTNER);
        cardApplyDo.setPersonInfo(personInfo);

        return cardApplyDo;
    }
}