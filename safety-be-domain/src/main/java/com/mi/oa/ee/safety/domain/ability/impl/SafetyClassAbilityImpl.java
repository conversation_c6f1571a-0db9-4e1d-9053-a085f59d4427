package com.mi.oa.ee.safety.domain.ability.impl;

import cn.hutool.core.util.ObjectUtil;
import com.mi.oa.ee.safety.domain.ability.SafetyClassAbility;
import com.mi.oa.ee.safety.domain.errorcode.SafetyClassErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.SafetyClassDo;
import com.mi.oa.ee.safety.infra.repository.SafetyCarrierGroupRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyClassRepository;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 安防分类的能力
 *
 * <AUTHOR>
 * @date 2022/8/23 17:53
 */
@Component
public class SafetyClassAbilityImpl implements SafetyClassAbility {

    @Autowired
    SafetyCarrierGroupRepository safetyCarrierGroupRepository;

    @Autowired
    SafetyClassRepository safetyClassRepository;

    @Override
    public void checkAndFillClass(SafetyClassDo safetyClassDO) {
        //校验数据
        if (safetyClassDO == null) {
            throw new BizException(SafetyClassErrorCodeEnum.IS_EMPTY);
        }

        //如果是0，说明是根节点，不处理
        if (OAUCFCommonConstants.STR_ZERO.equals(safetyClassDO.getClassCode())) {
            safetyClassDO.setName("{{{根节点}}}");
            return;
        }

        if (StringUtils.isEmpty(safetyClassDO.getClassCode())) {
            throw new BizException(SafetyClassErrorCodeEnum.CLASS_CODE_IS_EMPTY);
        }

        SafetyClassDo nowDO = safetyClassRepository.findSafetyClassByClassCode(safetyClassDO.getClassCode());
        if (nowDO == null) {
            throw new BizException(SafetyClassErrorCodeEnum.CLASS_NOT_EXISTS);
        } else {
            BeanUtils.copyProperties(nowDO, safetyClassDO);
        }
    }

    @Override
    public void fillSubSafetyClass(SafetyClassDo safetyClassDO) {
        SafetyClassDo query = new SafetyClassDo();
        query.setParentClassCode(safetyClassDO.getClassCode());
        safetyClassDO.setSubSafetyClass(safetyClassRepository.listByConditions(query));
    }

    @Override
    public void fillSubSafetyClassForList(List<SafetyClassDo> list) {
        List<String> parentCodes = list.stream().map(SafetyClassDo::getClassCode).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(parentCodes)) {
            return;
        }

        List<SafetyClassDo> subSafetyClass = safetyClassRepository.getListByParentCodes(parentCodes);
        if (ObjectUtil.isEmpty(subSafetyClass)) {
            return;
        }
        // 循环填充
        fillSubSafetyClassForList(subSafetyClass);
        Map<String, List<SafetyClassDo>> subSafetyClassMap = subSafetyClass.stream().collect(Collectors.groupingBy(SafetyClassDo::getParentClassCode));
        list.forEach(safetyClassDO -> safetyClassDO.setSubSafetyClass(subSafetyClassMap.get(safetyClassDO.getClassCode())));
    }

    @Override
    public void loadSafetyCarrierGroupDoList(SafetyClassDo safetyClassDo) {
        safetyClassDo.setSafetyCarrierGroupDOList(
                safetyCarrierGroupRepository.listByClassCode(safetyClassDo.getClassCode()));
    }

    @Override
    public List<SafetyClassDo> getListByClassCodes(List<String> classCodeList) {
        return safetyClassRepository.getListByClassCodes(classCodeList);
    }
}
