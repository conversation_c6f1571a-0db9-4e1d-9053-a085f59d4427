package com.mi.oa.ee.safety.domain.ability.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.cache.Cache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.common.config.WorkbenchProperties;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.*;
import com.mi.oa.ee.safety.common.enums.AccountTypeEnum;
import com.mi.oa.ee.safety.common.enums.AppCodeEnum;
import com.mi.oa.ee.safety.common.enums.DeptLevelEnum;
import com.mi.oa.ee.safety.common.enums.ZonedNumberEnum;
import com.mi.oa.ee.safety.common.enums.card.*;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.common.utils.JsonUtils;
import com.mi.oa.ee.safety.domain.ability.CardApplyAbility;
import com.mi.oa.ee.safety.domain.converter.CommonDoConverter;
import com.mi.oa.ee.safety.domain.errorcode.CardApplyDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.errorcode.CardInfoDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.*;
import com.mi.oa.ee.safety.domain.query.card.CardApplyQuery;
import com.mi.oa.ee.safety.infra.errorcode.CardApplyErrorCodeEnum;
import com.mi.oa.ee.safety.infra.remote.model.SearchUserCardInfoReq;
import com.mi.oa.ee.safety.infra.remote.model.supplier.CardInfoRemoteDto;
import com.mi.oa.ee.safety.infra.remote.nacos.CountryMappingConfig;
import com.mi.oa.ee.safety.infra.remote.sdk.*;
import com.mi.oa.ee.safety.infra.remote.sdk.notify.CardNotifyIdmProducerMessage;
import com.mi.oa.ee.safety.infra.remote.sdk.notify.CardOpenNotifyProducerMessage;
import com.mi.oa.ee.safety.infra.repository.*;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.idm.api.enums.AccountChannelEnum;
import com.mi.oa.infra.oaucf.idm.api.enums.UserIdTypeEnum;
import com.mi.oa.infra.oaucf.idm.api.rep.DeptInfoDto;
import com.mi.oa.infra.oaucf.idm.api.rep.Resp;
import com.mi.oa.infra.oaucf.idm.api.req.CreatePersonDto;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/21 19:56
 */
@Slf4j
@Service
public class CardApplyAbilityImpl implements CardApplyAbility {

    @Resource
    private CardApplyRepository cardApplyRepository;

    @Resource
    private CountryMappingConfig countryMappingConfig;

    @Resource
    private CardOpenNotifyProducerMessage notifySdk;

    @Resource
    private CardNotifyIdmProducerMessage idmNotifySdk;

    @Resource
    private IdmSdk idmSdk;

    @Resource
    private SpaceSdk spaceSdk;

    @Resource
    private IdmRemote idmRemote;

    @Resource
    private AddressSdk addressSdk;

    @Resource
    private UcSdk ucSdk;

    @Value("${card.sync.chat-id:oc_2131a522e9a67fa8ade73df3c53f7e22}")
    String syncChatId;

    @Resource
    HrodSdk hrodSdk;

    @Resource
    CommonDoConverter commonDoConverter;

    @Resource
    private CardParkAdminRepository cardParkAdminRepository;

    @Resource(name = "avatarUrlCache")
    private Cache<String, Object> avatarUrlCache;

    @Resource
    private WorkbenchProperties workbenchProperties;

    @Value("${card.apply.dimension-code}")
    private String dimensionCode;

    @Resource
    private CardTimeValidateRepository cardTimeValidateRepository;

    @NacosValue(value = "${card.inner-land-ids:1}", autoRefreshed = true)
    private List<String> innerLandIds;

    @Resource
    private CardApplyConfigRepository cardApplyConfigRepository;

    @Override
    public void checkId(CardApplyDo cardApplyDo) {
        if (cardApplyDo.getId() != null) {
            throw new BizException(CardApplyDomainErrorCodeEnum.ID_EMPTY_ERROR);
        }
    }

    @Override
    public void checkCanSupplierUploadPhoto(CardApplyDo cardApplyDo) {
        if (StringUtils.isEmpty(cardApplyDo.getPhotoUrl())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.PHOTO_EMPTY_ERROR);
        }
        //当前不是待拍照的状态不能上传
        if (!CardApplyStatusEnum.WAIT_PHOTO.getCode().equals(cardApplyDo.getApplyStatus())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.NOT_SUPPLIER_UPLOAD_PHOTO_ERROR);
        }
    }

    @Override
    public void checkIsExist(CardApplyDo cardApplyDo, Boolean needExist) {
        CardApplyDo res = cardApplyRepository.getCardApplyByUidAndApplyType(cardApplyDo);
        if (res != null) {
            cardApplyDo.setId(res.getId());
        }
        if (needExist && ObjectUtils.isEmpty(res)) {
            throw new BizException(CardApplyDomainErrorCodeEnum.CARD_APPLY_NOT_EXIST);
        }
        if (!needExist && res != null) {
            throw new BizException(CardApplyDomainErrorCodeEnum.CARD_APPLY_REPEATED);
        }
    }

    @Override
    public void fillSpaceInfo(CardApplyDo cardApplyDo) {
        if (StringUtils.isEmpty(cardApplyDo.getUid())) {
            return;
        }
        SafetySpaceDto safetySpace = spaceSdk.getStationInfoByUid(cardApplyDo.getUid());
        if (!ObjectUtils.isEmpty(safetySpace)) {
            StringBuilder station = new StringBuilder();
            if (!StringUtils.isEmpty(safetySpace.getParkName())) {
                station.append(safetySpace.getParkName());
            }
            if (!StringUtils.isEmpty(safetySpace.getBuildingName())) {
                station.append(safetySpace.getBuildingName());
            }
            if (!StringUtils.isEmpty(safetySpace.getFloorName())) {
                station.append(safetySpace.getFloorName());
            }
            if (!StringUtils.isEmpty(safetySpace.getStationCode())) {
                station.append("--");
                station.append(safetySpace.getStationCode());
            }
            cardApplyDo.setStationCode(station.toString());
        }
    }

    @Override
    public void buildBaseInfoByPerson(CardApplyDo cardApplyDo) {
        SafetyPersonDo person = cardApplyDo.getPersonInfo();
        if (StringUtils.isEmpty(cardApplyDo.getPhone())) {
            cardApplyDo.setPhone(person.getMobile());
        }
        if (StringUtils.isEmpty(cardApplyDo.getPartnerAccount())) {
            cardApplyDo.setPartnerAccount(person.getUserName());
        }
        if (StringUtils.isNotEmpty(person.getCompanyName())) {
            cardApplyDo.setCompanyName(person.getCompanyName());
        }
        if (StringUtils.isEmpty(cardApplyDo.getEmail())) {
            cardApplyDo.setEmail(person.getEmail());
        }
        if (StringUtils.isEmpty(cardApplyDo.getNation())) {
            cardApplyDo.setNation(person.getNation());
        }
        if ((ObjectUtils.isEmpty(cardApplyDo.getStartTime()) ||
                OAUCFCommonConstants.LONG_ZERO.equals(cardApplyDo.getStartTime().toEpochSecond()))
                && !CardApplyRemarkEnum.ONSITE.getCode().equals(cardApplyDo.getRemark())) {
            cardApplyDo.setStartTime(person.getStartTime());
        }
        if ((ObjectUtils.isEmpty(cardApplyDo.getEndTime()) ||
                OAUCFCommonConstants.LONG_ZERO.equals(cardApplyDo.getEndTime().toEpochSecond()))
                && !CardApplyRemarkEnum.ONSITE.getCode().equals(cardApplyDo.getRemark())) {
            cardApplyDo.setEndTime(person.getEndTime());
        }
        if (StringUtils.isEmpty(cardApplyDo.getDisplayName())) {
            cardApplyDo.setDisplayName(person.getDisplayName());
        }
        if (StringUtils.isNotEmpty(person.getLastNameEn())) {
            cardApplyDo.setSurname(person.getLastNameEn());
        }
        if (StringUtils.isNotEmpty(person.getFirstNameEn())) {
            cardApplyDo.setPinyinName(person.getFirstNameEn());
        }
        if (StringUtils.isEmpty(cardApplyDo.getResponsible())) {
            cardApplyDo.setResponsible(person.getResponsible());
        }
        if (StringUtils.isEmpty(cardApplyDo.getResponsibleName()) && !StringUtils.isEmpty(person.getResponsible())) {
            AccountModel accountInfo = idmSdk.getAccountInfo(person.getResponsible(), UserIdTypeEnum.HAVANA_ID);
            if (!ObjectUtils.isEmpty(accountInfo)) {
                cardApplyDo.setResponsibleName(accountInfo.getFullName());
                cardApplyDo.setResponsibleAccount(accountInfo.getAccountName());
            } else {
                cardApplyDo.setResponsibleName("--");
                cardApplyDo.setResponsibleAccount("--");
            }
        }
        if (ObjectUtils.isEmpty(cardApplyDo.getIdNumber())) {
            cardApplyDo.setIdNumber(person.getIdNumber());
        }
        cardApplyDo.setIdNumberType(IdNumberTypeEnum.IO_CARD.getCode());
    }

    @Override
    public CardApplyDo getCardApplyById(Long id) {
        return cardApplyRepository.getCardApplyById(id);
    }

    @Override
    public void fillFullCardApply(CardApplyDo cardApplyDo) {
        if (!ObjectUtils.isEmpty(cardApplyDo)) {
            buildBaseInfoByPerson(cardApplyDo);
            fillSpaceInfo(cardApplyDo);
            fillDeptInfo(cardApplyDo);
        }
    }

    @Override
    public String createIdm(CardApplyDo cardApplyDo) {
        return idmSdk.createPerson(cardApplyDo);
    }

    @Override
    public void updateWithCardTimeById(CardApplyDo cardApplyDo) {
        cardApplyRepository.updateWithCardTimeById(cardApplyDo);
    }

    @Override
    public PageModel<CardApplyDo> pageConditionApplyList(CardApplyDo cardApplyDo, Long pageNum, Long pageSize, List<String> parkCodes) {
        return cardApplyRepository.pageList(cardApplyDo, pageNum, pageSize, parkCodes);
    }

    @Override
    public PageModel<CardApplyDo> pageConditionApplyList(CardApplyQuery cardApplyQuery) {
        return cardApplyRepository.pageList(cardApplyQuery);
    }

    @Override
    public void fillBatchImport(List<CardApplyDo> cardApplyDos, Boolean isHistoryCard) {
        cardApplyDos.stream().parallel().forEach(cardApplyDo -> {
            if (isHistoryCard) {
                AccountModel partnerInfo = idmSdk.getAccountInfo(cardApplyDo.getPartnerAccount(),
                        UserIdTypeEnum.ACCOUNT_NAME);
                if (!ObjectUtils.isEmpty(partnerInfo)) {
                    cardApplyDo.setResponsibleAccount(partnerInfo.getAccountOwner());
                    cardApplyDo.setUid(partnerInfo.getUid());
                    if (!StringUtils.isEmpty(partnerInfo.getMobile())) {
                        cardApplyDo.setPhone(CodeUtils.encryptOrDecryptPhone(partnerInfo.getMobile(), true));
                    }
                    cardApplyDo.setEmpType(AccountTypeEnum.PARTNER.getValue());
                    cardApplyDo.setNation("{{{China}}}");
                    cardApplyDo.setSurname(partnerInfo.getSurname());
                    cardApplyDo.setPinyinName(partnerInfo.getGivenName());
                    cardApplyDo.setApplyStatus(CardApplyStatusEnum.COMPLETED.getCode());
                    cardApplyDo.setRemark("{{{历史合作卡批量导入创建申请单}}}");
                    cardApplyDo.setStartTime(SafetyConstants.Card.DEFAULT_START_TIME);
                    cardApplyDo.setEndTime(SafetyConstants.Card.DEFAULT_END_TIME);
                    cardApplyDo.setApplyType(CardTypeEnum.COOPERATION_CARD.getNumber());
                }
            }
            //填充园区编码
            if (!StringUtils.isEmpty(cardApplyDo.getParkName()) && StringUtils.isEmpty(cardApplyDo.getParkCode())) {
                List<SafetySpaceParkDto> spaceParks = spaceSdk.getParkCodesByFuzzyName(cardApplyDo.getParkName());
                if (CollectionUtils.isNotEmpty(spaceParks)) {
                    cardApplyDo.setParkCode(spaceParks.get(0).getParkCode());
                }
            }
            //填充责任人信息
            AccountModel accountInfo = idmSdk.getAccountInfo(cardApplyDo.getResponsibleAccount(), UserIdTypeEnum.ACCOUNT_NAME);
            if (!ObjectUtils.isEmpty(accountInfo)) {
                cardApplyDo.setResponsible(accountInfo.getUid());
                cardApplyDo.setResponsibleName(accountInfo.getFullName());
                if (isHistoryCard) {
                    fillDeptInfo(cardApplyDo);
                }
            } else {
                throw new BizException(CardApplyDomainErrorCodeEnum.RESPONSIBLE_NOT_EXIST_ERROR);
            }
        });
    }

    @Override
    public void uploadPhoto(Long id, String photoUrl) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(id);
        cardApplyDo.setPhotoUrl(photoUrl);
        cardApplyRepository.updateWithCardTimeById(cardApplyDo);
    }

    @Override
    public void operateApproval(List<Long> ids) {
        cardApplyRepository.updateStatus(ids, CardApplyStatusEnum.WAIT_PRINT_CARD.getCode());
    }

    @Override
    public void operateReject(List<Long> ids) {
        cardApplyRepository.updateStatus(ids, CardApplyStatusEnum.REFUSED.getCode());
        List<CardApplyDo> applyDoList = cardApplyRepository.findByIdList(ids);
        if (CollectionUtils.isNotEmpty(applyDoList)) {
            applyDoList.forEach(item -> {
                //驻场 拒绝后需删除有效期
                if (CardApplyRemarkEnum.ONSITE.getCode().equals(item.getRemark())) {
                    CardTimeValidityDo cardTimeValidityDo = new CardTimeValidityDo();
                    cardTimeValidityDo.setCardApplyId(item.getId());
                    cardTimeValidateRepository.deleteTimeByApplyId(cardTimeValidityDo);
                }
            });
        }
    }

    @Override
    public void notifyOnsiteMessage(List<Long> ids, CardApplyStatusEnum cardApplyStatusEnum) {
        ids.stream().parallel().forEach(id -> {
            CardOpenMessage cardOpenMessage = new CardOpenMessage();
            cardOpenMessage.setCardApplyStatusEnum(cardApplyStatusEnum);
            cardOpenMessage.setId(id);
            notifySdk.produceMessage(cardOpenMessage, String.valueOf(id));
        });
    }

    @Override
    public void operatePrint(List<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            cardApplyRepository.updateStatus(ids, CardApplyStatusEnum.WAIT_OPEN_CARD.getCode());
        }
    }

    @Override
    public void operateOpen(List<Long> ids) {
        cardApplyRepository.updateStatus(ids, CardApplyStatusEnum.WAIT_RECEIVE.getCode());
    }

    @Override
    public void operateReceive(List<Long> ids) {
        cardApplyRepository.updateStatus(ids, CardApplyStatusEnum.COMPLETED.getCode());
    }

    @Override
    public List<CardApplyDo> getCardApplyByIds(List<Long> ids) {
        return cardApplyRepository.getBatchApply(ids);
    }

    @Override
    public void checkPhotoUrl(CardApplyDo cardApplyDo) {
        //国内的数据，并且没有照片，则不允许打印
        if ((StringUtils.isEmpty(cardApplyDo.getZoneCode()) ||
                ZonedNumberEnum.CHINA_MAINLAND.getCode().equals(cardApplyDo.getZoneCode())) &&
                StringUtils.isEmpty(cardApplyDo.getPhotoUrl()) && CardApplyTypeEnum.EMPLOYEE_CARD_APPLY.getCode().equals(cardApplyDo.getApplyType())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.BATCH_PHOTO_URL_EMPTY);
        }
    }

    @Override
    public void fillDeptInfo(CardApplyDo cardApplyDo) {
        // 合作卡或者是物业卡，责任人是uid
        String uid = (CardTypeEnum.COOPERATION_CARD.getNumber().equals(cardApplyDo.getApplyType())
                || CardTypeEnum.PROPERTY_CARD_APPLY.getNumber().equals(cardApplyDo.getApplyType()))
                ? cardApplyDo.getResponsible() : cardApplyDo.getUid();
        if (StringUtils.isEmpty(uid)) {
            return;
        }
        Resp<List<DeptInfoDto>> resp = idmSdk.getDeptInfoByUid(uid);
        List<DeptInfoDto> deptInfos = resp.getData();
        if (CollectionUtils.isNotEmpty(deptInfos)) {
            for (DeptInfoDto deptInfoDto : deptInfos) {
                if (DeptLevelEnum.FIRST_DEPT.getLevel().equals(deptInfoDto.getLevel())) {
                    cardApplyDo.setFirstDeptIdName(deptInfoDto.getDeptName());
                    cardApplyDo.setFirstDeptId(deptInfoDto.getDeptId());
                } else if (DeptLevelEnum.SECOND_DEPT.getLevel().equals(deptInfoDto.getLevel())) {
                    cardApplyDo.setSecondDeptIdName(deptInfoDto.getDeptName());
                    cardApplyDo.setSecondDeptId(deptInfoDto.getDeptId());
                } else if (DeptLevelEnum.THIRD_DEPT.getLevel().equals(deptInfoDto.getLevel())) {
                    cardApplyDo.setThirdDeptIdName(deptInfoDto.getDeptName());
                    cardApplyDo.setThirdDeptId(deptInfoDto.getDeptId());
                } else if (DeptLevelEnum.FOURTH_DEPT.getLevel().equals(deptInfoDto.getLevel())) {
                    cardApplyDo.setFourthDeptId(deptInfoDto.getDeptId());
                    cardApplyDo.setFourthDeptIdName(deptInfoDto.getDeptName());
                }
            }
        }
    }

    @Override
    public String getReceiptAddress(String parkCode) {
        return cardApplyRepository.getReceiptAddress(parkCode);
    }

    @Override
    public CardApplyDo getApplyByUidAndType(CardApplyDo cardApplyDo) {
        return cardApplyRepository.getCardApplyByUidAndApplyType(cardApplyDo);
    }

    @Override
    public List<CardApplyDo> getApplyByFuzzyName(String name) {
        return cardApplyRepository.getApplyByFuzzyName(name);
    }

    @Override
    public List<CardApplyDo> getListByParkCodes(List<String> resourceCodes) {
        return cardApplyRepository.getListByParkCodes(resourceCodes);
    }

    @Override
    public void fillParkInfo(CardApplyDo cardApplyDo) {
        String parkName = "", receiptParkName = "";
        if (!StringUtils.isEmpty(cardApplyDo.getParkCode())) {
            //提示访客系统需要提示的信息
            SafetySpaceParkDto spacePark = spaceSdk.getParkByCode(cardApplyDo.getParkCode());
            if (spacePark != null) {
                parkName = spacePark.getParkName();
            }
        }
        cardApplyDo.setParkName(parkName);

        if (!StringUtils.isEmpty(cardApplyDo.getReceiptParkCode())) {
            //提示访客系统需要提示的信息
            SafetySpaceParkDto spacePark = spaceSdk.getParkByCode(cardApplyDo.getReceiptParkCode());
            if (spacePark != null) {
                receiptParkName = spacePark.getParkName();
            }
        }
        cardApplyDo.setReceiptParkName(receiptParkName);
        if (Objects.nonNull(cardApplyDo.getCityId()) && !OAUCFCommonConstants.STR_ZERO.equals(cardApplyDo.getCityId())) {
            AddressInfoDto addressInfoDto = addressSdk.getAddressById(Integer.valueOf(cardApplyDo.getCityId()));
            Optional.ofNullable(addressInfoDto).ifPresent(item -> cardApplyDo.setCityName(addressInfoDto.getAddrName()));
        }
    }

    @Override
    public List<String> getParkCodesByUc(Boolean isCheck) {
        if (!isCheck) {
            return null;
        }
        String loginAccount = idmRemote.getLoginAccount().getValue();
        AuthorityRemoteDto accountAuth = ucSdk.getAccountAuth(loginAccount, "INTRA", workbenchProperties.getCode());
        //默认有一个编码，匹配不上查询为空
        List<String> parkCodes = Lists.newArrayList("nothing");
        if (CollectionUtils.isNotEmpty(accountAuth.getResourceCodes())) {
            parkCodes.addAll(accountAuth.getResourceCodes());
        }
        //超管角色 查所有
        if (CollectionUtils.isNotEmpty(accountAuth.getRoleCodeList()) && accountAuth.getRoleCodeList().contains(
                SafetyConstants.Card.CARD_SUPER_ADMIN_ROLE_CODE)) {
            parkCodes = null;
        }
        return parkCodes;
    }

    @Override
    public List<SafetySpaceParkDto> listSpaceByParkCodes(List<String> resourceCodes) {
        List<SafetySpaceParkDto> parkDtos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(resourceCodes)) {
            resourceCodes.forEach(parkCode -> {
                SafetySpaceParkDto spacePark = spaceSdk.getParkByCode(parkCode);
                if (spacePark != null) {
                    parkDtos.add(spacePark);
                }
            });
        }
        return parkDtos;
    }

    @Override
    public Integer checkParkCodeExist(String parkCode) {
        String loginAccount = idmRemote.getLoginAccount().getValue();
        AuthorityRemoteDto accountAuth = ucSdk.getAccountAuth(loginAccount, "INTRA", workbenchProperties.getCode());
        if (CollectionUtils.isNotEmpty(accountAuth.getRoleCodeList()) && accountAuth.getRoleCodeList().contains(
                SafetyConstants.Card.CARD_SUPER_ADMIN_ROLE_CODE)) {
            return OAUCFCommonConstants.INT_ONE;
        }
        if (CollectionUtils.isNotEmpty(accountAuth.getResourceCodes()) && accountAuth.getResourceCodes().contains(parkCode)) {
            return OAUCFCommonConstants.INT_ONE;
        }
        return OAUCFCommonConstants.INT_ZERO;
    }

    @Override
    public void fillTempApplyBaseInfo(CardApplyDo cardApplyDo) {
        cardApplyDo.setApplyType(CardTypeEnum.TEMP_CARD.getNumber());
        Boolean isSync = (Boolean) cardApplyDo.getExtField("isSync");
        if (!ObjectUtils.isEmpty(isSync) && isSync) {
            cardApplyDo.setApplyStatus(CardApplyStatusEnum.COMPLETED.getCode());
            cardApplyDo.setStartTime(ZonedDateTimeUtils.getZonedDateTimeBegin(ZonedDateTime.now()));
        } else {
            cardApplyDo.setApplyStatus(CardApplyStatusEnum.WAIT_OPEN_CARD.getCode());
        }
        cardApplyDo.setEmpType(cardApplyDo.getPersonInfo().getAccountType().getValue());
    }

    @Override
    public void checkEmpTypeIsCanCreateApply(CardApplyDo cardApplyDo) {
        if (AccountTypeEnum.PARTNER.equals(cardApplyDo.getPersonInfo().getAccountType())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.NOT_EMP);
        }
    }

    @Override
    public void checkApplyIsExistBySameUidAndType(CardApplyDo cardApplyDo) {
        if (ObjectUtils.isEmpty(cardApplyDo.getApplyType())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.CARD_APPLY_TYPE_NOT_FOUND);
        }
        if (StringUtils.isEmpty(cardApplyDo.getUid())) {
            cardApplyDo.setUid(cardApplyDo.getPersonInfo().getUid());
        }
        CardApplyDo oldCardApply = cardApplyRepository.getCardApplyByUidAndApplyType(cardApplyDo);
        //同一类型 同一个人在系统只能有一条数据 若已存在则不让创建
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(oldCardApply)) {
            throw new BizException(CardApplyDomainErrorCodeEnum.CARD_APPLY_REPEATED);
        }
    }

    @Override
    public void checkCardApplyIsRepeat(CardApplyDo cardApplyDo) {
        if (Objects.nonNull(cardApplyDo.getId())
                && !CardApplyStatusEnum.REFUSED.getCode().equals(cardApplyDo.getApplyStatus())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.CARD_APPLY_REPEATED);
        }
    }

    @Override
    public CardApplyDo getCardApplyByUid(String uid) {
        return cardApplyRepository.getCardApplyByUid(uid);
    }

    @Override
    public void fillCardApplyByUidAndTypes(CardApplyDo cardApplyDo) {
        if (StringUtils.isEmpty(cardApplyDo.getUid())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.UID_EMPTY_ERROR);
        }
        List<Integer> applyTypes = (List<Integer>) cardApplyDo.getExtField("applyTypes");
        if (CollectionUtils.isEmpty(applyTypes)) {
            throw new BizException(CardApplyDomainErrorCodeEnum.CARD_APPLY_TYPE_NOT_FOUND);
        }
        CardApplyDo nowApply = cardApplyRepository.findCardByUidAndCardTypes(cardApplyDo.getUid(), applyTypes);
        commonDoConverter.copyCardApplyDo(nowApply, cardApplyDo);
    }

    @Override
    public void refreshAvatarUrl(PhotoEditDo photoEditDo) {
        if (StringUtils.isEmpty(photoEditDo.getUid())) {
            return;
        }
        //更新redis缓存
        CardApplyDo cardApplyDo = cardApplyRepository.findAvatarUrlByUid(photoEditDo.getUid());
        cardApplyDo.setPhotoUrl(photoEditDo.getPhotoUrl());
        RedisUtils.setEx(photoEditDo.getUid(), cardApplyDo, 1L, TimeUnit.DAYS);
        //更新本地缓存
        try {
            if (StringUtils.isNotEmpty(photoEditDo.getPhotoUrl())) {
                avatarUrlCache.put(photoEditDo.getUid(), photoEditDo.getPhotoUrl());
            } else {
                avatarUrlCache.put(photoEditDo.getUid(), "default");
            }
        } catch (Exception e) {
            log.error("Failed to update avatar URL in database for uid: {}", photoEditDo.getUid());
        }
    }

    @Override
    public void checkIsInternationalWhiteList(CardApplyDo cardApplyDo) {
        if (StringUtils.isEmpty(cardApplyDo.getCountryId())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.COUNTRY_ID_EMPTY);
        }
        //非国际门禁白名单不让创建
        Boolean isAccountActive = (Boolean) cardApplyDo.getExtField("isAccountActive");
        if (Objects.nonNull(isAccountActive) && isAccountActive) {
            CountryMappingDto countryMappingDto = countryMappingConfig.findByCountryId(cardApplyDo.getCountryId());
            if (Objects.isNull(countryMappingDto)) {
                throw new BizException(CardApplyDomainErrorCodeEnum.NOT_NATIONALITY_WHITE_LIST_ERROR);
            }
        }
    }

    @Override
    public void checkIsMiHomeDept(CardApplyDo cardApplyDo) {
        Boolean isAccountActive = (Boolean) cardApplyDo.getExtField("isAccountActive");
        if (Objects.nonNull(isAccountActive) && isAccountActive) {
            if (SafetyConstants.Card.MI_HOME_CODE.equals(cardApplyDo.getPersonInfo().getThirdDeptId())) {
                throw new BizException(CardApplyDomainErrorCodeEnum.MI_HOME_NOT_NEED_CARD);
            }
        }
    }

    @Override
    public Map<String, String> checkUcControl() {
        String loginAccount = idmRemote.getLoginAccount().getValue();
        AuthorityRemoteDto accountAuth = ucSdk.getAccountAuth(loginAccount, "INTRA", workbenchProperties.getCode());

        // 移除默认角色 "__builtin.all-members"
        if (Objects.nonNull(accountAuth) && CollectionUtils.isNotEmpty(accountAuth.getRoleCodeList())) {
            accountAuth.getRoleCodeList().removeIf(SafetyConstants.Card.DEFAULT_ROLE_CODE::equals);
        }
        // 超管不走下面验证权限的流程
        if (Objects.nonNull(accountAuth) && CollectionUtils.isNotEmpty(accountAuth.getRoleCodeList())
                && accountAuth.getRoleCodeList().contains(SafetyConstants.Card.CARD_SUPER_ADMIN_ROLE_CODE)) {
            Map<String, String> result = new HashMap<>();
            result.put("hasUcControl", Boolean.toString(false));
            return result;
        }
        Map<String, String> result = new HashMap<>();
        // 权限接入uc
        result.put("hasUcControl", Boolean.toString(true));
        // 获取维度代码列表 没有权限 就查不到
        if (Objects.isNull(accountAuth) || MapUtils.isEmpty(accountAuth.getDataMap())) {
            List<String> unDefined = new ArrayList<>();
            unDefined.add("UNDEFINED");
            result.put("resourceCodes", JsonUtils.toJson(unDefined));
            return result;
        }
        List<String> dimensionCodes = accountAuth.getDataMap().get(dimensionCode);
        // 空数组
        result.put("resourceCodes", dimensionCodes != null ? JsonUtils.toJson(dimensionCodes) : "[]");

        return result;
    }

    @Override
    public void notifyIdmMessage(CardApplyDo cardApplyDo) {
        Map<String, Object> message = buildIdmNotify(cardApplyDo);
        idmNotifySdk.produceMessage(message, (String) message.get("userName"));
    }

    private Map<String, Object> buildIdmNotify(CardApplyDo cardApplyDo) {
        CardInfoDo cardInfo = cardApplyDo.getCardInfo();
        String operateType = (String) cardApplyDo.getExtField("operateType");
        Map<String, Object> bodyMap = Maps.newHashMap();
        bodyMap.put("operateType", operateType);
        if (StringUtils.isNotEmpty(cardApplyDo.getPartnerAccount())) {
            bodyMap.put("userName", cardApplyDo.getPartnerAccount());
        } else {
            bodyMap.put("userName", cardApplyDo.getUid());
        }
        bodyMap.put("cardPhysical", cardInfo.getMediumPhysicsCode());
        bodyMap.put("cardEncrypted", cardInfo.getMediumEncryptCode());
        bodyMap.put("timeStamp", String.valueOf(System.currentTimeMillis()));
        return bodyMap;
    }

    @Override
    public List<CardApplyDo> getPhotoByPhoneLastFour(String phoneLastFour) {
        return cardApplyRepository.getPhotoByPhoneLastFour(phoneLastFour);
    }

    @Override
    public void fillOfficeAddress(CardApplyDo cardApplyDo) {
        Boolean isAccountActive = (Boolean) cardApplyDo.getExtField("isAccountActive");
        if (StringUtils.isNotEmpty(cardApplyDo.getParkCode())) {
            SafetySpaceParkDto spaceParkDto = spaceSdk.getParkByCode(cardApplyDo.getParkCode());
            if (spaceParkDto != null) {
                cardApplyDo.setCityId(spaceParkDto.getCityId());
                cardApplyDo.setCountryId(String.valueOf(spaceParkDto.getCountryId()));
                cardApplyDo.setParkCode(spaceParkDto.getParkCode());
            }
        } else {
            SafetyPersonDo personInfo = cardApplyDo.getPersonInfo();
            CardPsParkAdminDo cardPsParkAdminDo = cardParkAdminRepository.findOneByReportAddressId(personInfo.getReportAddressId());
            if (ObjectUtils.isEmpty(cardPsParkAdminDo) || StringUtils.isEmpty(cardPsParkAdminDo.getCountryId())
                    || SafetyConstants.Card.INVALID_COUNTRY_CODE.equals(cardPsParkAdminDo.getCountryId())) {
                String countryCode = personInfo.getOfficeNationalityCode();
                if (StringUtils.isEmpty(countryCode)) {
                    String cityCode = hrodSdk.getLocationHireByReportAddress(personInfo.getReportAddressId());
                    PersonInfoModel personWithCountry = hrodSdk.getLocationCityByCityCode(cityCode);
                    countryCode = personWithCountry.getExpandInfo().getOfficeNationalityCode();
                }
                if (Objects.nonNull(isAccountActive) && isAccountActive) {
                    CountryMappingDto countryMappingDto = countryMappingConfig.findByCountryCode(countryCode);
                    if (Objects.nonNull(countryMappingDto)) {
                        cardApplyDo.setCountryId(countryMappingDto.getCountryId());
                    }
                } else {
                    CountryMappingDto byCountryCodeV2 = countryMappingConfig.findByCountryCodeV2(countryCode);
                    if (Objects.nonNull(byCountryCodeV2)) {
                        cardApplyDo.setCountryId(byCountryCodeV2.getCountryId());
                    }
                }
                log.warn("{} not find park by report address id:{}", personInfo.getUserName(), personInfo.getReportAddressId());
            } else {
                cardApplyDo.setCityId(cardPsParkAdminDo.getCityId());
                cardApplyDo.setCountryId(String.valueOf(cardPsParkAdminDo.getCountryId()));
                cardApplyDo.setParkCode(cardPsParkAdminDo.getParkCode());
                cardApplyDo.getPersonInfo().setLocationCode(cardPsParkAdminDo.getWorkCity());
            }
        }

    }

    @Override
    public void fillEmpApplyInfo(CardApplyDo cardApplyDo) {
        cardApplyDo.setApplyType(CardTypeEnum.EMPLOYEE_CARD.getNumber());
        Boolean isSync = (Boolean) cardApplyDo.getExtField("isSync");
        Boolean isAccountActive = (Boolean) cardApplyDo.getExtField("isAccountActive");
        SafetyPersonDo safetyPersonDo = cardApplyDo.getPersonInfo();
        cardApplyDo.setEmpType(safetyPersonDo.getAccountType().getValue());
        cardApplyDo.setStartTime(ZonedDateTimeUtils.getZonedDateTimeBegin(ZonedDateTime.now()));
        cardApplyDo.setEndTime(SafetyConstants.Card.DEFAULT_END_TIME);

        //数据同步状态设置已完成
        if (!ObjectUtils.isEmpty(isSync) && isSync) {
            cardApplyDo.setApplyStatus(CardApplyStatusEnum.COMPLETED.getCode());
            return;
        }
        //园区编码不为空
        if (StringUtils.isNotEmpty(cardApplyDo.getParkCode())) {
            CardApplyConfigDo cardApplyConfigDo = cardApplyConfigRepository.findByParkCodeAndEmpType(cardApplyDo.getParkCode(),
                    AccountTypeEnum.getEnumByCode(cardApplyDo.getEmpType()).getDesc());
            //正式或外包走兜底
            if (Objects.isNull(cardApplyConfigDo) && (AccountTypeEnum.EMPLOYEE.getValue().equals(cardApplyDo.getEmpType())
                    || AccountTypeEnum.VENDOR.getValue().equals(cardApplyDo.getEmpType()))) {
                initialApplyStatus(cardApplyDo);
            } else if (Objects.nonNull(cardApplyConfigDo)) {
                cardApplyDo.setApplyStatus(cardApplyConfigDo.getApplyInitialStatus());
                if (StringUtils.isBlank(cardApplyDo.getCountryId())) {
                    cardApplyDo.setCountryId(cardApplyConfigDo.getCountryId());
                }
            } else {
                throw new BizException(CardApplyDomainErrorCodeEnum.EMP_TYPE_NOT_NEED_EMP_CARD);
            }
        } else if (AccountTypeEnum.EMPLOYEE.getValue().equals(cardApplyDo.getEmpType())
                || AccountTypeEnum.VENDOR.getValue().equals(cardApplyDo.getEmpType())) {
            initialApplyStatus(cardApplyDo);
        } else {
            throw new BizException(CardApplyDomainErrorCodeEnum.EMP_TYPE_NOT_NEED_EMP_CARD);
        }
    }

    private void initialApplyStatus(CardApplyDo cardApplyDo) {
        //兜底
        if (StringUtils.isNotEmpty(cardApplyDo.getCountryId()) && !innerLandIds.contains(cardApplyDo.getCountryId())) {
            cardApplyDo.setApplyStatus(CardApplyStatusEnum.WAIT_OPEN_CARD.getCode());
        } else {
            //兜底待上传
            cardApplyDo.setApplyStatus(CardApplyStatusEnum.WAIT_UPLOAD.getCode());
        }
    }

    private void fillParkInfo(CardApplyDo cardApplyDo, SafetySpaceParkDto parkDto, SafetySpaceParkDto receiptParkDto) {
        Optional.ofNullable(parkDto).ifPresent(item -> cardApplyDo.setParkName(item.getParkName()));
        Optional.ofNullable(receiptParkDto).ifPresent(item -> cardApplyDo.setReceiptParkName(item.getParkName()));
        AddressInfoDto addressInfoDto = addressSdk.getAddressById(Integer.valueOf(cardApplyDo.getCityId()));
        Optional.ofNullable(addressInfoDto).ifPresent(item -> cardApplyDo.setCityName(addressInfoDto.getAddrName()));
    }

    @Override
    public void fillParkInfoBatch(List<CardApplyDo> applyDos) {
        List<SafetySpaceParkDto> parkDtoList = new ArrayList<>();
        try {
            //获取所有园区信息
            parkDtoList = spaceSdk.getAllParkBuildingFloorTree();        //园区编码对应的园区信息
            Map<String, SafetySpaceParkDto> parkDtoMap = parkDtoList.stream()
                    .collect(Collectors.toMap(SafetySpaceParkDto::getParkCode, Function.identity(), (a, b) -> a));
            applyDos.forEach(item ->
                    fillParkInfo(item, parkDtoMap.get(item.getParkCode()), parkDtoMap.get(item.getReceiptParkCode())));
        } catch (Exception e) {
            log.error("get park info error");
        }
    }

    @Override
    public void fillCardApplyDoById(CardApplyDo cardApplyDo) {
        if (cardApplyDo.getId() == null) {
            throw new BizException(CardApplyDomainErrorCodeEnum.ID_EMPTY_ERROR);
        }
        CardApplyDo nowApplyDo = cardApplyRepository.findById(cardApplyDo.getId());
        if (nowApplyDo == null) {
            throw new BizException(CardApplyDomainErrorCodeEnum.CARD_APPLY_NOT_EXIST);
        }
        commonDoConverter.copyCardApplyDo(nowApplyDo, cardApplyDo);
    }

    @Override
    public void updateCardApply(CardApplyDo cardApplyDo) {
        cardApplyRepository.updateWithCardTimeById(cardApplyDo);
    }

    @Override
    public List<CardApplyDo> findByIdList(List<Long> applyIdList) {
        return cardApplyRepository.findByIdList(applyIdList);
    }

    @Override
    public void checkEnableUpload(CardApplyDo cardApplyDo) {
        if (ObjectUtils.isEmpty(cardApplyDo)) {
            throw new BizException(CardApplyDomainErrorCodeEnum.CARD_APPLY_NOT_EXIST);
        }
        //只有待拍照和待上传的支持自行上传和重发消息
        if (!CardApplyStatusEnum.WAIT_PHOTO.getCode().equals(cardApplyDo.getApplyStatus())
                && !CardApplyStatusEnum.WAIT_UPLOAD.getCode().equals(cardApplyDo.getApplyStatus())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.NOT_WAIT_PHOTO_STATUS);
        }
    }

    @Override
    public void checkOnEditCardApply(CardApplyDo cardApplyDo) {
        if (CardApplyStatusEnum.WAIT_OPEN_CARD.getCode().equals(cardApplyDo.getApplyStatus())
                && CardApplyStatusEnum.WAIT_RECEIVE.getCode().equals(cardApplyDo.getApplyStatus())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.CARD_APPLY_EDIT_STATUS_NOT_SUPPORT);
        }
    }

    @Override
    public CardApplyDo findCardByUidAndCardType(String uid, CardTypeEnum cardTypeEnum) {
        return cardApplyRepository.findCardByUidAndCardType(uid, cardTypeEnum);
    }


    @Override
    public void checkIsEmpty(CardApplyDo cardApplyDo) {
        if (ObjectUtils.isEmpty(cardApplyDo)) {
            throw new BizException(CardApplyDomainErrorCodeEnum.CARD_APPLY_NOT_EXIST);
        }
    }

    @Override
    public void checkIsExpire(CardApplyDo cardApplyDo) {
        ZonedDateTime now = ZonedDateTime.now();
        if (!OAUCFCommonConstants.LONG_ZERO.equals(cardApplyDo.getStartTime().toEpochSecond()) &&
                !OAUCFCommonConstants.LONG_ZERO.equals(cardApplyDo.getEndTime().toEpochSecond()) &&
                ZonedDateTimeUtils.compare(now, cardApplyDo.getEndTime()) > OAUCFCommonConstants.INT_ZERO) {
            throw new BizException(CardApplyDomainErrorCodeEnum.CARD_APPLY_EXPIRE);
        }
    }

    @Override
    public void checkValidateTimeBeforeUpdate(CardApplyDo cardApplyDo) {
        CardApplyDo oldCardApply = cardApplyRepository.getCardApplyById(cardApplyDo.getId());
        ZonedDateTime now = ZonedDateTime.now();
        //原先时间在有效期范围内 申请单时间不更新
        if (ZonedDateTimeUtils.compare(now, oldCardApply.getStartTime()) >= OAUCFCommonConstants.INT_ZERO &&
                ZonedDateTimeUtils.compare(now, oldCardApply.getEndTime()) < OAUCFCommonConstants.INT_ZERO) {
            cardApplyDo.setStartTime(oldCardApply.getStartTime());
            cardApplyDo.setEndTime(oldCardApply.getEndTime());
        }
    }

    @Override
    public Boolean judgeIsNeedUpdateIdm(CardApplyDo cardApplyDo) {
        return StringUtils.isNotEmpty(cardApplyDo.getIdNumber()) && StringUtils.isNotEmpty(cardApplyDo.getUid());
    }

    @Override
    public void checkValidateTimeIsExceedIdmTime(CardApplyDo cardApplyDo) {
        if (!ObjectUtils.isEmpty(cardApplyDo.getPersonInfo()) &&
                !ObjectUtils.isEmpty(cardApplyDo.getPersonInfo().getStartTime()) &&
                !ObjectUtils.isEmpty(cardApplyDo.getPersonInfo().getEndTime())) {
            ZonedDateTime startTime = cardApplyDo.getStartTime().plusDays(1L);
            ZonedDateTime endTime = cardApplyDo.getEndTime().minusDays(1L);
            if (ZonedDateTimeUtils.compare(startTime,
                    cardApplyDo.getPersonInfo().getStartTime()) < OAUCFCommonConstants.INT_ZERO ||
                    ZonedDateTimeUtils.compare(startTime, cardApplyDo.getPersonInfo().getEndTime()) > OAUCFCommonConstants.INT_ZERO ||
                    ZonedDateTimeUtils.compare(endTime, cardApplyDo.getPersonInfo().getStartTime()) < OAUCFCommonConstants.INT_ZERO ||
                    ZonedDateTimeUtils.compare(endTime, cardApplyDo.getPersonInfo().getEndTime()) > OAUCFCommonConstants.INT_ZERO) {
                throw new BizException(CardApplyErrorCodeEnum.VALIDATE_TIME_OUT_RANGE);
            }
        } else {
            log.info("current partner not have validate time: {}", cardApplyDo.getPersonInfo());
        }
    }

    @Override
    public void fillBaseInfoByUidAndApplyType(CardApplyDo cardApplyDo) {
        if (StringUtils.isEmpty(cardApplyDo.getUid())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.UID_EMPTY_ERROR);
        }

        if (cardApplyDo.getApplyType() == null) {
            throw new BizException(CardApplyDomainErrorCodeEnum.CARD_APPLY_TYPE_NOT_FOUND);
        }

        CardApplyDo nowCardApplyDo = cardApplyRepository.getCardApplyByUidAndApplyType(cardApplyDo);
        if (nowCardApplyDo != null) {
            commonDoConverter.copyCardApplyDo(nowCardApplyDo, cardApplyDo);
        }
    }

    @Override
    public void checkCompleted(CardApplyDo cardApplyDo) {

        if (StringUtils.isEmpty(cardApplyDo.getPhone())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.PHONE_EMPTY);
        }
        if (StringUtils.isEmpty(cardApplyDo.getSurname())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.SURNAME_EMPTY);
        }
        if (StringUtils.isEmpty(cardApplyDo.getDisplayName())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.NAME_EMPTY);
        }
        if (StringUtils.isEmpty(cardApplyDo.getPinyinName())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.PIN_YIN_NAME_EMPTY);
        }
        if (StringUtils.isEmpty(cardApplyDo.getNation())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.NATION_EMPTY);
        }
        if (StringUtils.isEmpty(cardApplyDo.getCompanyName())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.COMPANY_NAME_EMPTY);
        }
        if (StringUtils.isEmpty(cardApplyDo.getResponsible())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.RESPONSIBLE_EMPTY);
        }
    }

    @Override
    public void checkNameLength(CardApplyDo cardApplyDo) {
        if (StringUtils.isNotEmpty(cardApplyDo.getPinyinName()) &&
                cardApplyDo.getPinyinName().length() > SafetyConstants.Card.NAME_LENGTH) {
            throw new BizException(CardApplyErrorCodeEnum.NAME_LENGTH_ERROR);
        }
        if (StringUtils.isNotEmpty(cardApplyDo.getSurname()) &&
                cardApplyDo.getSurname().length() > SafetyConstants.Card.NAME_LENGTH) {
            throw new BizException(CardApplyErrorCodeEnum.NAME_LENGTH_ERROR);
        }
    }

    @Override
    public void checkZoneCode(CardApplyDo cardApplyDo) {
        //没有地区编码的时候不做校验
        if (StringUtils.isEmpty(cardApplyDo.getZoneCode())) {
            return;
        }
        List<SafetyZoneDto> safetyZoneDtoList = idmSdk.getZoneCodeListFromLocalCache();
        if (CollectionUtils.isNotEmpty(safetyZoneDtoList)) {
            Set<String> zoneCodeList = safetyZoneDtoList.stream().map(SafetyZoneDto::getZoneCode).collect(Collectors.toSet());
            if (!zoneCodeList.contains(cardApplyDo.getZoneCode())) {
                throw new BizException(CardApplyErrorCodeEnum.APPLY_ZONE_CODE_NOT_EXIST);
            }
        }
    }

    @Override
    public List<CardApplyDo> findAvatarUrlByUidList(List<String> uidList) {
        List<CardApplyDo> applyDoList = Lists.newArrayList();
        String avatarUrl = "";
        for (String uid : uidList) {
            if (StringUtils.isNotEmpty(uid)) {
                try {
                    avatarUrl = (String) avatarUrlCache.get(uid, () -> loadAvatarUrlByUid(uid));
                } catch (Exception e) {
                    log.error("uid:{} get avatar url from local cache failed:{}", uid, e.toString());
                } finally {
                    if (StringUtils.isNotEmpty(avatarUrl)) {
                        avatarUrlCache.put(uid, avatarUrl);
                    } else {
                        avatarUrlCache.put(uid, "default");
                    }
                    CardApplyDo cardApplyDo = new CardApplyDo();
                    cardApplyDo.setUid(uid);
                    avatarUrl = "default".equals(avatarUrl) ? "" : avatarUrl;
                    cardApplyDo.setPhotoUrl(avatarUrl);
                    applyDoList.add(cardApplyDo);
                }
            }
        }
        return applyDoList;
    }

    private String loadAvatarUrlByUid(String uid) {
        CardApplyDo cardApplyDo;
        cardApplyDo = (CardApplyDo) RedisUtils.get(uid);
        if (ObjectUtils.isEmpty(cardApplyDo)) {
            cardApplyDo = cardApplyRepository.findAvatarUrlByUid(uid);
            if (!ObjectUtils.isEmpty(cardApplyDo)) {
                RedisUtils.setEx(uid, cardApplyDo, 1L, TimeUnit.DAYS);
            } else {
                PersonInfoModel personInfo = idmSdk.findPersonInfoByPidFromLocalCache(uid);
                if (ObjectUtils.isEmpty(personInfo) || StringUtils.isEmpty(personInfo.getAccountName())) {
                    log.info("find avatar by uid person:{}", JacksonUtils.bean2Json(personInfo));
                    return "";
                }
            }
        }
        return cardApplyDo == null ? "" : cardApplyDo.getPhotoUrl();
    }

    @Override
    public List<CardApplyDo> findAvatarUrlByUserNameList(List<String> userNameList) {
        List<CardApplyDo> applyDoList = Lists.newArrayList();
        String avatarUrl = "";
        for (String userName : userNameList) {
            try {
                avatarUrl = (String) avatarUrlCache.get(userName, () -> loadAvatarUrlByUserName(userName));
            } catch (Exception e) {
                log.error("userName:{} get avatar url from local cache failed:{}", userName, e.toString());
            } finally {
                if (StringUtils.isNotEmpty(avatarUrl)) {
                    avatarUrlCache.put(userName, avatarUrl);
                } else {
                    avatarUrlCache.put(userName, "default");
                }
                CardApplyDo cardApplyDo = new CardApplyDo();
                cardApplyDo.setPartnerAccount(userName);
                avatarUrl = "default".equals(avatarUrl) ? "" : avatarUrl;
                cardApplyDo.setPhotoUrl(avatarUrl);
                applyDoList.add(cardApplyDo);
            }
        }
        return applyDoList;
    }

    @Override
    public void fillCardApplyByUidAndType(CardApplyDo cardApplyDo) {
        if (StringUtils.isEmpty(cardApplyDo.getUid())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.UID_EMPTY_ERROR);
        }
        if (ObjectUtils.isEmpty(cardApplyDo.getApplyType())
                || OAUCFCommonConstants.INT_ZERO.equals(cardApplyDo.getApplyType())) {
            throw new BizException(CardApplyDomainErrorCodeEnum.CARD_APPLY_TYPE_NOT_FOUND);
        }
        CardApplyDo nowApply = cardApplyRepository.findCardByUidAndCardType(cardApplyDo.getUid(),
                CardTypeEnum.ofNumber(cardApplyDo.getApplyType()));
        commonDoConverter.copyCardApplyDo(nowApply, cardApplyDo);
    }

    private String loadAvatarUrlByUserName(String userName) {
        CardApplyDo cardApplyDo;
        cardApplyDo = (CardApplyDo) RedisUtils.get(userName);
        if (ObjectUtils.isEmpty(cardApplyDo)) {
            cardApplyDo = cardApplyRepository.findAvatarUrlByUserName(userName);
            if (!ObjectUtils.isEmpty(cardApplyDo)) {
                RedisUtils.setEx(userName, cardApplyDo, 1L, TimeUnit.DAYS);
            }
        }
        return cardApplyDo == null ? "" : cardApplyDo.getPhotoUrl();
    }

}
