package com.mi.oa.ee.safety.domain.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.enums.AppCodeEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyUmsConfigEnum;
import com.mi.oa.ee.safety.common.enums.visitor.VisitorInfoVisitTypeEnum;
import com.mi.oa.ee.safety.domain.ability.*;
import com.mi.oa.ee.safety.domain.errorcode.SafetyUmsNotifyDomainErrorCode;
import com.mi.oa.ee.safety.domain.model.*;
import com.mi.oa.ee.safety.domain.service.SafetyUmsNotifyDomainService;
import com.mi.oa.ee.safety.infra.remote.sdk.IdmSdk;
import com.mi.oa.ee.safety.infra.repository.SafetyUmsNotifyRepository;
import com.mi.oa.ee.safety.infra.repository.VisitorApplyVisitorInfoRepository;
import com.mi.oa.ee.safety.infra.repository.VisitorParkRepository;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/20 21:21
 */
@Slf4j
@Service
public class SafetyUmsNotifyDomainServiceImpl implements SafetyUmsNotifyDomainService {

    @Autowired
    SafetyUmsNotifyAbility safetyUmsNotifyAbility;

    @Autowired
    VisitorApplyAbility visitorApplyAbility;

    @Autowired
    SafetyPersonAbility safetyPersonAbility;

    @Autowired
    VisitorParkAbility visitorParkAbility;

    @Autowired
    IdmSdk idmSdk;

    @Autowired
    SafetyUmsNotifyRepository umsNotifyRepository;

    @Autowired
    VisitorParkRepository visitorParkRepository;

    @Autowired
    VisitorApplyVisitorInfoRepository visitorInfoRepository;

    @Autowired
    private ReceptionApplyAbilityService applyAbilityService;

    @Resource
    private CardReceiptAddressAbility cardReceiptAddressAbility;

    @Value("${visitor.workbench-url}")
    String workbenchUrl;

    @Override
    public void checkSafetyUmsNotify(SafetyUmsNotifyDo safetyUmsNotifyDo) {
        if (StringUtils.isEmpty(safetyUmsNotifyDo.getReceiver())) {
            throw new BizException(SafetyUmsNotifyDomainErrorCode.RECEIVER_IS_EMPTY);
        }
        if (StringUtils.isEmpty(safetyUmsNotifyDo.getAppCode())) {
            throw new BizException(SafetyUmsNotifyDomainErrorCode.APP_CODE_IS_EMPTY);
        }
        if (safetyUmsNotifyDo.getConfigId() == null) {
            throw new BizException(SafetyUmsNotifyDomainErrorCode.CONFIG_ID_IS_EMPTY);
        }
        if (safetyUmsNotifyDo.getSendTime() == null) {
            throw new BizException(SafetyUmsNotifyDomainErrorCode.SEND_TIME_IS_EMPTY);
        }
    }

    @Override
    public void buildCardApplyWaitConfirmUmsNotify(SafetyUmsNotifyDo safetyUmsNotifyDo) {
        safetyUmsNotifyAbility.buildCardApplyWaitConfirmUmsNotify(safetyUmsNotifyDo);
    }

    @Override
    public void buildSupplierSyncUmsNotify(SafetyUmsNotifyDo safetyUmsNotifyDo) {
        if (safetyUmsNotifyDo.getSafetySyncDto() != null) {
            safetyUmsNotifyAbility.buildSupplierSyncUmsNotify(safetyUmsNotifyDo);
        }
    }

    @Override
    public SafetyUmsNotifyDo buildVisitorNotify(VisitorApplyDO apply,
                                                SafetyUmsConfigEnum nowEnum, String receiver) {
        SafetyUmsNotifyDo ums = null;
        if (receiver != null) {
            VisitorParkDo visitorParkDo = new VisitorParkDo();
            visitorParkDo.setParkCode(apply.getParkCode());
            visitorParkDo.setBuildingCode(apply.getBuildingCode());
            visitorParkDo.setFloorCode(apply.getFloorCode());
            visitorParkDo.setApplyType(apply.getApplyType());
            visitorParkAbility.fillVisitorParkInfo(visitorParkDo);
            visitorParkAbility.loadVisitorParkNameWithSpaceByCode(visitorParkDo);
            apply.setVisitorPark(visitorParkDo);
            //组装UMS的参数
            Map<String, Object> paramMap = visitorApplyAbility.buildVisitorParamMap(apply, nowEnum);
            //审批通过后给接待人发送飞书提醒
            ums = new SafetyUmsNotifyDo();
            //填装对应的appCode
            ums.setAppCode(AppCodeEnum.VISITOR.getAppCode());
            ums.setReceiver(receiver);
            ums.setParams(JacksonUtils.bean2Json(paramMap));
            ums.setSendTime(ZonedDateTime.now());
            ums.setConfigId(nowEnum.getCode());
        } else {
            log.warn("----- buildVisitorNotify receiver or visitorInfos is null applyCode : {}", apply.getApplyCode());
        }
        return ums;
    }

    @Override
    public Map<String, Object> buildVisitorParamMap(VisitorApplyDO apply, SafetyUmsConfigEnum nowEnum) {
        return visitorApplyAbility.buildVisitorParamMap(apply, nowEnum);
    }

    @Override
    public List<SafetyUmsNotifyDo> buildListCardNotify(List<CardInfoDo> cardInfoDos) {
        List<SafetyUmsNotifyDo> safetyUmsNotifyDos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cardInfoDos)) {
            Map<Integer, List<CardInfoDo>> cardTypeMap =
                    cardInfoDos.stream().collect(Collectors.groupingBy(CardInfoDo::getCardType));
            // 只处理临时卡
            List<CardInfoDo> tempCardInfos = cardTypeMap.get(CardTypeEnum.TEMP_CARD.getNumber());
            dealTempCards(tempCardInfos, SafetyUmsConfigEnum.TEMP_CARD_APPROACH_EXPIRE, safetyUmsNotifyDos);
        }
        return safetyUmsNotifyDos;
    }

    @Override
    public void buildCardNotify(SafetyUmsNotifyDo safetyUmsNotifyDo) {
        CardInfoDo cardInfo = safetyUmsNotifyDo.getCardInfo();
        safetyUmsNotifyDo.setAppCode(AppCodeEnum.CARD.getAppCode());
        safetyUmsNotifyDo.setReceiver(cardInfo.getUid());
        safetyUmsNotifyDo.setSendStatus(OAUCFCommonConstants.INT_ZERO);
        CardReceiptAddressDo cardReceiptAddressDo =
                cardReceiptAddressAbility.findCardReceiptAddressByParkCode(cardInfo.getParkCode());
        if (ObjectUtils.isNotEmpty(cardReceiptAddressDo)) {
            if (StringUtils.isNotEmpty(cardReceiptAddressDo.getPhotoAddress())) {
                safetyUmsNotifyDo.getCardInfo().putExtField("photoAddress", cardReceiptAddressDo.getPhotoAddress());
                safetyUmsNotifyDo.getCardInfo().putExtField("takePhotoTime", cardReceiptAddressDo.getTakePhotoTime());
            } else {
                safetyUmsNotifyDo.getCardInfo().putExtField("photoAddress", SafetyConstants.DEFAULT_RECEIPT_ADDRESS);
                safetyUmsNotifyDo.getCardInfo().putExtField("takePhotoTime", SafetyConstants.DEFAULT_RECEIPT_ADDRESS);
            }
        } else {
            safetyUmsNotifyDo.getCardInfo().putExtField("photoAddress", SafetyConstants.DEFAULT_RECEIPT_ADDRESS);
            safetyUmsNotifyDo.getCardInfo().putExtField("takePhotoTime", SafetyConstants.DEFAULT_RECEIPT_ADDRESS);
        }
        safetyUmsNotifyAbility.buildMap(safetyUmsNotifyDo);
    }

    private void dealTempCards(List<CardInfoDo> tempCardInfos, SafetyUmsConfigEnum tempCardApproachExpire,
                               List<SafetyUmsNotifyDo> safetyUmsNotifyDos) {
        for (CardInfoDo tempCardInfo : tempCardInfos) {
            SafetyUmsNotifyDo ums = new SafetyUmsNotifyDo();
            Map<String, String> params = Maps.newHashMap();
            params.put("userName", tempCardInfo.getPartnerAccount());
            params.put("photoAddress", "{{{【北京M9总装车间医务室】}}}");
            // 时间取 card_time_validity 这张表里的数据
            List<CardTimeValidityDo> validatePeriod = tempCardInfo.getValidatePeriod();
            if (validatePeriod != null) {
                Optional<CardTimeValidityDo> optionalCardTimeValidity = validatePeriod.stream()
                        .filter(cardTimeValidityDo -> cardTimeValidityDo.getCardId().equals(tempCardInfo.getId()))
                        .findFirst();

                String time = optionalCardTimeValidity
                        .map(cardTimeValidityDo -> ZonedDateTimeUtils.formatChinaYMDHM(cardTimeValidityDo.getEndTime()))
                        .orElse(ZonedDateTimeUtils.formatChinaYMDHM(tempCardInfo.getEndTime()));

                String timeEn = optionalCardTimeValidity
                        .map(cardTimeValidityDo -> ZonedDateTimeUtils.formatYMDHMWithRod(cardTimeValidityDo.getEndTime()))
                        .orElse(ZonedDateTimeUtils.formatYMDHMWithRod(tempCardInfo.getEndTime()));

                params.put("time", time);
                params.put("timeEn", timeEn);
            } else {
                // 如果 validatePeriod 为空，直接填充 tempCardInfo 的结束时间
                params.put("time", ZonedDateTimeUtils.formatChinaYMDHM(tempCardInfo.getEndTime()));
                params.put("timeEn", ZonedDateTimeUtils.formatYMDHMWithRod(tempCardInfo.getEndTime()));
            }

            if (tempCardInfo.getDisplayName() != null && !tempCardInfo.getDisplayName().equals("")) {
                params.put("name", tempCardInfo.getDisplayName());
            } else {
                params.put("name", "小米同学");
            }
            ums.setParams(JacksonUtils.bean2Json(params));
            ums.setAppCode(AppCodeEnum.CARD.getAppCode());
            ums.setReceiver(tempCardInfo.getUid());
            ums.setSendTime(ZonedDateTime.now());
            ums.setConfigId(tempCardApproachExpire.getCode());
            ums.setSendStatus(OAUCFCommonConstants.INT_ZERO);
            safetyUmsNotifyDos.add(ums);
        }
    }

    private void dealCoopCards(List<CardInfoDo> coopCardInfos, SafetyUmsConfigEnum nowEnum, List<SafetyUmsNotifyDo> safetyUmsNotifyDos) {
        if (CollectionUtils.isNotEmpty(coopCardInfos)) {
            Map<String, List<CardInfoDo>> responsibleMap =
                    coopCardInfos.stream().collect(Collectors.groupingBy(CardInfoDo::getResponsible));
            for (Map.Entry<String, List<CardInfoDo>> entry : responsibleMap.entrySet()) {
                //多个责任人需要创建多个notify
                SafetyUmsNotifyDo ums = new SafetyUmsNotifyDo();
                if (CollectionUtils.isNotEmpty(entry.getValue())) {
                    Map<String, String> params = Maps.newHashMap();
                    StringBuilder partnerNames = new StringBuilder();
                    for (CardInfoDo cardInfoDo : entry.getValue()) {
                        partnerNames.append(cardInfoDo.getDisplayName());
                        partnerNames.append(",");
                    }
                    partnerNames.deleteCharAt(partnerNames.length() - 1);
                    entry.getValue().sort(((o1, o2) -> ZonedDateTimeUtils.compare(o2.getEndTime(),
                            o1.getEndTime())));
                    params.put("partners", partnerNames.toString());
                    params.put("time", ZonedDateTimeUtils.formatChinaYMDHM(entry.getValue().get(0).getEndTime()));
                    ums.setParams(JacksonUtils.bean2Json(params));
                }
                ums.setAppCode(AppCodeEnum.CARD.getAppCode());
                ums.setReceiver(entry.getKey());
                ums.setSendTime(ZonedDateTime.now());
                ums.setConfigId(nowEnum.getCode());
                ums.setSendStatus(OAUCFCommonConstants.INT_ZERO);
                safetyUmsNotifyDos.add(ums);
            }
        }
    }

    @Override
    public void saveTomorrowExecutiveVisitorNotify(List<VisitorApplyDO> applyDos) {
        ZonedDateTime now = ZonedDateTime.now();
        List<SafetyUmsNotifyDo> safetyUmsNotifyDos = Lists.newArrayList();
        Map<String, List<VisitorApplyDO>> parkAdminApplyDos = parkAdminApplyMap(applyDos);
        Map<Long, List<VisitorApplyVisitUserInfoDo>> collect = visitorInfoRepository.getListByApplyIds(
                applyDos.stream().map(VisitorApplyDO::getId).collect(Collectors.toList())
        ).stream().collect(Collectors.groupingBy(VisitorApplyVisitUserInfoDo::getApplyId));
        parkAdminApplyDos.forEach((parkAdminUid, applies) -> {
            List<List<String>> contents = new ArrayList<>();
            SafetyUmsNotifyDo ums = new SafetyUmsNotifyDo();
            ums.setAppCode(AppCodeEnum.VISITOR.getAppCode());
            ums.setSendTime(now);
            ums.setConfigId(SafetyUmsConfigEnum.PARK_ADMIN_TOMORROW_EXECUTIVE_VISITOR_SEND_LARK.getCode());
            ums.setSendStatus(OAUCFCommonConstants.INT_ZERO);
            Map<String, Object> params = Maps.newHashMap();
            String link = workbenchUrl + String.format("visitor?visitTime=%s&visitType=%d",
                    now.plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'").withZone(ZoneOffset.UTC)),
                    VisitorInfoVisitTypeEnum.EXECUTIVE.getCode()
            );
            params.put("link", appendParkCode(link, applies));
            applies.forEach(visitorApplyDO -> {
                SafetyPersonDo applyUser = SafetyPersonDo.builder().uid(visitorApplyDO.getCreateUser()).build();
                safetyPersonAbility.fillPersonInfoByUid(applyUser);
                SafetyPersonDo receiver = SafetyPersonDo.builder().uid(visitorApplyDO.getReceiver()).build();
                safetyPersonAbility.fillPersonInfoByUid(receiver);
                List<VisitorApplyVisitUserInfoDo> visitInfoDos = collect.get(visitorApplyDO.getId());
                List<String> list = new ArrayList<>();
                list.add(String.format("高管助理：%s", applyUser.getDisplayName()));
                list.add(String.format("接待人员：%s", receiver.getDisplayName()));
                list.add(String.format("邀访公司：%s", qualifyCompanyName(visitorApplyDO.getCompanyName(), visitInfoDos)));
                list.add(String.format("来访园区：%s", visitorApplyAbility.buildParkFullName(visitorApplyDO)));
                list.add(String.format("来访时间：%s", ZonedDateTimeUtils.formatYMDHMWithRod(visitorApplyDO.getVisitTime())));
                list.add(String.format("访客人数：%s", visitInfoDos.size()));
                list.add(String.format("访客车牌：%s", qualifyPlateNumber(
                        visitInfoDos.stream()
                                .map(VisitorApplyVisitUserInfoDo::getPlateNumber)
                                .filter(ObjectUtil::isNotEmpty)
                                .distinct()
                                .collect(Collectors.joining("，")))
                ));
                contents.add(list);
            });

            params.put("info", contents.stream()
                    .map(list -> String.join("\\n", list))
                    .collect(Collectors.joining("\\n——————————\\n"))
            );
            ums.setParams(JacksonUtils.bean2Json(params));
            ums.setReceiver(parkAdminUid);
            safetyUmsNotifyDos.add(ums);
        });
        safetyUmsNotifyAbility.batchCreateNotify(safetyUmsNotifyDos);
    }

    @Override
    public void saveTomorrowReceptionNotify(List<VisitorApplyDO> applyDos) {
        ZonedDateTime now = ZonedDateTime.now();
        List<SafetyUmsNotifyDo> safetyUmsNotifyDos = Lists.newArrayList();
        Map<String, List<VisitorApplyDO>> parkAdminApplyDos = parkAdminApplyMap(applyDos);
        parkAdminApplyDos.forEach((parkAdminUid, applies) -> {
            SafetyUmsNotifyDo ums = new SafetyUmsNotifyDo();
            ums.setAppCode(AppCodeEnum.VISITOR.getAppCode());
            ums.setSendTime(now);
            ums.setConfigId(SafetyUmsConfigEnum.PARK_ADMIN_TOMORROW_RECEPTION_SEND_LARK.getCode());
            ums.setSendStatus(OAUCFCommonConstants.INT_ZERO);
            ums.setReceiver(parkAdminUid);
            Map<String, Object> params = Maps.newHashMap();
            String link = workbenchUrl + String.format("reception/data?visitTime=%s",
                    now.plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'").withZone(ZoneOffset.UTC))
            );
            params.put("link", appendParkCode(link, applies));
            params.put("parkName", applies.get(0).getVisitorPark().getParkName());
            SafetyPersonDo personDo = new SafetyPersonDo();
            personDo.setUid(parkAdminUid);
            safetyPersonAbility.fillPersonInfoByUid(personDo);
            params.put("parkAdminName", personDo.getDisplayName());
            List<List<String>> contents = new ArrayList<>();
            applies.forEach(visitorApplyDO -> {
                SafetyPersonDo applyUser = SafetyPersonDo.builder().uid(visitorApplyDO.getCreateUser()).build();
                safetyPersonAbility.fillPersonInfoByUid(applyUser);
                visitorApplyAbility.loadReceptionApply(visitorApplyDO);
                VisitorReceptionApplyDo receptionApply = visitorApplyDO.getReceptionApply();
                applyAbilityService.loadLevel(receptionApply);
                List<String> list = new ArrayList<>();
                list.add(String.format("来访时间：%s", ZonedDateTimeUtils.formatYMDHMWithRod(visitorApplyDO.getVisitTime())));
                list.add(String.format("来访单位：%s", visitorApplyDO.getCompanyName()));
                list.add(String.format("接待等级：%s", receptionApply.getReceptionLevel().getName()));
                list.add(String.format("申请人员：%s%s(%s)", applyUser.getFirstDeptName(), applyUser.getDisplayName(), applyUser.getUserName()));
                list.add(String.format("来访人数：%s位", receptionApply.getGuestNum()));
                contents.add(list);
            });
            params.put("receptionInfo", contents.stream()
                    .map(list -> String.join("\\n", list))
                    .collect(Collectors.joining("\\n——————————\\n"))
            );
            ums.setParams(JacksonUtils.bean2Json(params));
            safetyUmsNotifyDos.add(ums);
        });
        safetyUmsNotifyAbility.batchCreateNotify(safetyUmsNotifyDos);
    }

    private Map<String, List<VisitorApplyDO>> parkAdminApplyMap(List<VisitorApplyDO> applyDos) {
        Map<String, List<VisitorApplyDO>> parkAdminApplyDos = Maps.newHashMap();
        applyDos.forEach(apply -> {
            visitorApplyAbility.fillParkInfo(apply);
            visitorParkAbility.loadVisitorParkNameWithSpaceByCode(apply.getVisitorPark());
            visitorParkRepository.getParkAdminListByParkIds(Collections.singletonList(apply.getVisitorPark().getId()))
                    .forEach(parkAdminDo -> parkAdminApplyDos.merge(parkAdminDo.getParkAdmin(), new ArrayList<>(Arrays.asList(apply)), (old, new_) -> {
                        old.addAll(new_);
                        return old;
                    }));
        });

        return parkAdminApplyDos;
    }

    @Override
    public SafetyUmsNotifyDo buildCardReceiveNotice(CardApplyDo cardApplyDo) {
        SafetyUmsConfigEnum umsConfigEnum = (SafetyUmsConfigEnum) cardApplyDo.getExtField(SafetyUmsConfigEnum.UMS_CONFIG_ENUM_FIELD);
        SafetyUmsNotifyDo ums = new SafetyUmsNotifyDo();
        Map<String, String> params = Maps.newHashMap();
        params.put("parkName", cardApplyDo.getReceiptParkName());
        params.put("receiptAddress", (String) cardApplyDo.getExtField(SafetyUmsConfigEnum.RECEIPT_ADDRESS));
        params.put("name", cardApplyDo.getDisplayName());
        ums.setParams(JacksonUtils.bean2Json(params));
        ums.setAppCode(AppCodeEnum.CARD.getAppCode());
        ums.setReceiver(cardApplyDo.getUid());
        ums.setSendTime(ZonedDateTime.now());
        ums.setConfigId(umsConfigEnum.getCode());
        ums.setSendStatus(OAUCFCommonConstants.INT_ZERO);
        return ums;
    }

    @Override
    public List<SafetyUmsNotifyDo> buildCardReceiveNoticeList(List<CardApplyDo> cardApplyList) {
        return cardApplyList.stream().map(this::buildCardReceiveNotice).collect(Collectors.toList());
    }

    private String qualifyPlateNumber(String plateNumber) {
        return ObjectUtil.isEmpty(plateNumber) ? "{{{无}}}" : plateNumber;
    }

    private String qualifyCompanyName(String companyName, List<VisitorApplyVisitUserInfoDo> visitInfoDos) {
        return ObjectUtil.isNotEmpty(companyName) ? companyName : visitInfoDos.get(0).getName();
    }

    /**
     * 跳转链接条件性追加园区搜索条件
     */
    private String appendParkCode(String link, List<VisitorApplyDO> applyDOS) {
        List<String> parkCodes = applyDOS.stream().map(VisitorApplyDO::getParkCode).distinct().collect(Collectors.toList());
        if (parkCodes.size() == 1) {
            link += String.format("&parkCode=%s", parkCodes.get(0));
            List<String> buildingCodes = applyDOS.stream().map(VisitorApplyDO::getBuildingCode).distinct().collect(Collectors.toList());
            if (buildingCodes.size() == 1) {
                link += String.format("&buildingCode=%s", buildingCodes.get(0));
                List<String> floorCodes = applyDOS.stream().map(VisitorApplyDO::getFloorCode).distinct().collect(Collectors.toList());
                if (floorCodes.size() == 1) {
                    link += String.format("&floorCode=%s", floorCodes.get(0));
                }
            }
        }

        return link;
    }

    @Override
    public void batchInsert(List<SafetyUmsNotifyDo> safetyUmsNotifyDos) {
        safetyUmsNotifyAbility.batchCreateNotify(safetyUmsNotifyDos);
    }

    @Override
    public void batchUmsInsert(List<SafetyUmsNotifyDo> safetyUmsNotifyDos) {
        safetyUmsNotifyAbility.batchCreateUmsNotify(safetyUmsNotifyDos);
    }

    /**
     * @param safetyUmsNotifyDo
     * @return void
     * @desc 接收人需调用方填充
     * <AUTHOR> denghui
     * @date 2023/6/13 14:38
     */
    @Override
    public void buildNotify(SafetyUmsNotifyDo safetyUmsNotifyDo) {
        safetyUmsNotifyAbility.buildMap(safetyUmsNotifyDo);
        safetyUmsNotifyDo.setSendTime(ZonedDateTime.now());
        safetyUmsNotifyDo.setSendStatus(OAUCFCommonConstants.INT_ZERO);
    }

    @Override
    public SafetyUmsNotifyDo buildUmsSmsNotify(SafetyPersonDo safetyPersonDo, String code) {
        SafetyUmsNotifyDo safetyUmsNotifyDo = new SafetyUmsNotifyDo();
        safetyUmsNotifyDo.setAppCode(AppCodeEnum.CARD.getAppCode());
        safetyUmsNotifyDo.setReceiver(safetyPersonDo.getMobile());
        safetyUmsNotifyDo.setSendTime(ZonedDateTime.now());
        safetyUmsNotifyDo.setConfigId(SafetyUmsConfigEnum.CARD_MACHINE_SEND_SMS.getCode());
        safetyUmsNotifyDo.setSendStatus(OAUCFCommonConstants.INT_ZERO);
        Map<String, String> params = Maps.newHashMap();
        params.put("code", code);
        params.put("userDisplayName", safetyPersonDo.getDisplayName());
        safetyUmsNotifyDo.setParams(JacksonUtils.bean2Json(params));
        return safetyUmsNotifyDo;
    }
}
