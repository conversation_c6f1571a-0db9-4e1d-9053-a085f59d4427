package com.mi.oa.ee.visitor.api.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2022/11/16 21:04
 */
@Data
@ApiModel(value = "SafetyRecordVo", description = "安防记录信息")
public class SafetyRecordVo {

    @ApiModelProperty(value = "人员类型", dataType = "java.lang.String", required = false)
    private String userType;

    @ApiModelProperty(value = "人员类型名称", dataType = "java.lang.String", required = false)
    private String userTypeDesc;

    /**
     * 用户UID uid
     */
    @ApiModelProperty(value = "用户UID", dataType = "java.lang.String", required = false)
    private String uid;

    /**
     * 用户账号 user_name
     */
    @ApiModelProperty(value = "{{{用户账号}}}", dataType = "java.lang.String", required = false)
    private String userName;

    /**
     * 用户账号 user_name
     */
    @ApiModelProperty(value = "{{{用户姓名}}}", dataType = "java.lang.String", required = false)
    private String displayName;

    /**
     * 一级部门
     */
    @ApiModelProperty(value = "{{{一级部门}}}", dataType = "java.lang.String", required = false)
    private String firstDepartName;

    /**
     * 二级部门
     */
    @ApiModelProperty(value = "{{{二级部门}}}", dataType = "java.lang.String", required = false)
    private String secondDepartName;

    /**
     * 三级部门
     */
    @ApiModelProperty(value = "{{{三级部门}}}", dataType = "java.lang.String", required = false)
    private String thirdDepartName;

    /**
     * 四级部门
     */
    @ApiModelProperty(value = "{{{四级部门}}}", dataType = "java.lang.String", required = false)
    private String fourthDepartName;

    @ApiModelProperty(value = "国家名称", dataType = "java.lang.String", required = false)
    private String countryName;

    @ApiModelProperty(value = "省份名称", dataType = "java.lang.String", required = false)
    private String provinceName;

    @ApiModelProperty(value = "城市名称", dataType = "java.lang.String", required = false)
    private String cityName;

    @ApiModelProperty(value = "园区名称", dataType = "java.lang.String", required = false)
    private String parkName;

    @ApiModelProperty(value = "楼栋名称", dataType = "java.lang.String", required = false)
    private String buildingName;

    @ApiModelProperty(value = "楼层名称", dataType = "java.lang.String", required = false)
    private String floorName;

    @ApiModelProperty(value = "工位编码", dataType = "java.lang.String", required = false)
    private String stationCode;

    @ApiModelProperty(value = "类型名称", dataType = "java.lang.String", required = false)
    private String className;

    /**
     * 介质编码 medium_code
     */
    @ApiModelProperty(value = "{{{介质编码}}}", dataType = "java.lang.String", required = false)
    private String mediumCode;

    @ApiModelProperty(value = "供应商侧物理编码", dataType = "java.lang.String", required = false)
    private String mediumPhysicsCode;

    @ApiModelProperty(value = "供应商侧加密编码", dataType = "java.lang.String", required = false)
    private String mediumEncryptCode;

    /**
     * 载体编码 carrier_code
     */
    @ApiModelProperty(value = "载体编码", dataType = "java.lang.String", required = false)
    private String carrierCode;

    /**
     * 载体名称
     */
    @ApiModelProperty(value = "载体名称", dataType = "java.lang.String", required = false)
    private String carrierName;

    /**
     * 载体类型 0：默认  1：门禁供应商   2：闸机供应商   3：抬杆供应商 carrier_type
     */
    @ApiModelProperty(value = "载体类型 0：默认  1：门禁供应商   2：闸机供应商   3：抬杆供应商", dataType = "java.lang.String", required = false)
    private Integer carrierType;

    /**
     * 供应商侧介质校验编码 supplier_medium_check_code
     */
    @ApiModelProperty(value = "{{{供应商侧介质校验编码}}}", dataType = "java.lang.String", required = false)
    private String supplierMediumCheckCode;

    /**
     * 供应商侧载体序列号 supplier_carrier_control_serial
     */
    @ApiModelProperty(value = "{{{供应商侧载体序列号}}}", dataType = "java.lang.String", required = false)
    private String supplierCarrierControlSerial;

    /**
     * 进出
     */
    @ApiModelProperty(value = "进出1：入 2：出", dataType = "java.lang.String", required = false)
    private Integer inOrOut;

    @ApiModelProperty(value = "工位", dataType = "java.lang.String", required = false)
    private String stationName;

    /**
     * 记录时间 record_time
     */
    @ApiModelProperty(value = "{{{记录时间}}}", dataType = "java.time.ZonedDateTime", required = false)
    private String recordTime;

    /**
     * 创建时间
     *
     * @param null
     * @return
     * <AUTHOR>
     * @date 2022/8/31 17:16
     */
    @ApiModelProperty(value = "创建时间", dataType = "java.time.ZonedDateTime", required = false)
    private ZonedDateTime createTime;


}
