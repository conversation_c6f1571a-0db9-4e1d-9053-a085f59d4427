package com.mi.oa.ee;

import com.google.gson.reflect.TypeToken;
import com.mi.oa.ee.safety.common.dto.PersonInfoModel;
import com.mi.oa.ee.safety.infra.remote.sdk.HrodSdk;
import com.mi.oa.ee.visitor.api.VisitorApiApplication;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.newauth.autoconfig.AuthProperties;
import com.mi.oa.infra.oaucf.newauth.core.dto.SecretToken;
import com.mi.oa.infra.oaucf.newauth.core.token.TokenService;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.Identity;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import com.mi.oa.infra.uc.common.util.GsonUtil;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
@SpringBootTest(classes = VisitorApiApplication.class)
@Ignore
public class BaseTest {

    @Resource
    protected MockMvc mockMvc;

    @Resource
    private AuthProperties authProperties;

    @Resource
    private TokenService localTokenService;

    @Resource
    private HrodSdk hrodSdk;

    @Test
    public void testGetLocationHire() {
        String mi_1103 = hrodSdk.getLocationHireByReportAddress("MI_1103");
        System.out.println(mi_1103);
    }

    @Test
    public void testGetLocationCity() {
        PersonInfoModel chn053 = hrodSdk.getLocationCityByCityCode("CHN053");
        System.out.println(chn053);
    }


    protected HttpHeaders headers() throws Exception {
        HttpHeaders headers = new HttpHeaders();
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/api/v1/auth/open")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(GsonUtil.toJsonString(authProperties))
                                .cookie(cookie())
                )
                .andDo(result -> {
                    String content = result.getResponse().getContentAsString(StandardCharsets.UTF_8);
                    Type type = new TypeToken<BaseResp<SecretToken>>() {
                    }.getType();
                    BaseResp<SecretToken> resp = GsonUtils.fromJson(content, type);
                    headers.add("Authorization",
                            "Bearer " + resp.getData().getToken());
                });
        return headers;
    }

    protected Cookie cookie() {
        Identity identity = new Identity();
        identity.setIdentityType(Identity.IdentityType.USER);
        identity.setLoginType(Identity.LoginType.CAS);
        identity.setValue("wangjun30");
        String token = localTokenService.generateToken(identity).getToken();
        Cookie cookie = new Cookie("token", token);
        //过期时间，单位是：秒（s）
        cookie.setMaxAge(30 * 24 * 60 * 60);
        cookie.setPath("/");
        return cookie;
    }
}
