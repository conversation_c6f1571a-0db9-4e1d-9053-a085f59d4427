package com.mi.oa.ee.safety.api.web;

import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * index 控制类
 *
 * <AUTHOR>
 * @date 2021/7/15 19:55
 */
@WebLog
@RestController
@RequestMapping("/api/v1")
public class IndexController {

    @GetMapping ("/index")
    public BaseResp<Object> index() {
        Map<String, String> reMap = Maps.newHashMap();
        reMap.put("cache", "nowStr");
        reMap.put("error", ApplicationErrorCodeEnum.APPLICATION_UNKNOWN_ERROR.getErrDesc());
        return BaseResp.success(reMap);
    }

}
