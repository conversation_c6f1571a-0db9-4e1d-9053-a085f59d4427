package com.mi.oa.ee.safety.api.errorcode;

import com.mi.oa.infra.oaucf.core.exception.InterfaceErrorCode;

/**
 * by roger
 */
public enum InterfaceErrorCodeEnum implements InterfaceErrorCode {

    INTERFACE_UNKNOWN_ERROR(1, "{{{接口层未知错误}}}"),
    INTERFACE_PARAM_EMPTY_ERROR(2, "{{{参数为空}}}"),
    INTERFACE_EXPORT_ERROR(3, "{{{系统处理导出任务较多,请一分钟后重试}}}"),
    EXPORT_IMAGE_FAIL(4, "{{{导出图片失败}}}"),
    UC_DIMENSION_CODE_UNDEFINED(5, "{{{UC权限维度编码未定义}}}"),
    CARD_TRAVEL_RECORD_PARAMS_ERROR(6, "工卡差旅记录参数错误"),
    ELECTRONIC_IMEI_EMPTY(7, "设备号不能为空"),
    EMP_NO_NOT_EMPTY(8, "工号不能为空！"),
    USER_NAME_NOT_EMPTY(9, "用户名不能为空！"),
    NOT_EXIST_TO_PRINT(10, "当前用户(%s)不存在待打印的工卡"),
    NOT_EXIST_REISSUE_CARD_APPLY(11, "当前用户(%s)没有满足制卡条件，非员工卡或没有照片"),
    PHONE_CAN_NOT_EMPTY(12, "手机号不可以为空！"),
    PHYSICS_CARD_ERROR(13, "物理卡号异常"),
    ENCRYPT_CARD_NOT_EMPTY(14, "加密卡号不能为空"),
    NOT_EXIST_USER(15, "用户不存在"),
    VERIFICATION_CODE_CAN_NOT_EMPTY(16, "验证码不能为空"),
    VERIFICATION_CODE_NOT_EXIST(17, "验证码不存在"),
    VERIFICATION_CODE_ERROR(18, "验证码错误"),
    ID_NOT_EMPTY(19, "id不能为空"),
    UPDATE_REISSUE_CARD_APPLY_ERROR(20, "更新补卡状态失败"),
    DATA_ERROR(21, "工卡数据错误，请联系管理员"),
    PHYSICS_CARD_CAN_NOT_EMPTY(22, "物理卡号不能为空"),
    NOT_EXIST_TO_OPEN(23, "当前用户(%s)不存在待开卡的工卡"),
    PHOTO_URL_NOT_EMPTY(24, "照片地址不能为空"),
    CARD_APPLY_NOT_EXIST(25, "工卡申请单不存在"),
    PARK_CODE_NOT_EMPTY(26, "园区编码不能为空"),
    PHONE_LAST_FOUR_NOT_EMPTY(27, "手机号后四位不能为空"),
    PHOTO_NOT_EXIST(28, "照片不存在"),
    UID_NOT_EMPTY(29, "UID不能为空！"),
    CITY_ID_EMPTY(30, "城市ID不能为空"),
    CODE_NOT_EMPTY(31, "code不能为空"),
    PARAMS_ERROR(32, "参数错误"),;


    /**
     * 具体错误码
     */
    private int errCode;
    /**
     * 描述
     */
    private String errDesc;

    /**
     * 构造方法
     *
     * @param errCode
     * @param errDesc
     */
    InterfaceErrorCodeEnum(int errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }

    @Override
    public int getBizCode() {
        return 0;
    }

    @Override
    public int getErrorCode() {
        return errCode;
    }

    @Override
    public String getErrDesc() {
        return this.errDesc;
    }

}
