package com.mi.oa.ee.safety.api.web.common;

import com.mi.oa.ee.safety.api.converter.common.CommonVoConverter;
import com.mi.oa.ee.safety.api.model.common.req.ExportHistoryReq;
import com.mi.oa.ee.safety.api.model.common.req.ImportHistoryReq;
import com.mi.oa.ee.safety.api.model.common.req.PhotoEditReq;
import com.mi.oa.ee.safety.api.model.common.vo.*;
import com.mi.oa.ee.safety.application.dto.common.*;
import com.mi.oa.ee.safety.application.service.card.common.CardConfigService;
import com.mi.oa.ee.safety.application.service.common.AsyncExportService;
import com.mi.oa.ee.safety.application.service.common.AsyncImportTaskService;
import com.mi.oa.ee.safety.application.service.common.CommonService;
import com.mi.oa.ee.safety.common.dto.*;
import com.mi.oa.ee.safety.infra.remote.sdk.AddressSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.HrodSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.IdmSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.SpaceSdk;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/6/7 16:03
 */
@Api(tags = "公共接口")
@WebLog
@RestController
@RequestMapping("/api/v1/card/common")
@Slf4j
public class CommonController {

    @Resource
    private CommonVoConverter commonVoConverter;

    @Resource
    private CommonService commonService;

    @Resource
    private AsyncImportTaskService asyncImportTaskService;

    @Resource
    private AsyncExportService asyncExportService;

    @Resource
    private HrodSdk hrodSdk;

    @Resource
    private AddressSdk addressSdk;

    @Resource
    private SpaceSdk spaceSdk;

    @Resource
    private IdmSdk idmSdk;

    @Resource
    private CardConfigService cardConfigService;

    @Resource
    private IdmRemote idmRemote;

    @GetMapping("/zone/code/list")
    @ApiOperation(value = "地区编码列表", httpMethod = "GET")
    public BaseResp<List<SafetyZoneDto>> getZoneCodeList() {
        List<SafetyZoneDto> countryInfoDtoList = idmSdk.getZoneCodeListFromLocalCache();
        return BaseResp.success(countryInfoDtoList);
    }


    @GetMapping("/fuzzy/list/address")
    @ApiOperation(value = "模糊搜索地区信息", httpMethod = "GET")
    public BaseResp<List<CountryInfoVO>> findFuzzyAddress(@RequestParam(value = "addressName", required = false) String addressName) {
        List<CountryInfoDto> countryInfoDtoList = commonService.fuzzyByAddressName(addressName);
        return BaseResp.success(commonVoConverter.toCountryInfoVoList(countryInfoDtoList));
    }

    @GetMapping("/list/dept")
    @ApiOperation(value = "模糊搜索部门", httpMethod = "GET")
    public BaseResp<List<DeptVo>> getDeptList(@RequestParam(value = "deptName", required = false) String deptName) {
        return BaseResp.success(commonVoConverter.toVoList(hrodSdk.getDeptPathByDeptName(deptName)));
    }

    @GetMapping("/address/country")
    @ApiOperation(value = "获取国家接口(确认页面)", httpMethod = "GET")
    public BaseResp<List<CountryInfoVO>> getCountryList() {
        List<CountryInfoDto> countryInfoDtoList = commonService.countryList();
        return BaseResp.success(commonVoConverter.toCountryInfoVoList(countryInfoDtoList));
    }

    @GetMapping("/address/info/{addrId}")
    @ApiOperation(value = "获取当前地区城市(确认页面)", httpMethod = "GET")
    public BaseResp<AddressInfoDto> getAddressById(@PathVariable("addrId") String addrId) {
        AddressInfoDto address = addressSdk.getAddressById(Integer.valueOf(addrId));
        return BaseResp.success(address);
    }

    @GetMapping("/list/address/{addrId}")
    @ApiOperation(value = "获取下一级地区城市(确认页面)", httpMethod = "GET")
    public BaseResp<List<AddressInfoDto>> getAddressByParentId(@PathVariable("addrId") String addrId) {
        List<AddressInfoDto> address = addressSdk.getAddressByParentId(addrId);
        return BaseResp.success(address);
    }

    @GetMapping("/list/parks")
    @ApiOperation(value = "获取当前城市下所有园区(确认页面)", httpMethod = "GET")
    public BaseResp<List<SafetySpaceParkDto>> getParkList(@RequestParam("cityId") Integer cityId) {
        List<SafetySpaceParkDto> spaceParks = spaceSdk.getListParksByCityId(cityId);
        return BaseResp.success(spaceParks);
    }

    @GetMapping("/list/parks/fuzzy")
    @ApiOperation(value = "模糊搜索园区信息", httpMethod = "GET")
    public BaseResp<List<SafetySpaceParkDto>> getParkList(@RequestParam(value = "name", required = false) String name) {
        List<SafetySpaceParkDto> spaceParks = spaceSdk.getParkCodesByFuzzyName(name);
        return BaseResp.success(spaceParks);
    }

    @GetMapping("/user/info")
    @ApiOperation(value = "模糊搜索用户信息", httpMethod = "GET")
    public BaseResp<List<AccountModel>> getFuzzyUser(@RequestParam("name") String name) {
        return BaseResp.success(cardConfigService.findFuzzyUser(name));
    }

    @ApiOperation(value = "导入记录列表")
    @GetMapping("/import/history")
    public BaseResp<PageVO<AsyncImportRecordVo>> importHistoryList(ImportHistoryReq req) {
        AsyncImportQueryDto queryDto = commonVoConverter.toQueryDto(req);
        PageVO<AsyncImportTaskDto> pageVO = asyncImportTaskService.page(queryDto);
        List<AsyncImportRecordVo> resList = commonVoConverter.toImportRecordResList(pageVO.getList());
        return BaseResp.success(PageVO.build(resList, pageVO.getPageSize(), pageVO.getPageNum(), pageVO.getTotal()));
    }

    @ApiOperation(value = "导出记录列表")
    @GetMapping("/export/history")
    public BaseResp<PageVO<ExportHistoryVo>> exportHistoryList(ExportHistoryReq exportHistoryReq) {
        String uid = idmRemote.getLoginUid();
        ExportHistoryQueryDto exportHistoryQueryDto = commonVoConverter.toDto(exportHistoryReq);
        exportHistoryQueryDto.setUid(uid);
        PageVO<ExportHistoryDto> page = asyncExportService.page(exportHistoryQueryDto);
        List<ExportHistoryVo> tradeOrderResList = commonVoConverter.toResExtList(page.getList());
        PageVO<ExportHistoryVo> resPage = PageVO.build(tradeOrderResList, page.getPageSize(), page.getPageNum(), page.getTotal());
        return BaseResp.success(resPage);
    }

    @ApiOperation(value = "当前员工权限列表")
    @GetMapping("/user/permission")
    public BaseResp<UserPermissionVo> permission() {
        UserPermissionDto permissions = commonService.userPermissions();
        return BaseResp.success(commonVoConverter.toVo(permissions));
    }

    @GetMapping("/park/list")
    @ApiOperation(value = "园区列表", httpMethod = "GET")
    public BaseResp<List<SafetySpaceParkDto>> parkList() {
        List<SafetySpaceParkDto> listParks = spaceSdk.getParkBuildingFloorTree();
        return BaseResp.success(listParks);
    }

    @PostMapping("/photo/edit")
    @ApiOperation(value = "编辑照片", httpMethod = "POST")
    public BaseResp<Void> editPhoto(@RequestBody @Valid PhotoEditReq req) {
        PhotoEditDto photoEditDto = commonVoConverter.toPhotoEditDto(req);
        commonService.editPhoto(photoEditDto);
        return BaseResp.success();
    }
}
