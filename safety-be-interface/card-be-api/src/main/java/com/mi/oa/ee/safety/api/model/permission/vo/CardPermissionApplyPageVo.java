package com.mi.oa.ee.safety.api.model.permission.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.omg.CORBA.PRIVATE_MEMBER;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/4/7 21:39
 */
@Data
@ApiModel(value = "CardPermissionApplyPageVo", description = "卡权限申请单分页Vo对象")
public class CardPermissionApplyPageVo {

    @ApiModelProperty(value = "权限申请单id", dataType = "java.lang.Long")
    private Long permissionApplyId;

    @ApiModelProperty(value = "卡Id", dataType = "java.lang.Long")
    private Long cardId;

    @ApiModelProperty(value = "卡类型", dataType = "java.lang.String")
    private String cardType;

    @ApiModelProperty(value = "权限申请单code", dataType = "java.lang.String")
    private String permissionApplyCode;

    @ApiModelProperty(value = "权限申请单状态", dataType = "java.lang.Integer")
    private Integer permissionApplyStatus;

    @ApiModelProperty(value = "权限申请单状态描述", dataType = "java.lang.String")
    private String permissionApplyStatusDesc;

    @ApiModelProperty(value = "申请人姓名", dataType = "java.lang.String")
    private String displayName;

    @ApiModelProperty(value = "账号", dataType = "java.lang.String")
    private String userName;

    @ApiModelProperty(value = "申请人一级部门", dataType = "java.lang.String")
    private String firstDeptName;

    @ApiModelProperty(value = "申请人二级部门", dataType = "java.lang.String")
    private String secondDeptName;

    @ApiModelProperty(value = "申请人三级部门", dataType = "java.lang.String")
    private String thirdDeptName;

    @ApiModelProperty(value = "申请人四级部门", dataType = "java.lang.String")
    private String fourthDeptName;

    @ApiModelProperty(value = "申请类型", dataType = "java.lang.Integer")
    private Integer applyType;

    @ApiModelProperty(value = "门禁类型", dataType = "java.lang.String")
    private String controlTypeDesc;

    @ApiModelProperty(value = "申请园区编码", dataType = "java.lang.String")
    private String parkCode;

    @ApiModelProperty(value = "申请园区名称", dataType = "java.lang.String")
    private String parkName;

    @ApiModelProperty(value = "创建时间", dataType = "java.lang.String")
    private ZonedDateTime createTime;

    @ApiModelProperty(value = "权限包集合", dataType = "java.lang.String")
    private List<CardGroupVo> cardGroupList;

}
