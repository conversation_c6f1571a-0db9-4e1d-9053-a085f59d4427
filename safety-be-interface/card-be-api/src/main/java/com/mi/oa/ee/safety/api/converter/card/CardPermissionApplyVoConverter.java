package com.mi.oa.ee.safety.api.converter.card;

import com.mi.oa.ee.safety.api.model.permission.req.BpmCallBackReq;
import com.mi.oa.ee.safety.api.model.permission.req.CardGroupPageReq;
import com.mi.oa.ee.safety.api.model.permission.req.CardPermissionApplyPageReq;
import com.mi.oa.ee.safety.api.model.permission.req.CardPermissionCreateReq;
import com.mi.oa.ee.safety.api.model.permission.vo.CardGroupPageVo;
import com.mi.oa.ee.safety.api.model.permission.vo.CardGroupVo;
import com.mi.oa.ee.safety.api.model.permission.vo.CardPermissionAdminDetailVo;
import com.mi.oa.ee.safety.api.model.permission.vo.CardPermissionApplyDetailVo;
import com.mi.oa.ee.safety.api.model.permission.vo.CardPermissionApplyPageVo;
import com.mi.oa.ee.safety.api.model.permission.vo.ControlTypeVo;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigQueryDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardPermissionApplyDto;
import com.mi.oa.ee.safety.application.dto.safety.SafetyClassDto;
import com.mi.oa.ee.safety.application.dto.visitor.BpmCallBackDto;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto;
import com.mi.oa.ee.safety.common.enums.StatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardClassEnum;
import com.mi.oa.ee.safety.common.enums.card.CardPermissionApplyStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/4/2 22:18
 */
@Mapper(componentModel = "spring")
public interface CardPermissionApplyVoConverter {

    @IterableMapping(qualifiedByName = "toControlType")
    List<ControlTypeVo> toControlTypeVoList(List<CardPermissionApplyDto> permissionApplyDtoList);

    @Named("toControlType")
    ControlTypeVo toControlTypeVo(CardPermissionApplyDto permissionApplyDto);

    @Mapping(target = "startTime", expression = "java(toStartTime(req.getIsForever(), req.getStartTime()))")
    @Mapping(target = "endTime", expression = "java(toEndTime(req.getIsForever(), req.getEndTime()))")
    @Mapping(target = "parkCode", source = "parkCode")
    CardPermissionApplyDto createReqToDto(CardPermissionCreateReq req);

    @Named("toStartTime")
    default ZonedDateTime toStartTime(Boolean isForever, ZonedDateTime startTime) {
        if (isForever && startTime == null) {
            return ZonedDateTimeUtils.toZonedDateTime(SafetyConstants.Card.DEFAULT_START_TIME_STR);
        }
        return startTime;
    }

    @Named("toEndTime")
    default ZonedDateTime toEndTime(Boolean isForever, ZonedDateTime endTime) {
        if (isForever && endTime == null) {
            return ZonedDateTimeUtils.toZonedDateTime(SafetyConstants.Card.DEFAULT_END_TIME_STR);
        }
        return endTime;
    }

    @Mapping(target = "applyStatusDesc", source = "applyStatus", qualifiedByName = "applyStatusToDesc")
    @Mapping(target = "controlTypeDesc", source = "controlType", qualifiedByName = "controlTypeToDesc")
    @Mapping(target = "cardGroupList", source = "cardGroupList", qualifiedByName = "cardGroupList")
    @Mapping(target = "existCardGroupList", source = "existCardGroupList", qualifiedByName = "existCardGroupList")
    @Mapping(target = "isForever", source = "endTime", qualifiedByName = "checkIsShortTime")
    CardPermissionApplyDetailVo toDetailVo(CardPermissionApplyDto cardPermissionApplyDto);

    @Named("applyStatusToDesc")
    default String applyStatusToDesc(Integer applyStatus) {
        return CardPermissionApplyStatusEnum.codeToDesc(applyStatus);
    }

    @Named("controlTypeToDesc")
    default String controlTypeToDesc(String controlType) {
        return CardClassEnum.getDesc(controlType);
    }

    @Named("cardGroupList")
    default List<CardGroupVo> cardGroupList(List<CardGroupConfigDto> cardGroupList) {
        List<CardGroupVo> res = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cardGroupList)) {
            cardGroupList.forEach(item -> {
                CardGroupVo cardGroupVo = toGroupVo(item);
                cardGroupVo.setRecordStatus(item.getRecordStatusEnum().getCode());;
                res.add(cardGroupVo);
            });
        }
        return res;
    }

    @Named("existCardGroupList")
    default List<CardGroupVo> existCardGroupList(List<CardGroupConfigDto> existCardGroupList) {
        List<CardGroupVo> res = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(existCardGroupList)) {
            existCardGroupList.forEach(item -> {
                CardGroupVo cardGroupVo = toGroupVo(item);
                cardGroupVo.setRecordStatus(item.getRecordStatusEnum().getCode());;
                res.add(cardGroupVo);
            });
        }
        return res;
    }

    @Named("checkIsShortTime")
    default boolean checkIsShortTime(ZonedDateTime endTime) {
        ZonedDateTime zo = ZonedDateTimeUtils.toZonedDateTime(SafetyConstants.Card.DEFAULT_END_TIME_STR);
        return zo.isEqual(endTime);
    }

    BpmCallBackDto toBpmCallBackDto(BpmCallBackReq params);

    CardGroupConfigQueryDto pageReqToDto(CardGroupPageReq req);

    @IterableMapping(qualifiedByName = "toGroupPageVo")
    List<CardGroupPageVo> toGroupPageVoList(List<CardGroupConfigDto> list);

    @Named("toGroupPageVo")
    @Mapping(target = "deptNamePath", source = "deptInfo.deptNamePath")
    CardGroupPageVo toGroupPageVo(CardGroupConfigDto dto);

    @Mapping(target = "applyStatus", source = "permissionApplyStatus")
    CardPermissionApplyDto applyPageReqToDto(CardPermissionApplyPageReq req);

    @IterableMapping(qualifiedByName = "toPageVo")
    List<CardPermissionApplyPageVo> toApplyPageVoList(List<CardPermissionApplyDto> list);

    @Named("toPageVo")
    @Mapping(target = "permissionApplyStatusDesc", source = "applyStatus", qualifiedByName = "applyStatusToDesc")
    @Mapping(target = "controlTypeDesc", source = "controlTypeName")
    @Mapping(target = "cardType", source = "cardType", qualifiedByName = "cardTypeNumToCode")
    CardPermissionApplyPageVo toPageVo(CardPermissionApplyDto dto);

    @Named("cardTypeNumToCode")
    default String cardTypeNumToCode(Integer cardType) {
        CardTypeEnum cardTypeEnum = CardTypeEnum.ofNumber(cardType);
        if (Objects.nonNull(cardTypeEnum)) {
            return cardTypeEnum.getCode();
        }
        return null;
    }

    @Mapping(target = "controlTypeDesc", source = "controlTypeName")
    CardPermissionAdminDetailVo toAdminDetailVo(CardPermissionApplyDto cardPermissionApplyDto);

    @IterableMapping(qualifiedByName = "toGroupVo")
    List<CardGroupVo> toGroupVoList(List<CardGroupConfigDto> existCardGroupList);

    @Named("toGroupVo")
    CardGroupVo toGroupVo(CardGroupConfigDto dto);

}
