package com.mi.oa.ee.safety.api.converter.card;

import com.mi.oa.ee.safety.api.model.machine.vo.CardReissueMachineVo;
import com.mi.oa.ee.safety.application.dto.card.reissue.ReissueCardApplyDto;
import com.mi.oa.ee.safety.common.dto.CardReissueUserInfoFromOldDto;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/8/20 16:49
 */
@Mapper(componentModel = "spring")
public interface CardReissueMachineVoConverter {

    @Mapping(target = "id", source = "reissueApplyId")
    @Mapping(target = "empNo", source = "employeeNo")
    @Mapping(target = "enName", expression = "java(toCardPinYin(dto.getFirstNameEn(), dto.getLastNameEn()))")
    @Mapping(target = "cardReissueApplyStatus", source = "reissueApplyStatus")
    CardReissueMachineVo toReissueCardMachineVo(ReissueCardApplyDto dto);

    default String toCardPinYin(String firstName, String lastName) {
        return StringUtils.capitalize(lastName) + " " + StringUtils.capitalize(firstName);
    }

    @Mapping(target = "id", source = "id")
    @Mapping(target = "empNo", source = "employeeId")
    @Mapping(target = "name", source = "employeeName")
    @Mapping(target = "enName", source = "employeeEnName")
    @Mapping(target = "photoUrl", source = "photo")
    @Mapping(target = "cardType", source = "makeCardType")
    CardReissueMachineVo fromOldDtoToVo(CardReissueUserInfoFromOldDto bean);
}
