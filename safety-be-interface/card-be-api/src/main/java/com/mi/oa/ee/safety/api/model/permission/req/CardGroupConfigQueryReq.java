package com.mi.oa.ee.safety.api.model.permission.req;

import com.mi.oa.infra.oaucf.core.dto.BaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/4/1 21:40
 */
@ApiModel(description = "权限配置查询请求参数")
@Data
public class CardGroupConfigQueryReq extends BaseReq {

    @ApiModelProperty(value = "权限名称", example = "相机实验室", dataType = "java.lang.String")
    private String cardGroupName;

    @ApiModelProperty(value = "权限名称", example = "1", dataType = "java.lang.String")
    private String controlType;

    @ApiModelProperty(value = "地区", example = "103", dataType = "java.lang.String")
    private String cityId;

    @ApiModelProperty(value = "园区", example = "BJ01", dataType = "java.lang.String")
    private String parkCode;

    @ApiModelProperty(value = "管理员", example = "95c3cc598f7a427a9a60b370f5fd32af", dataType = "java.lang.String")
    private String adminUid;

    @ApiModelProperty(value = "权限编码", example = "GROUP001", dataType = "java.lang.String")
    private String cardGroupCode;

    @ApiModelProperty(value = "权限组名称", example = "实验室权限组", dataType = "java.lang.String")
    private String carrierGroupName;

    @ApiModelProperty(value = "是否允许用户申请", example = "1", dataType = "java.lang.Integer")
    private Integer applyFlag;

    @ApiModelProperty(value = "状态", example = "1", dataType = "java.lang.Integer")
    private Integer recordStatus;

    @ApiModelProperty(value = "部门路径", example = "小米集团/技术部/研发中心", dataType = "java.lang.String")
    private String deptNamePath;
}
