package com.mi.oa.ee.safety.api.model.config.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/21 10:10
 */
@ApiModel("临时卡删除请求参数")
@Data
public class RecyclableCardDeleteReq {

    @ApiModelProperty(value = "卡变编号", dataType = "java.lang.String", example = "ES000001", required = true)
    @NotEmpty(message = "卡变编号不能为空")
    private List<String> cardNumList;

}
