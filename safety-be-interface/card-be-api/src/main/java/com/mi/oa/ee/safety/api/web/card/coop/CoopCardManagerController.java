package com.mi.oa.ee.safety.api.web.card.coop;

import com.google.common.collect.Lists;
import com.mi.info.comb.neptune.client.NeptuneClient;
import com.mi.oa.ee.safety.api.converter.card.CardInfoVoConverter;
import com.mi.oa.ee.safety.api.errorcode.ApplyInterfaceErrorCodeEnum;
import com.mi.oa.ee.safety.api.errorcode.InterfaceErrorCodeEnum;
import com.mi.oa.ee.safety.api.model.coop.req.AuthLogRedoReq;
import com.mi.oa.ee.safety.api.model.coop.req.CardPageConditionReq;
import com.mi.oa.ee.safety.api.model.coop.req.CoopCardEditReq;
import com.mi.oa.ee.safety.api.model.coop.req.GroupPageConditionReq;
import com.mi.oa.ee.safety.api.model.coop.vo.CardDetailVo;
import com.mi.oa.ee.safety.api.model.coop.vo.OperateLogVo;
import com.mi.oa.ee.safety.api.model.shared.req.OperatePageConditionReq;
import com.mi.oa.ee.safety.application.dto.card.CardInfoDto;
import com.mi.oa.ee.safety.application.dto.card.CardPartnerApplyDto;
import com.mi.oa.ee.safety.application.dto.card.coop.CoopCardEditDto;
import com.mi.oa.ee.safety.application.dto.safety.SafetyCarrierDto;
import com.mi.oa.ee.safety.application.service.card.coop.CardApplyService;
import com.mi.oa.ee.safety.application.service.card.coop.CardService;
import com.mi.oa.ee.safety.common.dto.AccountModel;
import com.mi.oa.ee.safety.common.dto.AddressInfoDto;
import com.mi.oa.ee.safety.common.dto.CarrierGroupCityTreeDto;
import com.mi.oa.ee.safety.common.dto.OperateLogDto;
import com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceParkDto;
import com.mi.oa.ee.safety.common.enums.card.CardStatusEnum;
import com.mi.oa.ee.safety.infra.remote.sdk.AddressSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.SpaceSdk;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/16 16:19
 */
@Api(tags = "合作卡管理")
@WebLog
@RestController
@RequestMapping(value = {"/api/v1/card", "/api/v1/card/coop"})
@Slf4j
public class CoopCardManagerController {

    @Autowired
    CardService cardService;

    @Autowired
    CardInfoVoConverter voConverter;

    @Autowired
    AddressSdk addressSdk;

    @Autowired
    CardApplyService cardApplyService;

    @Autowired
    SpaceSdk spaceSdk;

    @Autowired
    IdmRemote idmRemote;

    @GetMapping("/list/address/{addrId}")
    @ApiOperation(value = "获取地区城市", httpMethod = "GET")
    public BaseResp<List<AddressInfoDto>> getAddressList(@PathVariable("addrId") String addrId) {
        List<AddressInfoDto> address = addressSdk.getAddressByParentId(addrId);
        return BaseResp.success(address);
    }

    @GetMapping("/list/parks")
    @ApiOperation(value = "获取当前城市下所有园区(确认页面)", httpMethod = "GET")
    public BaseResp<List<SafetySpaceParkDto>> getParkList(@RequestParam("cityId") Integer cityId) {
        List<SafetySpaceParkDto> spaceParks = spaceSdk.getListParksByCityId(cityId);
        return BaseResp.success(spaceParks);
    }

    @PostMapping("/condition/list")
    @ApiOperation(value = "根据条件获取工卡列表分页", httpMethod = "POST")
    public BaseResp<PageVO<CardDetailVo>> pageConditionCardList(@RequestBody CardPageConditionReq req) {
        PageModel<CardInfoDto> pageModel = cardService.pageConditionList(voConverter.pageReqToDto(req), true);
        return BaseResp.success(PageVO.build(pageModel, voConverter.toVoList(pageModel.getList())));
    }

    @GetMapping("/detail/info/{id}")
    @ApiOperation(value = "获取工卡信息详情", httpMethod = "GET")
    public BaseResp<CardDetailVo> getDetailCardInfo(@PathVariable("id") Long id) {
        return BaseResp.success(voConverter.toVo(cardService.getDetailCardInfo(id)));
    }

    @GetMapping("/gate/list/{carrierGroupCode}")
    @ApiOperation(value = "分页获取门禁列表", httpMethod = "GET")
    public BaseResp<PageVO<SafetyCarrierDto>> pageGateList(@PathVariable("carrierGroupCode") @NotNull(message =
            "{{{载体集编码不可为空}}}") String carrierGroupCode,
                                                           @RequestParam(value = "carrierName", required = false) String carrierName,
                                                           @RequestParam("pageNum") @NotNull Long pageNum,
                                                           @RequestParam("pageSize") @NotNull Long pageSize) {
        PageModel<SafetyCarrierDto> pageModel = cardService.getCarrierList(carrierGroupCode, carrierName, pageNum, pageSize);
        return BaseResp.success(PageVO.build(pageModel, pageModel.getList()));
    }

    @PostMapping("/condition/log/list")
    @ApiOperation(value = "条件分页获取门禁权限变更列表", httpMethod = "POST")
    public BaseResp<PageVO<OperateLogVo>> pageConditionOperateLogList(@RequestBody OperatePageConditionReq req) {
        PageModel<OperateLogDto> pageModel = cardService.pageListOperateLogs(req.getOperateStatus(),
                req.getOperateType(), Long.valueOf(req.getPageNum()), Long.valueOf(req.getPageSize()), req.getCardId());
        return BaseResp.success(PageVO.build(pageModel, voConverter.toVoListLogs(pageModel.getList())));
    }


    @GetMapping("/export/list")
    @ApiOperation(value = "异步导出合作卡列表", httpMethod = "GET")
    public BaseResp<Void> exportCardListV2(@RequestParam String queryData) {
        String uid = idmRemote.getLoginUid();
        int shardIndex = uid.hashCode() % 4;
        String shardKey = "COOP_CARD_LIST" + shardIndex; // 生成分片锁的键
        String token = RedisUtils.tryLock(shardKey, 30); // 使用分片锁
        if (StringUtils.isEmpty(token)) {
            String errDesc = NeptuneClient.getInstance()
                    .parseEntryTemplate(InterfaceErrorCodeEnum.INTERFACE_EXPORT_ERROR.getErrDesc()).replace("{", "").replace("}", "");
            throw new RuntimeException(errDesc);
        }
        cardService.asyncExportCardList(queryData);
        return BaseResp.success();
    }


    @GetMapping("/operate/exchange/{operateType}/{id}/{uid}")
    @ApiOperation(value = "工卡操作变更（销卡,恢复,删除）", httpMethod = "GET")
    public BaseResp<String> operateExchange(@PathVariable Integer operateType,
                                            @PathVariable Long id,
                                            @PathVariable String uid) {
        cardService.cardOperate(operateType, id, uid);
        return BaseResp.success();
    }

    @PostMapping("/log/redo")
    @ApiOperation(value = "重执行工卡操作日志变更(权限)", httpMethod = "POST")
    public BaseResp<String> operateLogRedo(@RequestBody AuthLogRedoReq req) {
        cardService.authorityRedo(voConverter.authReqToDto(req));
        return BaseResp.success();
    }

    @GetMapping("/group/list/{parkCode}")
    @ApiOperation(value = "获取园区下权限组", httpMethod = "GET")
    public BaseResp<List<SafetyCarrierGroupDto>> carrierGroupList(@PathVariable("parkCode") String parkCode) {
        return BaseResp.success(cardService.getCarrierGroups(parkCode, null));
    }

    @PostMapping("/page/group/list")
    @ApiOperation(value = "条件分页获取园区下权限组", httpMethod = "POST")
    public BaseResp<PageVO<SafetyCarrierGroupDto>> pageGroupList(@RequestBody GroupPageConditionReq groupPageConditionReq) {
        PageModel<SafetyCarrierGroupDto> pageModel =
                cardService.pageGroupConditionList(voConverter.toDto(groupPageConditionReq));
        return BaseResp.success(PageVO.build(pageModel, pageModel.getList()));
    }

    @GetMapping("/user/info")
    @ApiOperation(value = "模糊搜索用户信息", httpMethod = "GET")
    public BaseResp<List<AccountModel>> getFuzzyUser(@RequestParam("name") String name,
                                                     @RequestParam("isResponsible") Boolean isResponsible) {
        return BaseResp.success(cardService.findFuzzyUser(name, isResponsible));
    }

    @GetMapping("/info/{uid}")
    @ApiOperation(value = "根据uid查询工卡信息", httpMethod = "GET")
    public BaseResp<CardDetailVo> getCardInfoByUid(@PathVariable("uid") String uid) {
        CardDetailVo cardDetailVo = voConverter.toVo(cardService.getCardByUid(uid,
                Lists.newArrayList(CardStatusEnum.NOT_ACTIVE, CardStatusEnum.NOT_ACTIVE_FOR_TIME, CardStatusEnum.EXPIRED,
                        CardStatusEnum.USING)));
        if (ObjectUtils.isNotEmpty(cardDetailVo)) {
            CardPartnerApplyDto cardApply = cardApplyService.getDetailCardApply(cardDetailVo.getCardApplyId(), null);
            cardDetailVo.setPhotoUrl(cardApply.getPhotoUrl());
        } else {
            throw new BizException(ApplyInterfaceErrorCodeEnum.CARD_NOT_EXIST);
        }
        return BaseResp.success(cardDetailVo);
    }

    @GetMapping("/tree/group/list")
    @ApiOperation(value = "根据卡编码查对应权限的权限组集合(树状结构)", httpMethod = "GET")
    public BaseResp<List<CarrierGroupCityTreeDto>> getAccessCityByMedium(@RequestParam("mediumCode") String mediumCode) {
        List<CarrierGroupCityTreeDto> treeSafetyCarrierGroup = cardService.getAccessCityByMedium(mediumCode);
        return BaseResp.success(treeSafetyCarrierGroup);
    }


    @PostMapping("/page/rest/group")
    @ApiOperation(value = "查询已选后剩余的权限组")
    public BaseResp<PageVO<SafetyCarrierGroupDto>> pageRestGroupCodes(@RequestBody GroupPageConditionReq req) {
        PageModel<SafetyCarrierGroupDto> pageModel = cardService.getRestGroup(voConverter.toDto(req));
        return BaseResp.success(PageVO.build(pageModel, pageModel.getList()));
    }

    @PostMapping("/edit")
    @ApiOperation(value = "合作卡卡基础信息保存或编辑", httpMethod = "POST")
    public BaseResp<Void> edit(@RequestBody CoopCardEditReq req) {
        CoopCardEditDto editDto = voConverter.toCoopCardEditDto(req);
        cardService.editCardInfo(editDto);
        return BaseResp.success();
    }
}
