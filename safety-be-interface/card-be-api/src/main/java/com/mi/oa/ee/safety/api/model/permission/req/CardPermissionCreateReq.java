package com.mi.oa.ee.safety.api.model.permission.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/4/1 22:04
 */
@Data
@ApiModel(value = "CardPermissionCreateReq", description = "卡权限申请创建请求参数")
public class CardPermissionCreateReq {

    @ApiModelProperty(value = "被授权人uid", dataType = "java.lang.String", required = false)
    private String uid;

    @ApiModelProperty(value = "门禁类型", dataType = "java.lang.String", required = false)
    private String controlType;

    @ApiModelProperty(value = "权限生效开始时间", dataType = "java.lang.String", required = false)
    private ZonedDateTime startTime;

    @ApiModelProperty(value = "权限生效结束时间", dataType = "java.lang.String", required = false)
    private ZonedDateTime endTime;

    @ApiModelProperty(value = "申请原因", dataType = "java.lang.String", required = false)
    private String reasonName;

    @ApiModelProperty(value = "添加门禁权限包", dataType = "java.lang.String", required = false)
    private List<PermissionReq> permissionApplyGroupList;

    @ApiModelProperty(value = "是否永久", dataType = "java.lang.Boolean", required = false)
    private Boolean isForever;

    @NotEmpty(message = "{{{园区编码不能为空}}}")
    @ApiModelProperty(value = "申请园区编码", dataType = "java.lang.String", required = true)
    private String parkCode;

    @Data
    @ApiModel("添加权限查询")
    public static class PermissionReq {

        @NotEmpty(message = "{{{权限包code不能为空}}}")
        @ApiModelProperty(value = "权限包code", required = true)
        private String cardGroupCode;

        @ApiModelProperty(value = "{{{权限包名称}}}", required = true)
        private String cardGroupName;
    }
}
