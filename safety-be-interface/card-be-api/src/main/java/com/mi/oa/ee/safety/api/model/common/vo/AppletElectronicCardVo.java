package com.mi.oa.ee.safety.api.model.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @since 2024年3月27日 21:16:53
 */
@Data
@ApiModel(value = "小程序-电子工卡信息", description = "获取电子工卡状态（判断是否能开卡）")
public class AppletElectronicCardVo implements Serializable {
    private static final long serialVersionUID = -5126392420681523934L;

    @ApiModelProperty(value = "id", dataType = "java.lang.Long")
    private Long id;

    @ApiModelProperty(value = "是否可开电子卡类型 0：未开卡 1：开卡中 2：已开卡 3：已销卡 4：没有卡 5：异常 6：另一台手机已开卡", dataType = "java.lang.Integer")
    private Integer type;

    @ApiModelProperty(value = "是否可开电子卡类型描述", dataType = "java.lang.String")
    private String typeDesc;

    @ApiModelProperty(value = "电子卡类型 M:手机 W:手表 B:手环", dataType = "java.lang.String")
    private String electronType;

    @ApiModelProperty(value = "电子卡类型描述", dataType = "java.lang.String")
    private String electronTypeDesc;

    @ApiModelProperty(value = "电子卡模型", dataType = "java.lang.String")
    private String electronModel;

    @ApiModelProperty(value = "电子卡模型描述", dataType = "java.lang.String")
    private String electronModelDesc;

    @ApiModelProperty(value = "设备id", dataType = "java.lang.String")
    private String electronDeviceId;

    @ApiModelProperty(value = "照片地址", dataType = "java.lang.String")
    private String electronPhotoUrl;

    @ApiModelProperty(value = "创建时间")
    private ZonedDateTime createTime;
}
