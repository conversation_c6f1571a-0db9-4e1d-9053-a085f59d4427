package com.mi.oa.ee.safety.api.web.card.permission;

import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.api.converter.card.CardGroupConfigVoConverter;
import com.mi.oa.ee.safety.api.converter.card.CommonCardVoConverter;
import com.mi.oa.ee.safety.api.model.permission.req.CardGroupConfigAddReq;
import com.mi.oa.ee.safety.api.model.permission.req.CardGroupConfigEditReq;
import com.mi.oa.ee.safety.api.model.permission.req.CardGroupConfigGroupApplyUserReq;
import com.mi.oa.ee.safety.api.model.permission.req.CardGroupConfigGroupQueryReq;
import com.mi.oa.ee.safety.api.model.permission.req.CardGroupConfigOpReq;
import com.mi.oa.ee.safety.api.model.permission.req.CardGroupConfigQueryReq;
import com.mi.oa.ee.safety.api.model.permission.vo.CardGroupConfigGroupApplyUserVo;
import com.mi.oa.ee.safety.api.model.permission.vo.CardGroupConfigVo;
import com.mi.oa.ee.safety.api.model.shared.vo.SafetyRightVo;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigAddDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigEditDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigGroupApplyUserDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigGroupApplyUserQueryDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigQueryDto;
import com.mi.oa.ee.safety.application.service.card.permission.CardGroupConfigService;
import com.mi.oa.ee.safety.application.service.safety.SafetyCarrierGroupService;
import com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto;
import com.mi.oa.ee.safety.common.enums.safety.SafetySupplierTypeEnum;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/4/1 20:37
 */
@Api(tags = "卡权限申请配置")
@WebLog
@RestController
@RequestMapping("/api/v1/manager/card/permission/config")
@Slf4j
public class CardPermissionConfigurationAdminController {

    @Resource
    private CardGroupConfigService cardGroupConfigService;

    @Resource
    private CardGroupConfigVoConverter cardGroupConfigVoConverter;

    @Resource
    private SafetyCarrierGroupService safetyCarrierGroupService;

    @Resource
    private CommonCardVoConverter commonCardVoConverter;

    // 判断是否为权限包编码
    private static final Pattern PERMISSION_CODE_SPLIT_REGEX = Pattern.compile("^([Ss]\\d{5}|[Ss]\\d{12}|[Ww]\\d{14})$");

    @GetMapping("/page")
    @ApiOperation(value = "权限配置分页查询", httpMethod = "GET")
    public BaseResp<PageVO<CardGroupConfigVo>> page(CardGroupConfigQueryReq req) {
        // 正则切分权限包编码
        if (PERMISSION_CODE_SPLIT_REGEX.matcher(req.getCardGroupName()).matches()) {
            // 如果是权限包编码，则设置权限包编码
            req.setCardGroupCode(req.getCardGroupName());
            req.setCardGroupName(null);
        }

        CardGroupConfigQueryDto queryDto =  cardGroupConfigVoConverter.toQueryDto(req);
        PageModel<CardGroupConfigDto> page = cardGroupConfigService.page(queryDto);
        List<CardGroupConfigVo> resList = cardGroupConfigVoConverter.toCardGroupConfigVoList(page.getList());
        return BaseResp.success(PageVO.build(resList, page.getTotal(), page.getPageNum(), page.getPageSize()));
    }

    @PostMapping("/add")
    @ApiOperation(value = "权限配置新增", httpMethod = "POST")
    public BaseResp<Void> add(@RequestBody @Valid CardGroupConfigAddReq req) {
        CardGroupConfigAddDto addDto = cardGroupConfigVoConverter.toAddDto(req);
        cardGroupConfigService.add(addDto);
        return BaseResp.success();
    }

    @PostMapping("/edit")
    @ApiOperation(value = "权限配置修改", httpMethod = "POST")
    public BaseResp<CardGroupConfigVo> edit(@RequestBody @Valid CardGroupConfigEditReq req) {
        CardGroupConfigEditDto editDto = cardGroupConfigVoConverter.toEditDto(req);
        cardGroupConfigService.edit(editDto);
        return BaseResp.success();
    }

    @PostMapping("/detail")
    @ApiOperation(value = "查看配置详情", httpMethod = "POST")
    public BaseResp<CardGroupConfigVo> detail(@RequestBody @Valid CardGroupConfigOpReq req) {
        CardGroupConfigDto cardGroupConfigDto = cardGroupConfigService.detail(req.getCardGroupCode());
        CardGroupConfigVo cardGroupConfigVo = cardGroupConfigVoConverter.toCardGroupConfigVo(cardGroupConfigDto);
        return BaseResp.success(cardGroupConfigVo);
    }

    @PostMapping("/enable")
    @ApiOperation(value = "权限配置启用", httpMethod = "POST")
    public BaseResp<Void> enable(@RequestBody @Valid CardGroupConfigOpReq req) {
        cardGroupConfigService.enable(req.getCardGroupCode());
        return BaseResp.success();
    }

    @PostMapping("/disable")
    @ApiOperation(value = "权限配置禁用", httpMethod = "POST")
    public BaseResp<Void> disable(@RequestBody @Valid CardGroupConfigOpReq req) {
        cardGroupConfigService.disable(req.getCardGroupCode());
        return BaseResp.success();
    }

    @GetMapping("/carrierGroupList/list")
    @ApiOperation(value = "权限列表", httpMethod = "GET")
    public BaseResp<PageVO<SafetyRightVo>> carrierGroupList(@Valid CardGroupConfigGroupQueryReq req) {
        SafetyCarrierGroupDto query = cardGroupConfigVoConverter.toCarrierGroupQueryDto(req);
        query.setCarrierGroupTypeList(Lists.newArrayList(SafetySupplierTypeEnum.DOOR_PERMISSION.getCode(),
                SafetySupplierTypeEnum.GATE_PERMISSION.getCode()));
        PageModel<SafetyCarrierGroupDto> pageModel = safetyCarrierGroupService.pageConditionV1(query);
        return BaseResp.success(PageVO.build(commonCardVoConverter.toSafetyRightVo(pageModel.getList()),
                pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal()));
    }

    @GetMapping("/apply/user")
    @ApiOperation(value = "申请人信息列表", httpMethod = "GET")
    public BaseResp<PageVO<CardGroupConfigGroupApplyUserVo>> pageApplyUser(@Valid CardGroupConfigGroupApplyUserReq req) {
        CardGroupConfigGroupApplyUserQueryDto query = cardGroupConfigVoConverter.toCardGroupConfigGroupApplyUserDto(req);

        PageVO<CardGroupConfigGroupApplyUserDto> pageModel = cardGroupConfigService.pageApplyUser(query);
        return BaseResp.success(PageVO.build(commonCardVoConverter.toCardGroupConfigGroupApplyUserVoList(pageModel.getList()),
                pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal()));
    }
}
