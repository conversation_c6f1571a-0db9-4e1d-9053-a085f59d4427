package com.mi.oa.ee.safety.api.web.tool;

import com.mi.oa.ee.safety.application.service.card.permission.CardPermissionDataRefreshService;
import com.mi.oa.ee.safety.application.service.card.shared.CommonCardService;
import com.mi.oa.ee.safety.common.dto.CardOpenMessage;
import com.mi.oa.ee.safety.common.enums.card.CardApplyStatusEnum;
import com.mi.oa.ee.safety.infra.remote.sdk.notify.CardOpenNotifyProducerMessage;
import com.mi.oa.ee.safety.infra.repository.CardApplyRepository;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Collections;

@Api(tags = "工具接口")
@WebLog
@RestController
@Slf4j
@EnableAsync
@RequestMapping("/api/v1/card/tool")
public class CardToolController {

    @Resource
    private CardOpenNotifyProducerMessage notifySdk;

    @Resource
    private CommonCardService commonCardService;

    @Resource
    private CardApplyRepository cardApplyRepository;

    @Resource
    private CardPermissionDataRefreshService cardPermissionDataRefreshService;

    @GetMapping("/notify/open/card")
    public BaseResp<Void> notifyOpenCard(@RequestParam @NotNull Long applyId) {
        CardOpenMessage cardOpenMessage = new CardOpenMessage();
        cardOpenMessage.setCardApplyStatusEnum(CardApplyStatusEnum.COMPLETED);
        cardOpenMessage.setId(applyId);
        notifySdk.produceMessage(cardOpenMessage, String.valueOf(applyId));
        return BaseResp.success();
    }

    @PostMapping("/resync/user/rights")
    @ApiOperation("重推用户权限")
    public BaseResp<String> resyncUserRights(
            @ApiParam(value = "用户UID", required = true) @RequestParam @NotNull String uid,
            @ApiParam(value = "供应商编码（可选，为空则重推所有供应商权限）") @RequestParam(required = false) String supplierCode) {
        try {
            log.info("开始重推用户权限: uid={}, supplierCode={}", uid, supplierCode);
            
            // 调用CommonCardService的resyncUserAllRights方法重推权限
            boolean result = commonCardService.resyncUserAllRights(uid, supplierCode);
            
            if (result) {
                log.info("用户权限重推成功: uid={}, supplierCode={}", uid, supplierCode);
                return BaseResp.success("权限重推成功");
            } else {
                log.warn("用户权限重推失败或无权限需要重推: uid={}, supplierCode={}", uid, supplierCode);
                return BaseResp.success("无权限需要重推");
            }
        } catch (Exception e) {
            log.error("重推用户权限时发生异常: uid={}, supplierCode={}", uid, supplierCode, e);
            return BaseResp.error("重推权限失败: " + e.getMessage());
        }
    }

    @PostMapping("/refresh/apply/status")
    @ApiOperation("刷新申请单状态")
    public BaseResp<String> refreshApplyStatus(
            @ApiParam(value = "申请单ID", required = true) @RequestParam @NotNull Long applyId,
            @ApiParam(value = "新状态码", required = true) @RequestParam @NotNull Integer status) {
        try {
            log.info("开始刷新申请单状态: applyId={}, status={}", applyId, status);
            
            // 验证状态码是否有效
            CardApplyStatusEnum statusEnum = CardApplyStatusEnum.getEnumByCode(status);
            if (statusEnum == null) {
                log.error("无效的状态码: {}", status);
                return BaseResp.error("无效的状态码: " + status);
            }
            
            // 更新申请单状态
            cardApplyRepository.updateStatus(Collections.singletonList(applyId), status);
            
            log.info("申请单状态刷新成功: applyId={}, status={}", applyId, status);
            return BaseResp.success("状态刷新成功");
        } catch (Exception e) {
            log.error("刷新申请单状态时发生异常: applyId={}, status={}", applyId, status, e);
            return BaseResp.error("刷新状态失败: " + e.getMessage());
        }
    }

    @PostMapping("/refresh/permission/park-code")
    @ApiOperation("刷新权限申请单园区编码")
    public BaseResp<String> refreshPermissionParkCode(
            @ApiParam(value = "权限申请单号（可选，为空则全量刷新）") @RequestParam(required = false) String permissionApplyCode,
            @ApiParam(value = "分页大小（全量刷新时使用，默认100）") @RequestParam(required = false, defaultValue = "100") Integer pageSize) {
        try {
            log.info("开始刷新权限申请单园区编码: permissionApplyCode={}, pageSize={}", permissionApplyCode, pageSize);

            String result = cardPermissionDataRefreshService.refreshParkCode(permissionApplyCode, pageSize);

            log.info("权限申请单园区编码刷新完成: {}", result);
            return BaseResp.success(result);
        } catch (Exception e) {
            log.error("刷新权限申请单园区编码时发生异常: permissionApplyCode={}, pageSize={}", permissionApplyCode, pageSize, e);
            return BaseResp.error("刷新失败: " + e.getMessage());
        }
    }
}
