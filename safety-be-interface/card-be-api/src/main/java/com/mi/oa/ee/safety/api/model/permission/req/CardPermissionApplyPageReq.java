package com.mi.oa.ee.safety.api.model.permission.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/4/7 21:47
 */
@Data
@ApiModel(value = "CardPermissionApplyPageReq", description = "权限申请列表req对象")
public class CardPermissionApplyPageReq {

    @ApiModelProperty(value = "一级部门id", dataType = "java.lang.String", required = false)
    private String firstDeptId;

    @ApiModelProperty(value = "二级部门id", dataType = "java.lang.String", required = false)
    private String secondDeptId;

    @ApiModelProperty(value = "三级部门id", dataType = "java.lang.String", required = false)
    private String thirdDeptId;

    @ApiModelProperty(value = "四级部门id", dataType = "java.lang.String", required = false)
    private String fourthDeptId;

    @ApiModelProperty(value = "申请人姓名", dataType = "java.lang.String", required = false)
    private String applyUser;

    @ApiModelProperty(value = "申请方式", dataType = "java.lang.Integer", required = false)
    private Integer applyMethod;

    @ApiModelProperty(value = "申请单状态", dataType = "java.lang.Integer", required = false)
    private Integer permissionApplyStatus;

    @ApiModelProperty(value = "申请园区编码", dataType = "java.lang.String", required = false)
    private String parkCode;

    @ApiModelProperty(value = "权限类型", dataType = "java.lang.String", required = false)
    private String controlType;

    private Long pageNum = 1L;

    private Long pageSize = 10L;
}
