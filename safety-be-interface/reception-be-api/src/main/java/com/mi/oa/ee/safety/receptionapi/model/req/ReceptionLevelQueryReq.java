package com.mi.oa.ee.safety.receptionapi.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2023/5/8 20:55
 */
@Data
@ApiModel(description = "{{{接待等级}}}")
public class ReceptionLevelQueryReq {
    @ApiModelProperty(value = "接待等级名称")
    private String name;

    @ApiModelProperty(value = "接待等级状态")
    private Integer levelStatus;

    @ApiModelProperty(value = "当前页")
    private Long pageNum = 1L;

    @ApiModelProperty(value = "每页的个数")
    private Long pageSize = 10L;
}
