package com.mi.oa.ee.safety.receptionapi.web.admin;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.mi.oa.ee.safety.application.dto.reception.ReceptionApplyDto;
import com.mi.oa.ee.safety.application.dto.reception.ReceptionApplyEvaluateDto;
import com.mi.oa.ee.safety.application.dto.reception.ReceptionApplyHandlerDto;
import com.mi.oa.ee.safety.application.dto.reception.ReceptionConfigDto;
import com.mi.oa.ee.safety.application.dto.safety.SafetyOperateLogDto;
import com.mi.oa.ee.safety.application.dto.visitor.ApplyDto;
import com.mi.oa.ee.safety.application.dto.visitor.ApplyQueryDto;
import com.mi.oa.ee.safety.application.service.reception.ReceptionApplyService;
import com.mi.oa.ee.safety.application.service.safety.SafetyAsyncService;
import com.mi.oa.ee.safety.application.service.safety.SafetyOperateLogService;
import com.mi.oa.ee.safety.common.dto.PersonInfoModel;
import com.mi.oa.ee.safety.common.enums.AppCodeEnum;
import com.mi.oa.ee.safety.common.utils.ExcelUtils;
import com.mi.oa.ee.safety.receptionapi.converter.ReceptionApplyVoConverter;
import com.mi.oa.ee.safety.receptionapi.converter.ReceptionConfigVoConverter;
import com.mi.oa.ee.safety.receptionapi.errorcode.ReceptionInterfaceErrorCodeEnum;
import com.mi.oa.ee.safety.receptionapi.model.req.ApplyQueryReq;
import com.mi.oa.ee.safety.receptionapi.model.req.ReceptionApplyHandlerParamReq;
import com.mi.oa.ee.safety.receptionapi.model.req.ReceptionApplyParamReq;
import com.mi.oa.ee.safety.receptionapi.model.req.ReceptionApplyQueryReq;
import com.mi.oa.ee.safety.receptionapi.model.req.SafetyOperateLogReq;
import com.mi.oa.ee.safety.receptionapi.model.vo.ReceptionApplyCheckExportVo;
import com.mi.oa.ee.safety.receptionapi.model.vo.ReceptionApplyCheckPageVo;
import com.mi.oa.ee.safety.receptionapi.model.vo.ReceptionApplyDetailVo;
import com.mi.oa.ee.safety.receptionapi.model.vo.ReceptionApplyExportVo;
import com.mi.oa.ee.safety.receptionapi.model.vo.ReceptionApplyHandlerVo;
import com.mi.oa.ee.safety.receptionapi.model.vo.ReceptionApplyPageVo;
import com.mi.oa.ee.safety.receptionapi.model.vo.ReceptionConfigVo;
import com.mi.oa.ee.safety.receptionapi.model.vo.ReceptionEvaluationVo;
import com.mi.oa.ee.safety.receptionapi.model.vo.SafetyOperateLogVo;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2023/5/15 19:18
 */
@Api(tags = "接待-接待申请")
@WebLog
@Slf4j
@RestController("adminReceptionApply")
@RequestMapping("/api/v1/reception/admin")
public class ReceptionApplyController {
    @Autowired
    private ReceptionApplyService service;

    @Autowired
    private ReceptionApplyVoConverter converter;

    @Autowired
    SafetyOperateLogService safetyOperateLogService;

    @Autowired
    SafetyAsyncService safetyAsyncService;

    @Autowired
    private ReceptionConfigVoConverter configVoConverter;

    @ApiOperation(value = "接待申请数据分页")
    @GetMapping("/apply/page")
    public BaseResp<PageVO<ReceptionApplyPageVo>> applyPage(ApplyQueryReq queryReq) {
        ApplyDto applyDto = converter.toApplyDto(queryReq);
        ReceptionApplyDto receptionDto = converter.toReceptionDto(queryReq);
        ApplyQueryDto applyQueryDto = converter.toApplyQueryDto(queryReq);
        PageModel<ApplyDto> page = service.receptionPage(
                applyDto, applyQueryDto, receptionDto, queryReq.getPageNum(), queryReq.getPageSize()
        );
        return BaseResp.success(converter.toPageVo(page));
    }

    @ApiOperation(value = "接待申请数据导出")
    @GetMapping("/apply/export")
    public void applyExport(ApplyQueryReq queryReq, HttpServletRequest request, HttpServletResponse response) {
        ApplyDto applyDto = converter.toApplyDto(queryReq);
        ReceptionApplyDto receptionDto = converter.toReceptionDto(queryReq);
        ApplyQueryDto applyQueryDto = converter.toApplyQueryDto(queryReq);
        PageVO<ReceptionApplyExportVo> exportVo = converter.toExportVo(
                service.receptionPage(applyDto, applyQueryDto, receptionDto, 1L, 50000L)
        );

        ServletOutputStream os = null;
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", ExcelUtils.headStr("接待申请数据.xlsx", request));
            os = response.getOutputStream();
            EasyExcelFactory.write(os, ReceptionApplyExportVo.class).sheet("项目数据").doWrite(exportVo.getList());
        } catch (IOException e) {
            log.error("导出结果异常-{}", e.getMessage());
        } finally {
            IoUtil.close(os);
        }
    }

    @ApiOperation(value = "接待申请确认数据分页")
    @GetMapping("/apply/check/page")
    public BaseResp<PageVO<ReceptionApplyCheckPageVo>> applyCheckPage(ReceptionApplyQueryReq queryReq) {
        ApplyDto applyDto = converter.toApplyDto(queryReq);
        ReceptionApplyDto receptionDto = converter.toReceptionDto(queryReq);
        PageModel<ApplyDto> page = service.receptionCheckPage(
                applyDto, receptionDto, queryReq.getPageNum(), queryReq.getPageSize()
        );
        return BaseResp.success(converter.toCheckPageVo(page));
    }

    @ApiOperation(value = "接待申请确认数据导出")
    @GetMapping("/apply/check/export")
    public void applyCheckExport(ReceptionApplyQueryReq queryReq, HttpServletRequest request, HttpServletResponse response) {
        ApplyDto applyDto = converter.toApplyDto(queryReq);
        ReceptionApplyDto receptionDto = converter.toReceptionDto(queryReq);
        PageVO<ReceptionApplyCheckExportVo> exportVo = converter.toCheckExportVo(
                service.receptionCheckPage(applyDto, receptionDto, 1L, 50000L)
        );

        ServletOutputStream os = null;
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", ExcelUtils.headStr("接待确认数据.xlsx", request));
            os = response.getOutputStream();
            EasyExcelFactory.write(os, ReceptionApplyCheckExportVo.class).sheet("项目数据").doWrite(exportVo.getList());
        } catch (IOException e) {
            log.error("导出结果异常-{}", e.getMessage());
        } finally {
            IoUtil.close(os);
        }
    }

    @ApiOperation(value = "接待申请详情")
    @GetMapping("/apply/{receptionId}/detail")
    public BaseResp<ReceptionApplyDetailVo> applyDetail(@PathVariable("receptionId") @ApiParam(value = "接待id") Long receptionId) {
        ReceptionApplyDto workDetail = service.workDetail(receptionId);
        ReceptionApplyDetailVo detailVo = converter.dtoToDetailVo(workDetail);
        detailVo.setCurrentUserIsMange(service.currentUserIsManage(receptionId));

        return BaseResp.success(detailVo);
    }

    @ApiOperation(value = "接待申请调整")
    @PostMapping("/apply/{receptionId}/modify")
    public BaseResp applyModify(@PathVariable("receptionId") @ApiParam(value = "接待id") Long receptionId,
                                @RequestBody ReceptionApplyParamReq paramReq) {
        service.modify(receptionId, converter.paramToDto(paramReq));
        return BaseResp.success();
    }

    @ApiOperation(value = "接待申请服务")
    @PostMapping("/apply/{receptionId}/service")
    public BaseResp applyService(@PathVariable("receptionId") @ApiParam(value = "接待id") Long receptionId,
                                 @RequestBody ReceptionApplyParamReq paramReq) {
        service.service(receptionId, converter.paramToDto(paramReq));
        return BaseResp.success();
    }

    @ApiOperation(value = "接待申请一键完成全部接待")
    @PostMapping("/apply/{receptionId}/oneClick")
    public BaseResp oneClickCompletion(@PathVariable("receptionId") @ApiParam(value = "接待id") Long receptionId) {
        service.oneClick(receptionId);
        return BaseResp.success();
    }

    @ApiOperation(value = "接待申请复核通过")
    @PostMapping("/apply/{receptionId}/pass")
    public BaseResp applyPass(@PathVariable("receptionId") @ApiParam(value = "接待id") Long receptionId) {
        service.checkPass(receptionId);
        return BaseResp.success();
    }

    @ApiOperation(value = "接待申请复核驳回")
    @PostMapping("/apply/{receptionId}/reject")
    public BaseResp applyReject(@PathVariable("receptionId") @ApiParam(value = "接待id") Long receptionId,
                                @RequestBody ReceptionApplyParamReq paramReq) {
        service.checkReject(receptionId, paramReq.getRejectReason());
        return BaseResp.success();
    }

    @ApiOperation(value = "接待申请添加处理人")
    @PostMapping("/apply/{receptionId}/addHandler")
    public BaseResp applyAddHandler(@PathVariable("receptionId") @ApiParam(value = "接待id") Long receptionId,
                                    @RequestBody ReceptionApplyHandlerParamReq paramReq) {
        service.addHandler(receptionId, paramReq.getUidList());
        return BaseResp.success();
    }

    @ApiOperation(value = "接待申请配置项列表")
    @GetMapping("/apply/{receptionId}/configs")
    public BaseResp<List<ReceptionConfigVo>> applyConfigs(@PathVariable("receptionId") @ApiParam(value = "接待id") Long receptionId) {
        List<ReceptionConfigDto> applyConfigs = service.applyConfigs(receptionId);
        return BaseResp.success(configVoConverter.toVoList(applyConfigs));
    }

    @ApiOperation(value = "接待申请处理人列表")
    @GetMapping("/apply/{receptionId}/handlers")
    public BaseResp<List<ReceptionApplyHandlerVo>> applyHandlers(@PathVariable("receptionId") @ApiParam(value = "接待id") Long receptionId) {
        List<ReceptionApplyHandlerDto> applyHandlers = service.applyHandlers(receptionId);
        return BaseResp.success(converter.toHandlerList(applyHandlers));
    }

    @ApiOperation(value = "接待申请评价")
    @GetMapping("/apply/{receptionId}/evaluate")
    public BaseResp<ReceptionEvaluationVo> applyEvaluate(@PathVariable("receptionId") @ApiParam(value = "接待id") Long receptionId) {
        ReceptionApplyEvaluateDto evaluateDto = service.getEvaluation(receptionId);
        if (ObjectUtil.isEmpty(evaluateDto)) {
            throw new BizException(ReceptionInterfaceErrorCodeEnum.DATA_NOT_FOUND);
        }
        return BaseResp.success(converter.toEvaluateVo(evaluateDto));
    }

    @ApiOperation(value = "接待申请处理日志分页")
    @GetMapping("/apply/log/page")
    public BaseResp<PageVO<SafetyOperateLogVo>> applyLogPage(SafetyOperateLogReq safetyOperateLogReq) {
        SafetyOperateLogDto query = converter.toDto(safetyOperateLogReq);
        query.setAppCode(AppCodeEnum.VISITOR_RECEPTION.getAppCode());
        query.setRequestUrl("visit_reception_apply");
        PageModel<SafetyOperateLogDto> res = safetyOperateLogService.getListByBizId(query);
        List<SafetyOperateLogVo> safetyOperateLogVos = converter.toOperateLogList(res.getList());
        List<String> createUserUids = safetyOperateLogVos.stream()
                .map(SafetyOperateLogVo::getUpdateUser).collect(Collectors.toList());
        Map<String, List<PersonInfoModel>> map = safetyAsyncService.getMapByUidsForAsync(createUserUids);
        safetyOperateLogVos.forEach(
                safetyOperateLogVo -> {
                    List<PersonInfoModel> list = map.get(safetyOperateLogVo.getUpdateUser());
                    if (CollectionUtils.isNotEmpty(list)) {
                        safetyOperateLogVo.setUpdateUserName(list.get(0).getDisplayName());
                    }
                }
        );

        return BaseResp.success(PageVO.build(safetyOperateLogVos, res.getPageSize(), res.getPageNum(), res.getTotal()));
    }
}
