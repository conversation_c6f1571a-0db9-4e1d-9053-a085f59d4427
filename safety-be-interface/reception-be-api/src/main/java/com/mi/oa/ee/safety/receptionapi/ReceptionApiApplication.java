/*
 * Copyright (c) 2020. XiaoMi Inc.All Rights Reserved
 */
package com.mi.oa.ee.safety.receptionapi;


import com.mi.oa.infra.oaucf.EnableOACache;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 启动类
 *
 * <AUTHOR>
 * @date 2021/5/21 10:24 上午
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.mi.oa"})
@EnableAsync
@EnableOACache
public class ReceptionApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(ReceptionApiApplication.class, args);
    }

}
