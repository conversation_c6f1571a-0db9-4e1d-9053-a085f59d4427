package com.mi.oa.ee.safety.task.job;

import com.mi.oa.ee.safety.application.impl.safety.sync.SafetySupplierRightSyncService;
import com.mi.oa.ee.safety.common.dto.SafetySyncDto;
import com.mi.oa.ee.safety.common.enums.safety.SafetySupplierCodeEnum;
import com.mi.oa.ee.safety.domain.model.SafetyRightDo;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Component;

import java.util.List;
import javax.annotation.Resource;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/12/17 17:21
 */
@Component
@Slf4j
@PlanTask(name = "SafetyHollyWellSupplierSyncTask", quartzCron = "0 0/5 * * * ?", description = "霍尼1.0数据同步")
public class SafetyHollyWellSupplierSyncTask extends AbstractSafetySupplierSyncTask {

    @Resource
    private SafetySupplierRightSyncService safetySupplierRightSyncService;

    @Override
    protected String getTaskName() {
        return "SafetyHollyWellSupplierSyncTask";
    }

    @Override
    protected List<SafetyRightDo> getWaitSyncRightDoList(SafetySyncDto<T> safetySyncDto) {
        safetySyncDto.setSupplierCode(SafetySupplierCodeEnum.HOLLYWELL.getSupplierCode());
        return safetySupplierRightSyncService.getWaitSyncDoListWithSupplierCode(safetySyncDto);
    }

    @Override
    protected void preDealWaitSyncRightDoList(List<SafetyRightDo> rightDoList) {
        //补全部分right没有uid
        safetySupplierRightSyncService.fillRightUidList(rightDoList);
    }

    @Override
    protected String getSupplierCode() {
        return SafetySupplierCodeEnum.HOLLYWELL.getSupplierCode();
    }
}
