package com.mi.oa.ee.safety.task.job;

import com.mi.oa.ee.safety.domain.model.SafetyEventLogDo;
import com.mi.oa.ee.safety.domain.model.SafetyMediumDo;
import com.mi.oa.ee.safety.infra.remote.converter.SupplierDtoConverter;
import com.mi.oa.ee.safety.infra.remote.model.supplier.BaseQuery;
import com.mi.oa.ee.safety.infra.remote.model.supplier.SupplierDoorEventDto;
import com.mi.oa.ee.safety.infra.remote.sdk.supplier.impl.HikVisionAdapterSdk;
import com.mi.oa.ee.safety.infra.repository.SafetyEventLogRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyMediumRepository;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.plan.PlanThreadLocal;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工卡差旅开通权限
 *
 * <AUTHOR>
 * @date 2023/8/11 11:30
 */
@Component
@Slf4j
@PlanTask(name = "SafetyEventLogForHikVisionTask", quartzCron = "0 0/1 * * * ?", description = "同步海康事件日志")
public class SafetyEventLogForHikVisionTask implements PlanExecutor {

    @Resource
    HikVisionAdapterSdk hikVisionAdapterSdk;

    @Resource
    SafetyEventLogRepository safetyEventLogRepository;

    @Resource
    SafetyMediumRepository safetyMediumRepository;

    @Resource
    SupplierDtoConverter supplierDtoConverter;

    @Value("${supplier.hikvision.sync.minutes:5}")
    private Long minutes;

    @Override
    public void execute() {
        try {
            //从miplan获取对应的开始时间yyyy-MM-dd HH:mm:ss
            String startTime = PlanThreadLocal.getRequestData();
            if (StringUtils.isNotBlank(startTime)) {
                startTime = ZonedDateTimeUtils.formatISO8601(ZonedDateTimeUtils.toZonedDateTime(startTime));
            } else {
                Long minutesLong = minutes + OAUCFCommonConstants.LONG_TEN;
                //默认使用配置的时间往前加10分钟
                startTime = ZonedDateTimeUtils.formatISO8601(ZonedDateTime.now().minusMinutes(minutesLong));
            }
            Date start = new Date();
            PlanThreadLocal.updateProgress(0, "同步海康事件日志开始");
            List<SupplierDoorEventDto> list = Lists.newArrayList();
            Integer pageNum = OAUCFCommonConstants.INT_ONE, count = OAUCFCommonConstants.INT_ZERO;
            do {
                BaseQuery<String> query = new BaseQuery<>();
                query.setPageNum(pageNum);
                query.setPageSize(1000);
                query.setQueryStartTime(startTime);
                query.setQueryEndTime(ZonedDateTimeUtils.formatISO8601(ZonedDateTime.now()));
                BaseResp<List<SupplierDoorEventDto>> resp = hikVisionAdapterSdk.querySupplierDoorEventByCondition(query);
                if (OAUCFCommonConstants.INT_ZERO.equals(resp.getCode())) {
                    list = resp.getData();
                    if (CollectionUtils.isNotEmpty(list)) {
                        list = list.stream().filter(supplierDoorEventDto ->
                                StringUtils.isNotBlank(supplierDoorEventDto.getUserId())).collect(Collectors.toList());
                        count += list.size();
                        saveSafetyEventLog(list);
                    }
                }
                PlanThreadLocal.updateProgress(pageNum, "同步海康事件日志进行中，处理数量" + count);
                pageNum++;
            } while (CollectionUtils.isNotEmpty(list));
            Date end = new Date();
            PlanThreadLocal.updateProgress(100, "同步海康事件日志完成，消耗时间：" + (end.getTime() - start.getTime()));
        } catch (Exception e) {
            PlanThreadLocal.updateProgress(100, e.getMessage());
            log.error("SafetyEventLogForHikVisionTask error", e);
        }
    }

    private void saveSafetyEventLog(List<SupplierDoorEventDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        try {
            List<SafetyEventLogDo> safetyEventLogDoList = supplierDtoConverter.toSafetyEventLogDoList(list);
            safetyEventLogRepository.batchSave(safetyEventLogDoList);
        } catch (Exception e) {
            log.error("safetyEventLogRepository batchSave error", e);
        }

        try {
            List<SafetyMediumDo> safetyMediumDoList = supplierDtoConverter.toSafetyMediumDoList(list);
            safetyMediumRepository.batchSaveOrUpdate(safetyMediumDoList);
        } catch (Exception e) {
            log.error("safetyMediumRepository batchSave error", e);
        }

    }
}
