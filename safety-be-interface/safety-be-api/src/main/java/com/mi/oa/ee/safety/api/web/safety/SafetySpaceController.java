package com.mi.oa.ee.safety.api.web.safety;

import com.mi.oa.ee.safety.api.converter.safety.CommonVoConverter;
import com.mi.oa.ee.safety.api.model.safety.vo.CountryInfoVO;
import com.mi.oa.ee.safety.application.dto.common.CountryInfoDto;
import com.mi.oa.ee.safety.application.service.common.CommonService;
import com.mi.oa.ee.safety.common.dto.AddressInfoDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceBuildingDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceFloorDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceParkDto;
import com.mi.oa.ee.safety.infra.remote.sdk.AddressSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.SpaceSdk;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 安防-空间相关接口
 *
 * <AUTHOR>
 * @date 2022/12/21 16:43
 */
@Api(tags = "安防-空间相关接口")
@WebLog
@Slf4j
@RestController
@RequestMapping("/api/v1/safety/space")
public class SafetySpaceController {

    @Autowired
    SpaceSdk spaceSdk;

    @Autowired
    AddressSdk addressSdk;

    @Resource
    private CommonVoConverter commonVoConverter;

    @Resource
    private CommonService commonService;

    @GetMapping("/list/parks/fuzzy")
    @ApiOperation(value = "模糊搜索园区信息", httpMethod = "GET")
    public BaseResp<List<SafetySpaceParkDto>> getParkList(@RequestParam(value = "name", required = false) String name) {
        List<SafetySpaceParkDto> spaceParks = spaceSdk.getParkCodesByFuzzyName(name);
        return BaseResp.success(spaceParks);
    }

    @GetMapping("/fuzzy/list/address")
    @ApiOperation(value = "模糊搜索地区信息", httpMethod = "GET")
    public BaseResp<List<CountryInfoVO>> findFuzzyAddress(@RequestParam(value = "addressName", required = false) String addressName) {
        List<CountryInfoDto> countryInfoDtoList = commonService.fuzzyByAddressName(addressName);
        return BaseResp.success(commonVoConverter.toCountryInfoVoList(countryInfoDtoList));
    }

    @GetMapping("/list/address/{addrId}")
    @ApiOperation(value = "获取地区城市", httpMethod = "GET")
    public BaseResp<List<AddressInfoDto>> getAddressList(@PathVariable("addrId") String addrId) {
        List<AddressInfoDto> address = addressSdk.getAddressByParentId(addrId);
        return BaseResp.success(address);
    }

    @GetMapping("/list/parks/all")
    @ApiOperation(value = "获取当前城市下所有园区", httpMethod = "GET")
    public BaseResp<List<SafetySpaceParkDto>> getAllParkList(@RequestParam("cityId") Integer cityId) {
        List<SafetySpaceParkDto> spaceParks = spaceSdk.getAllParksByCityId(cityId);
        return BaseResp.success(spaceParks);
    }

    @GetMapping("/list/parks")
    @ApiOperation(value = "获取当前城市下有效的园区", httpMethod = "GET")
    public BaseResp<List<SafetySpaceParkDto>> getParkList(@RequestParam("cityId") Integer cityId) {
        List<SafetySpaceParkDto> spaceParks = spaceSdk.getListParksByCityId(cityId);
        return BaseResp.success(spaceParks);
    }

    @GetMapping("/list/buildings")
    @ApiOperation(value = "获取当前园区下所有楼栋", httpMethod = "GET")
    public BaseResp<List<SafetySpaceBuildingDto>> getBuildingList(@RequestParam("parkCode") String parkCode) {
        SafetySpaceDto safetySpace = spaceSdk.getBuildingsByParkCode(parkCode);
        return BaseResp.success(safetySpace.getBuildings());
    }

    @GetMapping("/list/floors")
    @ApiOperation(value = "获取当前楼栋下所有楼层", httpMethod = "GET")
    public BaseResp<List<SafetySpaceFloorDto>> getFloorList(@RequestParam("buildingCode") String buildingCode) {
        SafetySpaceDto safetySpace = spaceSdk.getFloorsByBuildingCode(buildingCode);
        return BaseResp.success(safetySpace.getFloors());
    }

    @GetMapping("/address/country")
    @ApiOperation(value = "获取国家接口(确认页面)", httpMethod = "GET")
    public BaseResp<List<CountryInfoVO>> getCountryList() {
        List<CountryInfoDto> countryInfoDtoList = commonService.countryList();
        return BaseResp.success(commonVoConverter.toCountryInfoVoList(countryInfoDtoList));
    }
}
