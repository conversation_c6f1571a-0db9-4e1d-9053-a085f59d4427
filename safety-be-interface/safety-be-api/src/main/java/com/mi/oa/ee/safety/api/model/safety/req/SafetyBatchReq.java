package com.mi.oa.ee.safety.api.model.safety.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @date 2022/11/18 15:56
 */
@Data
@ApiModel(value = "SafetyBatchReq", description = "批量查询或删除对象")
public class SafetyBatchReq {

    @ApiModelProperty(value = "编码列表", dataType = "java.util.List")
    List<String> codes;

}
