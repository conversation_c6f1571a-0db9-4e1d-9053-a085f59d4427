package com.mi.oa.ee.safety.api.model.safety.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 供应商的视图对象
 *
 * <AUTHOR>
 * @date 2022/11/16 19:09
 */
@Data
@ApiModel(value = "SafetySupplierVo", description = "安防供应商信息")
public class SafetySupplierVo {

    /**
     * 供应商编码 supplier_code
     */
    @ApiModelProperty(value = "供应商编码", dataType = "java.lang.String")
    private String supplierCode;

    /**
     * 供应商名称 name
     */
    @ApiModelProperty(value = "供应商名称", dataType = "java.lang.String")
    private String name;

    /**
     * 供应商描述 description
     */
    @ApiModelProperty(value = "供应商描述", dataType = "java.lang.String")
    private String description;

    /**
     * 0：默认  1：门禁供应商   2：闸机供应商   3：抬杆供应商 supplier_type
     */
    @ApiModelProperty(value = "供应商类型", dataType = "java.lang.Integer")
    private Integer supplierType;


    @ApiModelProperty(value = "供应商类型描述", dataType = "java.lang.String")
    private String supplierTypeDesc;

    /**
     * 供应商负责人 owner
     */
    @ApiModelProperty(value = "供应商负责人uid", dataType = "java.lang.String")
    private String owner;

    /**
     * 供应商负责人 owner
     */
    @ApiModelProperty(value = "供应商负责人姓名", dataType = "java.lang.String")
    private String ownerName;

    /**
     * 备注 remark
     */
    @ApiModelProperty(value = "{{{备注}}}", dataType = "java.lang.String")
    private String remark;

    /**
     * 租户 tenant_code
     */
    @ApiModelProperty(value = "租户编码", dataType = "java.lang.String")
    private String tenantCode;

}
