package com.mi.oa.ee.safety.api.converter.safety;

import com.mi.oa.ee.safety.api.model.safety.vo.SafetyPersonVo;
import com.mi.oa.ee.safety.application.dto.safety.SafetyPersonDto;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @date 2025/02/11 17:47
 */
@Mapper(componentModel = "spring")
public interface SafetyPersonVoConverter {
    /**
     * dto -> vo
     *
     * @param safetyPersonDto
     * @return com.mi.oa.ee.safety.api.model.safety.vo.SafetyPersonVo
     * <AUTHOR>
     * @date 2025/02/11 17:47
     */
    SafetyPersonVo dtoToVo(SafetyPersonDto safetyPersonDto);
}
