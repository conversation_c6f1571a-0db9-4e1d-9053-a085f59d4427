package com.mi.oa.ee.safety.common.enums.safety;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 安防介质类型
 *
 * <AUTHOR>
 * @date 2023/4/3 10:25
 */
@Getter
@AllArgsConstructor
public enum SafetyMediumTypeEnum {

    DEFAULT(0, "{{{默认}}}", Lists.newArrayList()),
    CARD(1, "{{{工卡}}}", Lists.newArrayList(SafetySupplierTypeEnum.DOOR_PERMISSION.getCode())),
    VISIT_CODE(2, "{{{访问码}}}", Lists.newArrayList(SafetySupplierTypeEnum.VIRTUAL_GATE_PERMISSION.getCode(), SafetySupplierTypeEnum.GATE_PERMISSION.getCode())),
    PLATE_NUMBER(3, "{{{车牌}}}", Lists.newArrayList(SafetySupplierTypeEnum.LIFT_ROD_PERMISSION.getCode())),
    FACE(4, "{{{人脸}}}", Lists.newArrayList(SafetySupplierTypeEnum.DOOR_PERMISSION.getCode()));

    private Integer code;

    private String desc;

    private List<Integer> carrierTypeList;

    public static SafetyMediumTypeEnum getEnumByCode(Integer code) {
        for (SafetyMediumTypeEnum s : SafetyMediumTypeEnum.values()) {
            if (s.getCode().equals(code)) {
                return s;
            }
        }
        return null;
    }
}
