package com.mi.oa.ee.safety.common.utils;

import java.math.BigInteger;

/**
 * @version 1.0
 * @className HexUtil
 * @description:
 * @author: chenying
 * @date: 2019/5/9 11:48
 **/
public class HexUtil {
    /**
     * 十进制转为十六进制
     */
    public static String tenToHex(String hex) {
        return new BigInteger(hex, 10).toString(16);
    }

    /**
     * 十六进制转为十进制
     */
    public static String hexToTen(String hex) {
        return new BigInteger(hex, 16).toString(10);
    }
}
