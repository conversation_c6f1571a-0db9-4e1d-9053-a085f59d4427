package com.mi.oa.ee.safety.common.dto;

import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2022/8/26 20:39
 */
@Data
public class SafetyUmsNotifyDto extends DTO {

    private Long id;

    /**
     * 应用编码 app_code
     */
    private String appCode;

    /**
     * uid 或者邮箱，手机号，没有uid的时候可填装手机号或邮箱 receiver
     */
    private String receiver;

    /**
     * 消息配置ID config_id
     */
    private Long configId;

    /**
     * 消息发送时间 send_time
     */
    private ZonedDateTime sendTime;

    /**
     * 消息发送状态，0：未发送，1：已发送 send_status
     */
    private Integer sendStatus;

    /**
     * 租户 tenant_code
     */
    private String tenantCode;

    /**
     * 消息参数 params
     */
    private String params;
}
