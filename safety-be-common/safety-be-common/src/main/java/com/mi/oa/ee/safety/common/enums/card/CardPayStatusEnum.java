package com.mi.oa.ee.safety.common.enums.card;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/10/21 11:28
 */
@Getter
@AllArgsConstructor
public enum CardPayStatusEnum {
    WAIT_PAY(0, "{{{待支付}}}"),
    PAYED(1, "{{{已支付}}}"),
    PAY_FAILED(2, "{{{支付失败}}}"),
    WAIT_REFUND(3, "{{{待退款}}}"),
    REFUND_APPLIED(4, "{{{退款中}}}"),
    REFUND(5, "{{{已退款}}}"),
    ;

    private Integer code;
    private String desc;
}
