package com.mi.oa.ee.safety.common.dto;

import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/12/2 11:59
 */
@Data
public class CardPageConditionDto {

    /**
     * 省份id
     */
    private Long provinceId;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 园区编码
     */
    private String parkCode;

    /**
     * 载体集编码
     */
    private String carrierGroupCode;

    /**
     * 物理卡号
     */
    private String mediumPhysicsCode;

    /**
     * 加密卡号
     */
    private String mediumEncryptCode;

    /**
     * 一级部门id
     */
    private String firstDeptId;

    /**
     * 二级部门id
     */
    private String secondDeptId;

    /**
     * 三级部门id
     */
    private String thirdDeptId;

    /**
     * 四级部门id
     */
    private String fourthDeptId;

    /**
     * 工卡状态
     */
    private Integer cardStatus;

    /**
     * 工卡状态
     */
    private List<Integer> cardStatusList;

    /**
     * 责任人id
     */
    private String responsible;

    /**
     * uid
     */
    private String uid;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 介质编码
     */
    private String mediumCode;

    private Long pageNum = 1L;

    private Long pageSize = 10L;

    /**
     * 共卡类型 1：合作 2：临时 3正式 4 物业
     */
    private Integer cardType;

    /**
     * 员工工号
     */
    private String employeeNo;

    /**
     * 申请单状态 0无 1有
     */
    private Integer hasSpecialAuth;


    //离职日期
    private ZonedDateTime preLeaveDate;

    private String phone;

    private ZonedDateTime preLeaveDateFrom;

    private ZonedDateTime preLeaveDateTo;

    private List<String> preLeaveUidList;

    // 介质编码List
    List<String> mediumCodes;

    /**
     * 过期时间开始
     */
    private ZonedDateTime expireTimeFrom;

    /**
     * 过期时间结束
     */
    private ZonedDateTime expireTimeTo;
}
