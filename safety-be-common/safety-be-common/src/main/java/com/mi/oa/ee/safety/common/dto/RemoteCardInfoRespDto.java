package com.mi.oa.ee.safety.common.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: chenmingke
 * @Date: 2024/9/4 上午9:45
 * @Desc: 老系统同步卡信息
 */
@Data
public class RemoteCardInfoRespDto implements Serializable {
    /**
     * 物理卡号
     */
    private String mediumPhysicsCard;

    /**
     * 加密卡号
     */
    private String mediumEncryptCard;

    /**
     * 卡编号
     */
    private String cardNum;

    /**
     * 用户名(账号)
     */
    private String userName;

    /**
     * 供应商测权限组编码
     */
    private List<String> supplierAccessCodes;

    /**
     * 1:合作卡 2:临时卡 3:正式卡
     */
    private Integer cardType;

    private String endTime;

    private String photoUrl;
}
