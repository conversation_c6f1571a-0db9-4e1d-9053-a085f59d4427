package com.mi.oa.ee.safety.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/5/19 15:09
 */
@Getter
@AllArgsConstructor
public enum SendObjectEnum {

    VISITOR("visitor", "{{{发送对象是访客}}}"),
    OVERSEAS_VISITOR("overseas_visitor", "{{{发送对象是海外访客访客}}}"),
    PARK_ADMIN("parkAdmin", "{{{发送对象是园区管理员}}}"),
    RECEIVER("receiver", "{{{发送对象是接待人}}}"),
    APPLICANT("applicant", "{{{发送对象是申请人}}}"),
    ;

    private String code;

    private String desc;

    public static SendObjectEnum getNowEnumByCode(String code) {
        for (SendObjectEnum value : SendObjectEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
