package com.mi.oa.ee.safety.infra.repository;

import com.mi.oa.ee.safety.domain.model.CardPermissionApplyDo;
import com.mi.oa.ee.safety.domain.model.CardPermissionApplyGroupDo;
import com.mi.oa.ee.safety.domain.query.card.CardGroupConfigGroupApplyUserQuery;
import com.mi.oa.ee.safety.domain.query.card.CardPermissionApplyQuery;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/4/3 20:21
 */
public interface CardPermissionApplyRepository {

    void create(CardPermissionApplyDo cardPermissionApplyDo);

    CardPermissionApplyDo findById(Long id);

    List<CardPermissionApplyGroupDo> findPermissionApplyGroupListByCode(String permissionApplyCode);

    List<CardPermissionApplyGroupDo> findPermissionApplyGroupListByCodeWithIsDeleted(String permissionApplyCode);

    void updateByCancel(CardPermissionApplyDo cardPermissionApplyDo);

    List<CardPermissionApplyGroupDo> findAllPermissionGroupForOnceOpened(CardPermissionApplyDo cardPermissionApplyDo);

    List<CardPermissionApplyDo> findPermissionApplyListByCode(List<String> permissionApplyCodeList);

    PageModel<CardPermissionApplyDo> pageApply(CardPermissionApplyQuery cardPermissionApplyQuery);

    CardPermissionApplyDo findByBpmCode(String businessKey);

    void updateByReject(CardPermissionApplyDo cardPermissionApplyDo);

    void updateByPass(CardPermissionApplyDo cardPermissionApplyDo);

    /**
     * 批量更新关系表
     * @param relationList
     */
    void updateRelation(List<CardPermissionApplyGroupDo> relationList);

    /**
     * 查询关系表列表
     * @param businessKey 授权流程
     * @return
     */
    List<CardPermissionApplyGroupDo> findRelationByBpmCode(String businessKey);

    /**
     * 查询权限申请单
     * @param applyCode
     * @return
     */
    CardPermissionApplyDo findByApplyCode(String applyCode);

    /**
     * 更新申请单状态
     * @param applicantApplyDo
     */
    void updateByFinal(CardPermissionApplyDo applicantApplyDo);

    /**
     * 查询权限申请单用户数量
     * @param groupCodeList
     * @return
     */
    Map<String, Long> findPermissionApplyUserCount(List<String> groupCodeList);

    /**
     * 分页查询指定组的申请人员信息
     * @param query
     * @return
     */
    PageVO<CardPermissionApplyGroupDo> pageApplyUser(CardGroupConfigGroupApplyUserQuery query);

    List<CardPermissionApplyDo> findExpiredPermissionList();

    void updateById(CardPermissionApplyDo cardPermissionApplyDo);

    void batchSaveRelation(List<CardPermissionApplyGroupDo> cardPermissionApplyGroupDoList);

    void batchDeleteRelation(List<CardPermissionApplyGroupDo> cardPermissionApplyDoList);

    List<CardPermissionApplyGroupDo> findPermissionApplyGroupListByUidAndSourceAndCode(String uid, Integer code,
                                                                                       List<String> groupCodeList);

    List<CardPermissionApplyDo> findWillExpiredPermissionList();

    void editNotifyStatus(CardPermissionApplyDo cardPermissionApplyDo);

    List<CardPermissionApplyDo> findPreExpireApplyList();

    /**
     * 分页查询权限申请单（用于数据刷新）
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param permissionApplyCode 权限申请单号（可选）
     * @return 分页结果
     */
    PageModel<CardPermissionApplyDo> pageForRefresh(Integer pageNum, Integer pageSize, String permissionApplyCode);
}
