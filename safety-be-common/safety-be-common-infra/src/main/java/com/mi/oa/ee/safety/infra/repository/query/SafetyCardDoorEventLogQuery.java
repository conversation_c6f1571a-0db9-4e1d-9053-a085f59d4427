package com.mi.oa.ee.safety.infra.repository.query;

import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/7/11 10:20
 */
@Data
public class SafetyCardDoorEventLogQuery {

    /**
     * 当前页
     */
    private Long pageNum;

    /**
     * 页面大小
     */
    private Long pageSize;

    /**
     * 开始时间
     */
    private ZonedDateTime startTime;

    /**
     * 结束时间
     */
    private ZonedDateTime endTime;

    /**
     * 账号
     */
    private String userName;

    /**
     * 加密卡号
     */
    private String mediumEncryptCode;
}
