package com.mi.oa.ee.safety.infra.repository;

import com.mi.oa.ee.safety.domain.model.VisitorReceptionGuestDo;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2023/5/10 16:58
 */
public interface VisitorReceptionGuestRepository {
    VisitorReceptionGuestDo find(Long id);

    VisitorReceptionGuestDo find(VisitorReceptionGuestDo guestDo);

    List<VisitorReceptionGuestDo> list(VisitorReceptionGuestDo guestDo);

    List<VisitorReceptionGuestDo> listByIds(List<Long> ids);

    PageModel<VisitorReceptionGuestDo> page(VisitorReceptionGuestDo guestDo, Long pageNum, Long pageSize);

    VisitorReceptionGuestDo create(VisitorReceptionGuestDo guestDo);

    void update(VisitorReceptionGuestDo guestDo);
}
