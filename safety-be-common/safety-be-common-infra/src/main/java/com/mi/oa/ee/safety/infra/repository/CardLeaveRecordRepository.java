package com.mi.oa.ee.safety.infra.repository;

import com.mi.oa.ee.safety.domain.model.CardLeaveRecordDo;
import com.mi.oa.ee.safety.infra.repository.query.CardLeaveRecordQuery;

import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/8/9 21:50
 */
public interface CardLeaveRecordRepository {

    void save(CardLeaveRecordDo cardLeaveRecordDo);

    CardLeaveRecordDo getOneByPsLeaveNumber(String psLeaveNumber);

    void delete(CardLeaveRecordDo cardLeaveRecordDo);

    List<CardLeaveRecordDo> findLeaveRecordByUidList(List<String> uidList);

    CardLeaveRecordDo findPreLeaveRecordByUid(String uid);

    void updateById(CardLeaveRecordDo cardLeaveRecord);

    List<CardLeaveRecordDo> queryLeaveRecordList(CardLeaveRecordQuery leaveRecordQuery);
}
