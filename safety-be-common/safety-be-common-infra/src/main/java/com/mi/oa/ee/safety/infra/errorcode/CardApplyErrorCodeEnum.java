package com.mi.oa.ee.safety.infra.errorcode;

import com.mi.oa.ee.safety.common.enums.BizCodeEnum;
import com.mi.oa.infra.oaucf.core.exception.InfraErrorCode;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/28 20:38
 */
@AllArgsConstructor
public enum CardApplyErrorCodeEnum implements InfraErrorCode {

    UPDATE_ERROR(1, "{{{更新失败}}}"),
    BATCH_UPDATE_APPROVAL_ERROR(2, "{{{批量更新同意错误}}}"),
    PARAM_ERROR(3, "{{{参数错误}}}"),
    CARD_DELETE_FAIL(4, "{{{删除失败}}}"),
    CARD_SAVE_FAIL_ERROR(5, "{{{工卡保存失败}}}"),
    MEDIUM_DELETE_ERROR(6, "{{{介质删除异常}}}"),
    MEDIUM_REPEAT(7, "物理卡号/加密卡号重复"),

    VALIDATE_TIME_OUT_RANGE(8, "{{{有效期时间不能超过用户合作周期}}}"),
    TEMP_CARD_NOT_EXIST(9, "{{{临时卡不存在}}}"),
    EMP_CARD_NOT_EXIST(10, "{{{正式卡不存在}}}"),

    APPLY_ZONE_CODE_NOT_EXIST(11, "{{{申请单中当前地区编码不存在}}}"),

    NAME_LENGTH_ERROR(12, "{{{拼音姓名长度超过限制，最长30位}}}"),
    ;

    private Integer code;

    private String desc;

    @Override
    public int getBizCode() {
        return BizCodeEnum.CARD.getBizCode();
    }

    @Override
    public int getErrorCode() {
        return code;
    }

    @Override
    public String getErrDesc() {
        return desc;
    }
}
