package com.mi.oa.ee.safety.infra.remote.model;

import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/13 21:49
 */
@Data
public class IdmUserModel extends DTO {

    private String uid;
    private String uniqueValue;
    private String userChannel;
    private String fullName;
    private String displayName;
    private String deptId;
    private String deptName;
    private String zoneCode;
    private String mobile;
    private String personalEmail;
    private List<UserBindInfo> userBindVoList;
}
