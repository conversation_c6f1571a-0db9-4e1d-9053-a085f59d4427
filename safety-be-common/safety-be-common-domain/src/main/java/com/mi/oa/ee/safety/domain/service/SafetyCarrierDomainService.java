package com.mi.oa.ee.safety.domain.service;

import com.mi.oa.ee.safety.domain.model.SafetyCarrierDo;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.util.List;

/**
 * 安防载体领域服务
 *
 * <AUTHOR>
 * @date 2022/8/18 16:51
 */
public interface SafetyCarrierDomainService {

    /**
     * 填装安防载体的空间信息
     *
     * @param safetyCarrierDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/11 20:28
     */
    void fillSpaceInfo(SafetyCarrierDo safetyCarrierDo);

    /**
     * 填装安防载体基本信息
     *
     * @param safetyCarrierDo
     * @return void
     * <AUTHOR>
     * @date 2023/9/11 20:28
     */
    void fillCarrierInfo(SafetyCarrierDo safetyCarrierDo);

    /**
    * 批量填装安防载体的分类名称
    * @param safetyCarrierDoList
    * @return        void
    * <AUTHOR>
    * @date          2023/9/11 20:29
    */
    void fillClassNameForList(List<SafetyCarrierDo> safetyCarrierDoList);

    /**
    * 批量填装安防载体全部信息
    * @param safetyCarrierDoList
    * @return        void
    * <AUTHOR>
    * @date          2023/9/11 20:37
    */ 
    void fillSafetyCarrierDoForList(List<SafetyCarrierDo> safetyCarrierDoList);

    /**
    * 更新前校验
    * @param safetyCarrierDO
    * @return        void
    * <AUTHOR>
    * @date          2023/9/11 20:37
    */ 
    void checkBeforeUpdate(SafetyCarrierDo safetyCarrierDO);

    /**
    * 删除前检查
    * @param safetyCarrierDO
    * @return        void
    * <AUTHOR>
    * @date          2023/9/11 20:38
    */ 
    void checkBeforeDelete(SafetyCarrierDo safetyCarrierDO);
}
