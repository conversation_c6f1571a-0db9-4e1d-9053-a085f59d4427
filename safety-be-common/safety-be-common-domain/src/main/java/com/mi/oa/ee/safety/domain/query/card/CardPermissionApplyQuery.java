package com.mi.oa.ee.safety.domain.query.card;

import lombok.Data;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/4/7 22:00
 */
@Data
public class CardPermissionApplyQuery {

    private String firstDeptId;

    private String secondDeptId;

    private String thirdDeptId;

    private String fourthDeptId;

    private String disPlayName;

    private Integer applyMethod;

    private Integer permissionApplyStatus;

    private String applyUser;

    private Long pageNum;

    private Long pageSize;

    /**
     * 申请园区编码
     */
    private String parkCode;

    /**
     * 权限类型
     */
    private String controlType;
}
