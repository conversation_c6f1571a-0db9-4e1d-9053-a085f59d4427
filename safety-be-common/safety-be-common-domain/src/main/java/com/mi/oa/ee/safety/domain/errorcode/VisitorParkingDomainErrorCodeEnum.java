package com.mi.oa.ee.safety.domain.errorcode;

import com.mi.oa.ee.safety.common.enums.BizCodeEnum;
import com.mi.oa.infra.oaucf.core.exception.DomainErrorCode;
import lombok.AllArgsConstructor;

/**
 *
 * <AUTHOR> denghui
 * @desc
 * @date 2022/9/27 20:44
 */
@AllArgsConstructor
public enum VisitorParkingDomainErrorCodeEnum implements DomainErrorCode {

    PARKING_NOT_EXIST(1, "车位配置不存在"),
    PARKING_CONFIG_DUPLICATE(2, "园区车位配置重复"),
    PARKING_TIME_SLOT_NOT_EMPTY(3, "车位数量配置不能为空"),
    PARKING_TIME_SLOT_NOT_CROSS(4, "车位数量配置时间不能交叉"),
    PARKING_TIME_SLOT_START_GE_END(5, "车位数量配置起始时间不能大于等于结束时间"),
    PARK_NOT_PARKING(6, "职场暂未开放访客车位"),
    PARKING_RESIDUAL_INSUFFICIENT(7, "当前来访日时间段，园区空余车位不足，请调整申请信息后重新提交"),
    APPLY_NUM_NOT_GT_PARKING_LIMIT_NUM(8, "申请数量不能大于车位数量"),
    PARKING_NOT_MATCH_TIME_SLOT(9, "该时间段内车辆无法入园"),
    APPLY_NUM_NOT_GT_PARKING_APPLY_LIMIT_NUM(10, "申请数量不能大于申请限制数量"),
    PARK_NOT_PRIVATE_PARKING(11, "职场暂无自有车位"),
    PARKING_RESIDUAL_INSUFFICIENT_EXECUTIVE_WARN(12, "当前来访日时间段，园区空余车位不足，请联系职场协调，线下报备"),
    ;
    private Integer code;

    private String desc;

    @Override
    public int getBizCode() {
        return BizCodeEnum.SPACE.getBizCode();
    }

    @Override
    public int getErrorCode() {
        return code;
    }

    @Override
    public String getErrDesc() {
        return desc;
    }
}
