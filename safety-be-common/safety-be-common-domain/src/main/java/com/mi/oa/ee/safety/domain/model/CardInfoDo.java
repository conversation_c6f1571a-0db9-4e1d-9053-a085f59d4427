package com.mi.oa.ee.safety.domain.model;

import com.mi.oa.infra.oaucf.core.domain.DomainObject;
import lombok.Data;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/12/2 14:11
 */
@Data
public class CardInfoDo extends DomainObject<CardInfoDo> implements Serializable {


    private static final long serialVersionUID = -1779062835572988448L;

    /**
     * id
     */
    private Long id;

    /**
     * 申请单id
     */
    private Long cardApplyId;

    /**
     * 工卡状态
     */
    private Integer cardStatus;

    /**
     * 工卡类型
     */
    private Integer cardType;

    /**
     * 合作伙伴uid
     */
    private String uid;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 公司名字
     */
    private String companyName;

    /**
     * 国籍
     */
    private String nation;

    /**
     * 显示名
     */
    private String displayName;

    /**
     * 姓氏拼音
     */
    private String surname;

    /**
     * 名字拼音
     */
    private String pinyinName;

    /**
     * 身份类型
     */
    private Integer idNumberType;

    /**
     * 身份号码
     */
    private String idNumber;

    /**
     * 合作伙伴账号
     */
    private String partnerAccount;

    /**
     * email
     */
    private String email;

    /**
     * 照片地址
     */
    private String photoUrl;

    /**
     * 责任人uid
     */
    private String responsible;

    /**
     * 责任人姓名
     */
    private String responsibleName;

    /**
     * 责任人账号
     */
    private String responsibleAccount;

    /**
     * 一级部门名称
     */
    private String firstDeptIdName;

    /**
     * 二级部门名称
     */
    private String secondDeptIdName;

    /**
     * 三级部门名称
     */
    private String thirdDeptIdName;

    /**
     * 四级部门名称
     */
    private String fourthDeptIdName;

    /**
     * 介质编码
     */
    private String mediumCode;

    /**
     * 物理卡号
     */
    private String mediumPhysicsCode;

    /**
     * 新物理卡号
     */
    private String newMediumPhysicsCode;

    /**
     * 加密卡号
     */
    private String mediumEncryptCode;

    /**
     * 新加密卡号
     */
    private String newMediumEncryptCode;

    private ZonedDateTime backTime;

    private CardTimeValidityDo cardTimeValidity;

    /**
     * 载体集
     */
    private List<SafetyCarrierGroupDo> accessControl;

    /**
     * 增加权限组
     */
    private List<SafetyRightDo> addRights;

    private List<SafetyRightDo> updateRights;

    /**
     * 删除的权限组
     */
    private List<SafetyRightDo> removeRight;

    /**
     * 有效时间
     */
    private List<CardTimeValidityDo> validatePeriod;

    /**
     * 增加权限组编码
     */
    private List<String> addGroupCodes;

    /**
     * 修改权限组编码
     */
    private List<String> updateGroupCodes;

    /**
     * 删除权限组编码
     */
    private List<String> delGroupCodes;

    /**
     * 有效期开始时间
     */
    private ZonedDateTime startTime;

    /**
     * 有效期结束时间
     */
    private ZonedDateTime endTime;

    private String remark;

    /**
     * 工位编码
     */
    private String stationCode;

    /**
     * 创建时间
     */
    private ZonedDateTime createTime;

    private ZonedDateTime updateTime;

    /**
     * 卡编号
     */
    private String cardNum;

    /**
     * 园区编码
     */
    private String parkCode;

    /**
     * 园区名称
     */
    private String parkName;

    private String tenantCode;

    /**
     * 工卡
     */
    private CardApplyDo cardApply;

    /**
     * 卡操作日志对象(支撑域)
     */
    private SafetyOperateLogDo safetyOperateLog;

    private SafetyPersonDo safetyPersonDo;

    /**
     * 当前人与介质关系
     */
    private SafetyPersonMediumDo safetyPersonMediumDo;

    /**
     * 权限对象
     */
    private List<SafetyRightDo> safetyRightList;

    /**
     * 介质对象
     */
    private SafetyMediumDo safetyMedium;

    private Integer hasSpecialAuth;

    /**
     * 离职记录
     */
    private CardLeaveRecordDo cardLeaveRecord;

    private RecyclableCardDo recyclableCardDo;


    private String prefixEncryptCode;

    private String suffixEncryptCode;

    @Override
    protected boolean sameIdentityAs(CardInfoDo cardInfoDo) {
        return false;
    }
}
