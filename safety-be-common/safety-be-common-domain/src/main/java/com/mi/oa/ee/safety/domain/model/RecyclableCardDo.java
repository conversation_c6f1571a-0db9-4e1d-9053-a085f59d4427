package com.mi.oa.ee.safety.domain.model;

import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.domain.enums.card.RecyclableCardCountryEnum;
import com.mi.oa.ee.safety.domain.enums.card.RecyclableCardStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @since 2023/10/21 14:59
 */
@Data
public class RecyclableCardDo implements Serializable {

    private static final long serialVersionUID = -6491224165921126788L;

    private Long id;
    /**
     * 卡编号
     */
    private String cardNum;

    /**
     * 序号
     */
    private Integer seq;

    /**
     * 物理卡号
     */
    private String mediumPhysicsCode;

    /**
     * 加密卡号
     */
    private String mediumEncryptCode;

    /**
     * 卡状态
     */
    private RecyclableCardStatusEnum cardStatusEnum;

    /**
     * 归属地
     */
    private RecyclableCardCountryEnum countryEnum;

    /**
     * 工卡类型
     */
    private CardTypeEnum cardTypeEnum;

    /**
     * 使用人uid
     */
    private String uid;

    /*
     * 使用人
     */
    private String userName;

    /*
     * 使用人姓名
     */
    private String displayName;

    /**
     * 操作人uid
     */
    private String operatorUid;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人姓名
     */
    private String operatorDisplayName;

    /**
     * 操作时间
     */
    private ZonedDateTime operateTime;

    /**
     * 操作日志。记录新增、绑卡、变更、销号等操作日志
     */
    private RecyclableCardUseLogDo logDo;
}
