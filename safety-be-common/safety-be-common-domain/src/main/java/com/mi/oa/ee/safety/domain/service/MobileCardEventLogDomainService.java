package com.mi.oa.ee.safety.domain.service;

import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;

public interface MobileCardEventLogDomainService {
    /**
     * 保存移动刷卡记录
     */
    void saveMobileCardEvent(SafetyCarrierDo byCarrierCode, CardInfoDo cardInfoDo, SafetyPersonDo safetyPersonDo, Long eventTime);
}
