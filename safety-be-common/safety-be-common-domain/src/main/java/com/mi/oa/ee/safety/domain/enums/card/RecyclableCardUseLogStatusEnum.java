package com.mi.oa.ee.safety.domain.enums.card;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2023/10/21 14:35
 */
@Getter
@AllArgsConstructor
public enum RecyclableCardUseLogStatusEnum {
    UNDEFINED(0, "{{{未定义}}}"),
    IN_USE(1, "{{{使用中}}}"),
    RETURNED(2, "{{{已归还}}}"),
    ;

    private final Integer code;

    private final String desc;

    public static RecyclableCardUseLogStatusEnum ofCode(Integer code) {
        return Stream.of(values()).filter(statusEnum -> statusEnum.getCode().equals(code))
                .findAny().orElse(UNDEFINED);
    }

}
