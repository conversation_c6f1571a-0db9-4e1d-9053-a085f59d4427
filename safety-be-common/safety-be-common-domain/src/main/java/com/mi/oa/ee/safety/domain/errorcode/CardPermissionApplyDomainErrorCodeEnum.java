package com.mi.oa.ee.safety.domain.errorcode;

import com.mi.oa.ee.safety.common.enums.BizCodeEnum;
import com.mi.oa.infra.oaucf.core.exception.DomainErrorCode;


public enum CardPermissionApplyDomainErrorCodeEnum implements DomainErrorCode {
    CARD_PERMISSION_APPLY_ID_EMPTY(1, "{{{申请单ID不能为空}}}"),
    CARD_PERMISSION_APPLY_NOT_EXIST(2, "{{{卡权限申请单不存在}}}"),
    CARD_PERMISSION_APPLY_CODE_EMPTY(3, "{{{卡权限申请单编码为空}}}"),
    APPLY_USER_EMPTY(4, "{{{申请人uid为空}}}"),
    APPLY_USER_NOT_MATCH(5, "{{{当前登录人与申请人不匹配}}}"),
    CARD_PERMISSION_APPLY_CAN_NOT_CANCEL(6, "{{{当前申请单状态不能取消~}}}"),
    CARD_PERMISSION_APPLY_EXCEED_CARD_TIME(7, "{{{权限申请有效期超过卡有效期:%s}}}"),
    PARK_CODE_EMPTY(8, "{{{园区编码不能为空}}}"),
    PARK_CODE_NO_PERMISSION(9, "{{{您没有该园区的权限，请联系管理员}}}"),;

    private Integer code;

    private String errDesc;

    CardPermissionApplyDomainErrorCodeEnum(Integer code, String errDesc) {
        this.code = code;
        this.errDesc = errDesc;
    }

    @Override
    public int getBizCode() {
        return BizCodeEnum.CARD.getBizCode();
    }

    @Override
    public int getErrorCode() {
        return this.code;
    }

    @Override
    public String getErrDesc() {
        return this.errDesc;
    }
}
