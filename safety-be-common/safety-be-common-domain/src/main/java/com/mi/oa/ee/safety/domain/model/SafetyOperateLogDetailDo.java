package com.mi.oa.ee.safety.domain.model;

import com.mi.oa.infra.oaucf.core.domain.DomainObject;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 *
 * <AUTHOR>
 * @date 2023/1/12 17:45
 */
@Data
public class SafetyOperateLogDetailDo extends DomainObject<SafetyOperateLogDetailDo> implements Serializable {

    private static final long serialVersionUID = 2889565501514351387L;
    /**
     * 当前领域的ID
     */
    private Long id;

    /**
     * 应用编码 app_code
     */
    private String appCode;

    /**
     * 日志ID log_id
     */
    private Long logId;

    /**
     * 模型ID model_id
     */
    private Long modelId;

    /**
     * 模型类型 model_type
     */
    private String modelType;

    /**
     * 租户 tenant_code
     */
    private String tenantCode;

    /**
     * 同步状态 sync_status
     */
    private Integer syncStatus;

    /**
     * 模型详情 model_params
     */
    private String modelParams;

    @Override
    protected boolean sameIdentityAs(SafetyOperateLogDetailDo safetyOperateLogDetailDo) {
        return safetyOperateLogDetailDo.getId().equals(this.getId());
    }
}
