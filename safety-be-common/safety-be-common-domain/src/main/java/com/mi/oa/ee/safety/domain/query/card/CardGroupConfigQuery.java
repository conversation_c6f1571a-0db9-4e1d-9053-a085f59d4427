package com.mi.oa.ee.safety.domain.query.card;

import com.mi.oa.infra.oaucf.core.dto.BaseReq;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/2 20:18
 */
@Data
public class CardGroupConfigQuery extends BaseReq {

    private String cardGroupName;

    private String controlType;

    private String parentControlType;

    private String provinceId;

    private String cityId;

    private String parkCode;

    private String buildingCode;

    private String floorCode;

    private String adminUid;

    private Boolean isQueryRest;

    private Integer recordStatus;

    private List<String> cardGroupCodeList;

    /**
     * 权限编码
     */
    private String cardGroupCode;

    /**
     * 权限组名称
     */
    private String carrierGroupName;

    /**
     * 是否允许用户申请
     */
    private Integer applyFlag;

    /**
     * 部门路径
     */
    private String deptNamePath;
}
