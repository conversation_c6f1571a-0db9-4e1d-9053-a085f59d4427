package com.mi.oa.ee.safety.domain.service;

import com.mi.oa.ee.safety.domain.model.TKSEmpCardInfoDo;

public interface TKSEmpCardInfoDomainService {
    /**
     * 保存工卡信息
     * @param tksEmpCardInfoDo
     */
    void saveKSCardInfo(TKSEmpCardInfoDo tksEmpCardInfoDo);

    /**
     * 填充工卡信息
     * @param tksEmpCardInfoDo
     */
    TKSEmpCardInfoDo fillKSCardInfoByUidOrUserId(TKSEmpCardInfoDo tksEmpCardInfoDo);

    /**
     * 删除工卡信息
     * @param tksEmpCardInfoDo
     */
    void deleteKSCardInfo(TKSEmpCardInfoDo tksEmpCardInfoDo);

    /**
     * 查询人员组织信息
     * @param uid
     */
    void checkOrgCodeByUid(String uid);

    /**
     * 校验加密卡号和物理卡号
     * @param mediumEncryptCode
     * @param mediumPhysicsCode
     */
    void checkEncryptAndPhysicsCode(String mediumEncryptCode, String mediumPhysicsCode);
}
