package com.mi.oa.ee.safety.domain.model;

import com.mi.oa.infra.oaucf.core.domain.DomainObject;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/4/3 15:07
 */
@Data
public class CardPermissionApplyDo extends DomainObject<CardPermissionApplyDo> {

    /**
     * id
     */
    private Long id;

    /**
     * uid
     */
    private String uid;

    /**
     * 卡申请id
     */
    private Long cardApplyId;

    /**
     * 卡id
     */
    private Long cardId;

    /**
     * 审批编号
     */
    private String bpmCode;

    /**
     * 权限申请单编码
     */
    private String permissionApplyCode;

    /**
     * 权限申请单状态
     */
    private Integer permissionApplyStatus;

    /**
     * 门禁类型
     */
    private String controlType;

    /**
     * 申请园区编码
     */
    private String parkCode;

    /**
     * 申请理由
     */
    private String reasonName;

    /**
     * 开始时间
     */
    private ZonedDateTime startTime;

    private String createUser;

    private ZonedDateTime createTime;

    /**
     * 结束时间
     */
    private ZonedDateTime endTime;

    private CardApplyDo cardApply;

    private CardInfoDo cardInfo;

    /**
     * 授权人/申请人信息
     */
    private SafetyPersonDo personInfo;

    /**
     * 权限申请单和权限包关系集合
     */
    private List<CardPermissionApplyGroupDo> permissionApplyGroupList;

    private List<CardPermissionApplyGroupDo> existPermissionApplyGroupList;

    private List<CardGroupConfigDo> groupConfigList;

    /**
     * 根据授权流程的审批人分组的权限包集合
     */
    private Map<String, List<CardPermissionApplyGroupDo>> relationMap;

    private SafetyClassDo safetyClass;

    @Override
    protected boolean sameIdentityAs(CardPermissionApplyDo cardPermissionApplyDo) {
        return false;
    }
}
