package com.mi.oa.ee.safety.domain.ability;

import com.mi.oa.ee.safety.domain.model.CardPermissionApplyDo;
import com.mi.oa.ee.safety.domain.model.CardPermissionApplyGroupDo;

import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/4/3 14:36
 */
public interface CardPermissionApplyAbility {

    void buildPermissionApplyInfo(CardPermissionApplyDo cardPermissionApplyDo);

    void fillInfoById(CardPermissionApplyDo cardPermissionApplyDo);

    void loadCardPermissionApplyGroupByCode(CardPermissionApplyDo cardPermissionApplyDo);

    void cancelPermissionApplyGroup(CardPermissionApplyDo cardPermissionApplyDo);

    void cancelPermissionApply(CardPermissionApplyDo cardPermissionApplyDo);

    void loadCardPermissionGroupForOnceOpened(CardPermissionApplyDo cardPermissionApplyDo);

    void loadCardPermissionGroupByApplyCode(CardPermissionApplyDo cardPermissionApplyDo);

    /**
     * 驳回流程
     * @param cardPermissionApplyDo
     */
    void rejectPermissionApply(CardPermissionApplyDo cardPermissionApplyDo);

    /**
     * 明细更新为拒绝
     * @param cardPermissionApplyDo
     */
    void rejectPermissionApplyGroup(CardPermissionApplyDo cardPermissionApplyDo);

    /**
     * 审批通过
     * @param cardPermissionApplyDo
     */
    void passPermissionApply(CardPermissionApplyDo cardPermissionApplyDo);

    /**
     * 根据审批人对申请的权限做分组处理
     * @param cardPermissionApplyDo
     */
    void groupPermissionRelation(CardPermissionApplyDo cardPermissionApplyDo);

    /**
     * 权限开通流程完成后计算整个单据状态
     * @param applicantApplyDo
     */
    void computeStatusAfterGrant(CardPermissionApplyDo applicantApplyDo);

    /**
     * 授权流程拒绝
     * @param cardPermissionApplyGroupDoList
     */
    void rejectGrantApply(List<CardPermissionApplyGroupDo> cardPermissionApplyGroupDoList);

    /**
     * 授权流程通过
     * @param cardPermissionApplyGroupDoList
     */
    void passGrantApply(List<CardPermissionApplyGroupDo> cardPermissionApplyGroupDoList);

    void checkCurrentUserIsApplicant(CardPermissionApplyDo cardPermissionApplyDo);

    void checkApplyStatusIsEnableCancel(CardPermissionApplyDo cardPermissionApplyDo);

    List<CardPermissionApplyDo> findExpiredPermissionList();

    void checkCardValidateTime(CardPermissionApplyDo cardPermissionApplyDo);

    List<CardPermissionApplyDo> findWillExpiredPermissionList();

    void fillSafetyCarrier(CardPermissionApplyDo cardPermissionApplyDo);
}
