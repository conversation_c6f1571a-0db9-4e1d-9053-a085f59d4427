package com.mi.oa.ee.safety.domain.ability;

import com.mi.oa.ee.safety.domain.model.VisitorParkControlDo;
import com.mi.oa.ee.safety.domain.model.VisitorParkDo;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.util.List;

/**
 * 访客园区领域
 *
 * <AUTHOR>
 * @date 2023/2/6 14:42
 */
public interface VisitorParkAbility {

    /**
     * 根据条件查询园区信息
     *
     * @param visitorParkDo
     * @return com.mi.oa.infra.oaucf.core.dto.PageModel<com.mi.oa.ee.safety.domain.model.VisitorParkDo>
     * <AUTHOR>
     * @date 2023/4/7 14:38
     */
    PageModel<VisitorParkDo> queryPageByConditions(VisitorParkDo visitorParkDo);

    void checkEnable(Long parkId);

    void checkEnable(VisitorParkDo visitorParkDo);

    /**
     * 检查当前园区是否可启用
     *
     * @param visitorParkDo
     * @return void
     * <AUTHOR>
     * @date 2023/4/3 14:50
     */
    void checkParkIsCanEnable(VisitorParkDo visitorParkDo);

    /**
     * 填装访客园区的信息
     *
     * @param parkDo
     * @return void
     * <AUTHOR>
     * @date 2023/3/23 15:33
     */
    void fillVisitorParkInfo(VisitorParkDo parkDo);

    /**
     * 从空间中台根据code加载对应的园区名称
     *
     * @param parkDo
     * @return void
     * <AUTHOR>
     * @date 2023/3/28 19:56
     */
    void loadVisitorParkNameWithSpaceByCode(VisitorParkDo parkDo);

    /**
     * 加载当前园区对应角色下的安防载体集code
     *
     * @param parkDo
     */
    void loadVisitorParkForSafetyCarrierGroups(VisitorParkDo parkDo);

    void fillParkAdminUids(VisitorParkDo parkDo);

    void loadSafetyCarrierDoList(VisitorParkDo visitorParkDo);

    void getOneByCodesAndType(VisitorParkDo parkInfo);

    void checkParkControlTimeCross(List<VisitorParkControlDo> list);

    void loadParkConfig(List<VisitorParkControlDo> list);
}
