package com.mi.oa.ee.safety.application.dto.safety;

import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2022/9/27 16:39
 */
@Data
public class SafetyConfigUserDto {

    /**
     * id
     */
    private Long id;

    /**
     * 应用编码 app_code
     */
    private String appCode;

    /**
     * 业务编码 biz_code
     */
    private String bizCode;

    /**
     * 用户UID uid
     */
    private String uid;

    /**
     * 用户账号 user_name
     */
    private String userName;

    /**
     * 用户显示用姓名 user_display_name
     */
    private String userDisplayName;

    /**
     * 用户类型 0：普通用户  1：员工 user_type
     */
    private Integer userType;

    /**
     * 配置类型 0：白名单  1：黑名单 config_type
     */
    private Integer configType;

    /**
     * 是否禁用 0：启用 1：禁用
     */
    private Integer status;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 备注 remark
     */
    private String remark;

    /**
     * 租户 tenant_code
     */
    private String tenantCode;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private ZonedDateTime updateTime;

    /**
     * 用户电话
     */
    private String phone;

    /**
     * 开始时间
     */
    private ZonedDateTime startTime;

    /**
     * 结束时间
     */
    private ZonedDateTime endTime;
}
