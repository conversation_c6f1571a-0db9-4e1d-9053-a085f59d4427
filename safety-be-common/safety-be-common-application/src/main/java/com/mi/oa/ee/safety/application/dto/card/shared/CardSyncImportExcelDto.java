package com.mi.oa.ee.safety.application.dto.card.shared;

import com.mi.info.comb.neptune.client.NeptuneClient;
import com.mi.oa.ee.safety.common.excel.annotation.ExportField;
import com.mi.oa.ee.safety.common.excel.annotation.ImportField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/12 14:52
 */
@Data
public class CardSyncImportExcelDto {

    @ExportField(columnName = "姓名(name)", order = 1)
    @ImportField(desc = "姓名(name)")
    private String displayName;

    @ExportField(columnName = "账号(account)", order = 2)
    @ImportField(desc = "账号(account)")
    private String userName;

    @ExportField(columnName = "工号(empl id)", order = 3)
    @ImportField(desc = "工号(empl id)")
    private String employeeNo;

    @ExportField(columnName = "导入结果(result)", order = 4)
    private String result;

    @ExportField(columnName = "失败原因(error description)", order = 5)
    private String exception;

    @ExportField(columnName = "园区编码(parkCode)", order = 6)
    @ImportField(desc = "园区编码(parkCode)")
    private String parkCode;

    /**
     * 是否加入到小程序白名单
     */
    private String addAppletWhite;

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        //使用国际化
        this.result = NeptuneClient.getInstance().parseEntryTemplate(result);
    }

    public String getException() {
        return exception;
    }

    public void setException(String exception) {
        //使用国际化
        this.exception = NeptuneClient.getInstance().parseEntryTemplate(exception);
    }
}
