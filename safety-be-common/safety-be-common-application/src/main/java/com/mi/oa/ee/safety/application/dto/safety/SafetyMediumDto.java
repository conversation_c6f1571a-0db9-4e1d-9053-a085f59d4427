package com.mi.oa.ee.safety.application.dto.safety;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/22 11:03
 */
@Data
public class SafetyMediumDto {

    /**
     * 介质编码 medium_code
     */
    private String mediumCode;

    /**
     * 介质类型
     */
    private Integer mediumType;

    /**
     * 供应商编码 supplier_code
     */
    private String supplierCode;

    /**
     * 介质物理编码 medium_physics_code
     */
    private String mediumPhysicsCode;

    /**
     * 介质加密编码 medium_encrypt_code
     */
    private String mediumEncryptCode;

    /**
     * 介质名称 name
     */
    private String name;

    /**
     * 介质描述 description
     */
    private String description;

    /**
     * 园区编码 park_code
     */
    private String parkCode;

    /**
     * 备注 remark
     */
    private String remark;

    /**
     * 租户 tenant_code
     */
    private String tenantCode;

    /**
     * 介质拓展信息 extend_info
     */
    private String extendInfo;

    private Long isDeleted;
}
