package com.mi.oa.ee.safety.application.dto.reception;

import com.mi.oa.ee.safety.application.dto.safety.SafetyConfigUserDto;
import com.mi.oa.ee.safety.application.dto.safety.SafetyPersonDto;
import com.mi.oa.ee.safety.application.dto.visitor.ApplyDto;
import com.mi.oa.ee.safety.application.dto.visitor.ApplyUserInfoDto;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/5/11 10:44
 */
@Data
public class ReceptionApplyDto {

    /**
     * 接待申请单id
     */
    private Long id;

    /**
     * 园区id
     */
    private Long parkId;

    /**
     * 访客申请单ID apply_id
     */
    private Long applyId;

    /**
     * 复核状态 check_status
     */
    private Integer checkStatus;

    /**
     * 复合驳回原因 check_reject_reason
     */
    private String checkRejectReason;

    /**
     * 申请单类型
     */
    private Integer applyType;

    /**
     * 复核人uid checker
     */
    private String checker;

    /**
     * 复核时间 check_time
     */
    private ZonedDateTime checkTime;

    /**
     * 访客申请单
     */
    private ApplyDto visitorApply;

    /**
     * 园区编码
     */
    private String parkCode;

    /**
     * 园区名称
     */
    private String parkName;

    /**
     * 楼栋编码
     */
    private String buildingCode;

    /**
     * 楼栋名称
     */
    private String buildingName;

    /**
     * 楼层编码
     */
    private String floorCode;

    /**
     * 楼层名称
     */
    private String floorName;

    /**
     * 原因id
     */
    private Integer visitReasonId;

    /**
     * 申请人信息
     */
    SafetyPersonDto applyUserInfo;

    /**
     * 原因名称
     */
    private String visitReasonName;

    /**
     * 来访时间
     */
    private ZonedDateTime visitTime;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 来宾类型id
     */
    private Long receptionGuestId;

    /**
     * 来宾类型名称
     */
    private String receptionGuestName;

    /**
     * 来宾类型
     */
    private ReceptionGuestDto receptionGuest;

    /**
     * 申请服务处理人
     */
    private List<ReceptionApplyHandlerDto> applyHandlers;

    /**
     * 接待服务列表
     */
    private List<ReceptionApplyServiceDto> applyServices;

    /**
     * 我方陪同人员
     */
    private List<ApplyUserInfoDto> entourageList;

    /**
     * 接待等级
     */
    private ReceptionLevelDto receptionLevel;

    /**
     * 接待配置
     */
    private ReceptionConfigDto receptionConfig;

    private ReceptionApplyEvaluateDto applyEvaluate;

    /**
     * 来宾紧急对接人
     */
    private String emergencyContactGuest;

    /**
     * 我方陪同人员
     */
    private String entourages;

    /**
     * 签入时间
     */
    private ZonedDateTime checkInTime;

    /**
     * 签出时间
     */
    private ZonedDateTime checkOutTime;

    /**
     * 接待人uid
     */
    private String receiver;

    /**
     * 申请人名称
     */
    private String applicantName;

    /**
     * 接待等级id
     */
    private Long receptionLevelId;

    /**
     * 接待等级名称
     */
    private String receptionLevelName;

    /**
     * 来宾人数
     */
    private Integer guestNum;

    /**
     * 来宾名单列表
     */
    private String guestListUrl;

    /**
     * 来宾名单列表文件名称
     */
    private String guestListUrlName;

    /**
     * wifi密码
     */
    private String wifiPwd;

    private Integer isNeed;

    /**
     * 行程安排
     */
    private String scheduling;

    /**
     * 行程附件url scheduling_attach_url
     */
    private String schedulingAttachUrl;

    /**
     * 行程附件名称 scheduling_attach_url_name
     */
    private String schedulingAttachUrlName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 申请还是接待de
     */
    private Integer ownerType;

    /**
     * 来访状态
     */
    private Integer visitStatus;

    /**
     * 黑名单人员
     */
    private SafetyConfigUserDto safetyConfigUser;

    private List<String> parkAdminNames;

    private String parkAdmins;

    /**
     * 评价状态 0未评价 1已评价
     */
    private Integer evaluateStatus;

    /**
     * 签到方式
     */
    private Integer signType;

    /**
     * 是否可上传来访名单：0 - 否，1 - 是
     */
    private Boolean uploadable;

    /**
     * 关键人员摘要
     */
    private String guestSummary;

    /**
     * 保密协议备注信息
     */
    private String caComment;

    /**
     * 是否接待经理
     */
    private Boolean isParkAdminManager;

    /**
     * 当前页
     */
    private Long pageNum;

    /**
     * 页面大小
     */
    private Long pageSize;
}
