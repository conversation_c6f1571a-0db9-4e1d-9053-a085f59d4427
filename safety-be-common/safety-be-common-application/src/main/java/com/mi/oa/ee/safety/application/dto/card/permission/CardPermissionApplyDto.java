package com.mi.oa.ee.safety.application.dto.card.permission;

import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/4/2 22:13
 */
@Data
public class CardPermissionApplyDto {

    private Long permissionApplyId;

    private String permissionApplyCode;

    private String photoUrl;

    private Long cardId;

    private Boolean isForever;

    /**
     * 门禁类型名称
     */
    private String controlTypeName;

    /**
     * 门禁类型
     */
    private String controlType;

    private Integer applyStatus;

    private String companyName;

    private String firstDeptId;

    private String secondDeptId;

    private String thirdDeptId;

    private String fourthDeptId;

    private String applyBpmCode;

    private String firstDeptName;

    private String secondDeptName;

    private String thirdDeptName;

    private String fourthDeptName;

    private String applyUser;

    /**
     * 卡类型
     */
    private Integer cardType;

    /**
     * 是否可用
     */
    private Integer isAvailable;

    /**
     * 被授权人uid集合
     */
    private String uid;

    /**
     * 权限生效开始时间
     */
    private ZonedDateTime startTime;

    /**
     * 权限生效结束时间
     */
    private ZonedDateTime endTime;

    /**
     * 申请原因
     */
    private String reasonName;

    private String displayName;

    private String userName;

    private ZonedDateTime createTime;
    /**
     * 权限包和申请单关系集合(创建时用)
     */
    private List<PermissionDto> permissionApplyGroupList;

    private List<CardGroupConfigDto> cardGroupList;

    private List<CardGroupConfigDto> existCardGroupList;

    private Long pageNum;

    private Long pageSize;

    private Integer applyMethod;

    /**
     * 申请园区编码
     */
    private String parkCode;

    /**
     * 申请园区名称
     */
    private String parkName;

    @Data
    public static class PermissionDto {

        /**
         * 权限包编码
         */
        private String cardGroupCode;

        /**
         * 权限包名称
         */
        private String cardGroupName;
    }
}
