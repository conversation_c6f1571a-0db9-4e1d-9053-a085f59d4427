package com.mi.oa.ee.safety.application.dto.card.permission;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/4/2 20:18
 */
@Data
public class CardGroupConfigQueryDto {

    private String cardGroupName;

    private String controlType;

    private String cityId;

    private String parkCode;

    private String buildingCode;

    private String floorCode;

    private String adminUid;

    private Long pageNum;

    private Long pageSize;

    /**
     * 权限编码
     */
    private String cardGroupCode;

    /**
     * 权限组名称
     */
    private String carrierGroupName;

    /**
     * 是否允许用户申请
     */
    private Integer applyFlag;

    /**
     * 状态
     */
    private Integer recordStatus;

    /**
     * 部门路径
     */
    private String deptNamePath;
}
