package com.mi.oa.ee.safety.application.dto.card.temp;

import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @since 2023/6/8 16:26
 */
@Data
public class TempCardApplyEditDto {

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 人员uid
     */
    private String uid;

    /**
     * 权限开始时间 start_time
     */
    private ZonedDateTime startTime;

    /**
     * 权限结束时间 end_time
     */
    private ZonedDateTime endTime;

    /**
     * 卡号
     */
    private String cardNum;

    /**
     * 物理卡号
     */
    private String mediumPhysicsCode;

    /**
     * 加密卡号
     */
    private String mediumEncryptCode;
}
