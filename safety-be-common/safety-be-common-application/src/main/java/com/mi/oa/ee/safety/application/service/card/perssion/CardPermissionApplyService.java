package com.mi.oa.ee.safety.application.service.card.perssion;

import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigQueryDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardPermissionApplyDto;
import com.mi.oa.ee.safety.application.dto.visitor.BpmCallBackDto;
import com.mi.oa.ee.safety.common.dto.BpmLinkDto;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/4/2 21:50
 */
public interface CardPermissionApplyService {
//    List<CardPermissionApplyDto> getControlTypeList(CardPermissionApplyDto dto);

    List<CardPermissionApplyDto> getControlTypeList(CardPermissionApplyDto dto);

    Long createApply(CardPermissionApplyDto cardPermissionApplyDto);

    void cancel(Long permissionApplyId);

    CardPermissionApplyDto findDetail(Long permissionApplyId);

    PageModel<CardGroupConfigDto> pageGroup(CardGroupConfigQueryDto configDto);

    PageModel<CardPermissionApplyDto> pageApply(CardPermissionApplyDto cardPermissionApplyDto);

    /**
     * 申请流程审批结果处理
     * @param bpmCallBackDto
     */

    void applyBpmCallBack(BpmCallBackDto bpmCallBackDto);

    /**
     * 授权流程审批结果处理
     * @param bpmCallBackDto
     */
    void grantCallBack(BpmCallBackDto bpmCallBackDto);

    CardPermissionApplyDto findAdminDetail(Long permissionApplyId);

    CardPermissionApplyDto findCardGroupList(String uid);

    List<CardPermissionApplyDto> findExpiredPermissionList();

    void doPermissionExpireClose(CardPermissionApplyDto cardPermissionApplyDto);

    BpmLinkDto goToDetailBpm(String bpmCode);

    List<CardPermissionApplyDto> findWillExpiredPermissionList();

    void doPermissionExpireNotify(CardPermissionApplyDto permissionApplyDto);

    void editNotifyStatus(String uid, String permissionApplyCode);

    List<CardPermissionApplyDto> findPreExpireApplyList();

}
