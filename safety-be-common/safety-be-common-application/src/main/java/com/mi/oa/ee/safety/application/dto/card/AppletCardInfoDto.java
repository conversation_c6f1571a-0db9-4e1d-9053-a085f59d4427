package com.mi.oa.ee.safety.application.dto.card;


import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/25 16:57
 */
@Data
public class AppletCardInfoDto {

    private Long cardInfoId;

    private Long cardApplyId;

    private String cardNum;

    private String mediumCode;

    private String mediumPhysicsCode;

    private String mediumEncryptCode;

    private Integer cardType;

    private String cardTypeName;

    private Integer applyStatus;

    private Integer cardStatus;

    private String cardStatusName;

    private ZonedDateTime endTime;

    private String photoPlace;

    private String takePhotoTime;

    private List<String> grantedParkList;
}
