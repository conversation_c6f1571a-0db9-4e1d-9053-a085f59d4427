package com.mi.oa.ee.safety.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.enums.StatusEnum;
import com.mi.oa.ee.safety.domain.model.CardGroupConfigDo;
import com.mi.oa.ee.safety.domain.model.DeptInfoDo;
import com.mi.oa.ee.safety.domain.model.SafetySpaceDo;
import com.mi.oa.ee.safety.domain.query.card.CardGroupConfigQuery;
import com.mi.oa.ee.safety.infra.repository.CardGroupConfigRepository;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.CardGroupConfigPoConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardGroupConfigEnhancePo;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardGroupConfigPo;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardGroupConfigMapper;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/4/2 21:11
 */
@Service
public class CardGroupConfigRepositoryImpl implements CardGroupConfigRepository {

    private static final String ADMIN_QUERY_TEMPLATE = "\"adminUserCode\":\"%s\"";

    @Resource
    private CardGroupConfigMapper cardGroupConfigMapper;

    @Resource
    private CardGroupConfigPoConverter cardGroupConfigPoConverter;

    @Override
    public void save(CardGroupConfigDo cardGroupConfigDo) {
        CardGroupConfigPo cardGroupConfigPo = cardGroupConfigPoConverter.toPo(cardGroupConfigDo);
        cardGroupConfigMapper.insert(cardGroupConfigPo);
    }

    @Override
    public void update(CardGroupConfigDo cardGroupConfigDo) {
        CardGroupConfigPo cardGroupConfigPo = cardGroupConfigPoConverter.toPo(cardGroupConfigDo);
        cardGroupConfigMapper.updateById(cardGroupConfigPo);
    }

    @Override
    public CardGroupConfigDo findByCode(String cardGroupCode) {
        LambdaQueryWrapper<CardGroupConfigPo> queryWrapper = Wrappers.lambdaQuery(CardGroupConfigPo.class)
                .eq(CardGroupConfigPo::getCardGroupCode, cardGroupCode);
        CardGroupConfigPo cardGroupConfigPo = cardGroupConfigMapper.selectOne(queryWrapper);
        return cardGroupConfigPoConverter.toDo(cardGroupConfigPo);
    }

    @Override
    public PageModel<CardGroupConfigDo> page(CardGroupConfigQuery query) {
        // 如果有权限组名称查询，使用自定义的分页查询方法
        if (StringUtils.isNotBlank(query.getCarrierGroupName())) {
            Page<CardGroupConfigPo> page = new Page<>(query.getPageNum(), query.getPageSize());
            IPage<CardGroupConfigPo> pageResult = cardGroupConfigMapper.pageQuery(page, query);
            List<CardGroupConfigDo> recordList = cardGroupConfigPoConverter.toDoList(pageResult.getRecords());
            return PageModel.build(recordList, pageResult.getSize(), pageResult.getPages(), pageResult.getTotal());
        }

        if (StringUtils.isEmpty(query.getControlType())
                && SafetyConstants.Card.CARD_GROUP_PARENT_TYPE_NORMAL_CODE.equals(query.getParentControlType())) {
            query.setControlType(SafetyConstants.Card.CARD_GROUP_PARENT_TYPE_NORMAL_CODE);
        }
        LambdaQueryWrapper<CardGroupConfigPo> queryWrapper = Wrappers.lambdaQuery(CardGroupConfigPo.class)
                .like(StringUtils.isNotBlank(query.getCardGroupName()), CardGroupConfigPo::getCardGroupName, query.getCardGroupName())
                .eq(StringUtils.isNotBlank(query.getParkCode()), CardGroupConfigPo::getParkCode, query.getParkCode())
                .eq(StringUtils.isNotBlank(query.getCityId()), CardGroupConfigPo::getCityId, query.getCityId())
                .eq(StringUtils.isNotBlank(query.getProvinceId()), CardGroupConfigPo::getProvinceId, query.getProvinceId())
                .eq(StringUtils.isNotBlank(query.getControlType()), CardGroupConfigPo::getControlType, query.getControlType())
                .like(StringUtils.isNotBlank(query.getAdminUid()), CardGroupConfigPo::getAdminUser, query.getAdminUid())
                .eq(StringUtils.isNotBlank(query.getCardGroupCode()), CardGroupConfigPo::getCardGroupCode, query.getCardGroupCode())
                .eq(Objects.nonNull(query.getApplyFlag()), CardGroupConfigPo::getApplyFlag, query.getApplyFlag())
                .eq(Objects.nonNull(query.getRecordStatus()), CardGroupConfigPo::getRecordStatus, query.getRecordStatus());
        if (StringUtils.isEmpty(query.getControlType())
                && SafetyConstants.Card.CARD_GROUP_PARENT_TYPE_SPECIAL_CODE.equals(query.getParentControlType())) {
            queryWrapper.ne(CardGroupConfigPo::getControlType, SafetyConstants.Card.CARD_GROUP_PARENT_TYPE_NORMAL_CODE);
        }
        if (Objects.nonNull(query.getIsQueryRest()) && query.getIsQueryRest()) {
            query.setRecordStatus(StatusEnum.WHITE.getCode());
            queryWrapper.eq(CardGroupConfigPo::getRecordStatus, query.getRecordStatus());
/*            queryWrapper.notIn(CollectionUtils.isNotEmpty(query.getCardGroupCodeList()),
                    CardGroupConfigPo::getCardGroupCode, query.getCardGroupCodeList());*/
            queryWrapper.ne(CardGroupConfigPo::getCarrierGroupCode, StringUtils.EMPTY);
        } else if (Objects.nonNull(query.getIsQueryRest())) {
            List<String> cardGroupCodeList = query.getCardGroupCodeList() == null ? Lists.newArrayList("EMPTY") :
                    query.getCardGroupCodeList();
            queryWrapper.in(CardGroupConfigPo::getCardGroupCode, cardGroupCodeList);
        }

        // 添加部门路径查询条件（前缀匹配）
        if (StringUtils.isNotBlank(query.getDeptNamePath())) {
            queryWrapper.likeRight(CardGroupConfigPo::getDeptNamePath, query.getDeptNamePath());
        }
        queryWrapper.orderByDesc(CardGroupConfigPo::getId);
        IPage<CardGroupConfigPo> iPage = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<CardGroupConfigPo> page = cardGroupConfigMapper.selectPage(iPage, queryWrapper);
        List<CardGroupConfigDo> recordList = cardGroupConfigPoConverter.toDoList(page.getRecords());
        return PageModel.build(recordList, page.getSize(), page.getPages(), page.getTotal());
    }

    @Override
    public List<CardGroupConfigDo> findListByCode(Set<String> groupCodeSet) {
        LambdaQueryWrapper<CardGroupConfigPo> queryWrapper = Wrappers.<CardGroupConfigPo>lambdaQuery()
                .in(CardGroupConfigPo::getCardGroupCode, groupCodeSet);
        List<CardGroupConfigPo> cardGroupConfigPos = cardGroupConfigMapper.selectList(queryWrapper);
        return cardGroupConfigPoConverter.toDoList(cardGroupConfigPos);
    }

    @Override
    public PageVO<CardGroupConfigDo> pageForApplet(CardGroupConfigQuery query) {
        List<CardGroupConfigEnhancePo> list = cardGroupConfigMapper.conditionQueryList(query);
        List<CardGroupConfigDo> resultList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            //条件查询空间信息不为空时 重新查询 保证权限包下关联的位置信息完整
            Map<String, List<CardGroupConfigEnhancePo>> codeMap;
            if (StringUtils.isNotBlank(query.getParkCode())) {
                Set<String> collect = list.stream().map(CardGroupConfigEnhancePo::getCardGroupCode).collect(Collectors.toSet());
                CardGroupConfigQuery newQuery = new CardGroupConfigQuery();
                newQuery.setCardGroupCodeList(Lists.newArrayList(collect));
                List<CardGroupConfigEnhancePo> list1 = cardGroupConfigMapper.conditionQueryList(newQuery);
                codeMap = list1.stream().collect(Collectors.groupingBy(CardGroupConfigEnhancePo::getCardGroupCode));
            } else {
                codeMap = list.stream().collect(Collectors.groupingBy(CardGroupConfigEnhancePo::getCardGroupCode));
            }
            for (Map.Entry<String, List<CardGroupConfigEnhancePo>> entry : codeMap.entrySet()) {
                List<CardGroupConfigEnhancePo> value = entry.getValue();
                CardGroupConfigDo cardGroupConfigDo = new CardGroupConfigDo();
                List<SafetySpaceDo> spaceList = Lists.newArrayList();
                CardGroupConfigEnhancePo tempPo = value.get(0);
                cardGroupConfigPoConverter.copy(tempPo, cardGroupConfigDo);
                DeptInfoDo deptInfoDo = new DeptInfoDo();
                deptInfoDo.setDeptName(tempPo.getDeptName());
                deptInfoDo.setDeptNamePath(tempPo.getDeptNamePath());
                cardGroupConfigDo.setDeptInfo(deptInfoDo);
                for (CardGroupConfigEnhancePo cardGroupConfigEnhancePo : value) {
                    SafetySpaceDo safetySpaceDo = new SafetySpaceDo();
                    safetySpaceDo.setParkCode(cardGroupConfigEnhancePo.getParkCode());
                    safetySpaceDo.setBuildingCode(cardGroupConfigEnhancePo.getBuildingCode());
                    safetySpaceDo.setFloorCode(cardGroupConfigEnhancePo.getFloorCode());
                    if (StringUtils.isNotBlank(safetySpaceDo.getParkCode())) {
                        spaceList.add(safetySpaceDo);
                    }
                }
                cardGroupConfigDo.setSpaceList(spaceList);
                resultList.add(cardGroupConfigDo);
            }

        }
        return doPage(resultList, query);
    }

    private PageVO<CardGroupConfigDo> doPage(List<CardGroupConfigDo> resultList, CardGroupConfigQuery query) {
        int pageNum = query.getPageNum();
        int pageSize = query.getPageSize();
        Integer total = resultList.size();
        // 总页数
        int totalPage = (total % pageSize) > 0 ? (total / pageSize) + 1 : (total / pageSize);
        AtomicReference<PageVO<CardGroupConfigDo>> pageVO = new AtomicReference<>(new PageVO<>());
        Stream.iterate(1, i -> i + 1).limit(totalPage).forEach(pageIndex -> {
            List<CardGroupConfigDo> collect = resultList.stream().skip((long) (pageIndex - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
            if (pageIndex.equals(pageNum)) {
                pageVO.set(PageVO.build(collect, pageSize, pageNum, total));
            }
        });
        return pageVO.get();
    }

    @Override
    public boolean checkName(CardGroupConfigDo cardGroupConfigDo) {
        LambdaQueryWrapper<CardGroupConfigPo> queryWrapper = Wrappers.<CardGroupConfigPo>lambdaQuery()
                .eq(CardGroupConfigPo::getCardGroupName, cardGroupConfigDo.getCardGroupName())
                .eq(CardGroupConfigPo::getControlType, cardGroupConfigDo.getControlType())
                .ne(Objects.nonNull(cardGroupConfigDo.getId()), CardGroupConfigPo::getId, cardGroupConfigDo.getId());
        int count = cardGroupConfigMapper.selectCount(queryWrapper);
        return 0 == count;
    }

    @Override
    public List<CardGroupConfigDo> findByIds(List<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            LambdaQueryWrapper<CardGroupConfigPo> queryWrapper = Wrappers.<CardGroupConfigPo>lambdaQuery()
                    .in(CardGroupConfigPo::getId, ids);
            List<CardGroupConfigPo> cardGroupConfigPos = cardGroupConfigMapper.selectList(queryWrapper);
            return cardGroupConfigPoConverter.toDoList(cardGroupConfigPos);
        }
        return Collections.emptyList();
    }
}
