package com.mi.oa.ee.safety.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.common.BasePO;
import lombok.*;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2022/09/07/10:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "visitor_apply", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class VisitorApplyPO extends BasePO<VisitorApplyPO> {
    /**
     * 申请单code apply_code
     */
    private String applyCode;

    /**
     * 申请单类型，1：普通，2：团访 3：接待 apply_type
     */
    private Integer applyType;

    /**
     * BPM侧的流程编码 bpm_business_key
     */
    private String bpmBusinessKey;

    /**
     * 园区编码 park_code
     */
    private String parkCode;

    /**
     * 楼栋编码 building_code
     */
    private String buildingCode;

    /**
     * 楼层编码 floor_code
     */
    private String floorCode;

    /**
     * 接待人的uid receiver
     */
    private String receiver;

    /**
     * 来访原因ID visit_reason_id
     */
    private Long visitReasonId;

    /**
     * 来访时间 visit_time
     */
    private ZonedDateTime visitTime;

    /**
     * 来访结束时间 visit_end_time
     */
    private ZonedDateTime visitEndTime;

    /**
     * 邀约状态，0：已取消  1：审批中  2：审批驳回  3：待签到 4：已过期 5：已签到  6：已完成 visit_status
     */
    private Integer visitStatus;

    /**
     * company_name
     */
    private String companyName;

    /**
     * 完成类型 complete_type
     */
    private Integer completeType;

    /**
     * 完成时间 complete_time
     */
    private ZonedDateTime completeTime;

    /**
     * 短信发送状态，2进制用于表示各种短信的发送状态 ums_status
     */
    private String umsStatus;

    /**
     * 拓展表单信息 extend_info
     */
    private String extendInfo;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 审批通过时间
     */
    private ZonedDateTime approvalTime;
}