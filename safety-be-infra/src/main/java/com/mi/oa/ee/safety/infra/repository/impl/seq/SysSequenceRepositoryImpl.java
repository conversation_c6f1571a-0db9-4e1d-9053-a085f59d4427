package com.mi.oa.ee.safety.infra.repository.impl.seq;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.ee.safety.domain.model.SysSequenceDo;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.SequenceConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.SysSequencePo;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.SysSequenceMapper;
import com.mi.oa.ee.safety.infra.seq.repository.SysSequenceRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 编码序列
 */
@Repository
public class SysSequenceRepositoryImpl implements SysSequenceRepository {

    @Resource
    private SysSequenceMapper sysSequenceMapper;

    @Resource
    private SequenceConverter sequenceConverter;

    @Override
    public SysSequenceDo getByCode(String code) {
        LambdaQueryWrapper<SysSequencePo> queryWrapper = Wrappers.<SysSequencePo>lambdaQuery()
                .eq(SysSequencePo::getCode, code);
        SysSequencePo sysSequencePo = sysSequenceMapper.selectOne(queryWrapper);
        return sequenceConverter.toSequenceDo(sysSequencePo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Long next(String code) {
        SysSequencePo sysSequencePo = new SysSequencePo();
        sysSequencePo.setCode(code);
        sysSequenceMapper.next(sysSequencePo);
        return sysSequencePo.getId();
    }
}
