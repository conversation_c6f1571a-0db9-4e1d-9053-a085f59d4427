package com.mi.oa.ee.safety.infra.repository.mybatis.converter;

import com.mi.oa.ee.safety.domain.model.MobileCardEventLogDo;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.MobileCardEventLogPo;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MobileCardEventLogConverter {
    MobileCardEventLogPo toPo(MobileCardEventLogDo mobileCardEventLogDo);

    MobileCardEventLogDo toDo(MobileCardEventLogPo po);
}
