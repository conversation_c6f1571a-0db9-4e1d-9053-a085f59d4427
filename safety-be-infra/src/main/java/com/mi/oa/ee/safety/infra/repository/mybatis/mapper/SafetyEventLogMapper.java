package com.mi.oa.ee.safety.infra.repository.mybatis.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.SafetyEventLogPo;

import java.util.List;

/**
 * table_name : safety_event_log
 *
 * <AUTHOR>
 * @date 2024/01/16/06:14
 */
public interface SafetyEventLogMapper extends BaseMapper<SafetyEventLogPo> {
    /**
     * @mbg.generated
     */
    SafetyEventLogPo selectByPrimaryKey(Long id);

    @DS("door-event-log")
    void batchInsertOrUpdate(List<SafetyEventLogPo> list, String nowUser, Long nowTime);
}