package com.mi.oa.ee.safety.infra.repository.mybatis.converter;

import com.mi.oa.ee.safety.domain.model.CardPermissionApplyDo;
import com.mi.oa.ee.safety.domain.model.CardPermissionApplyGroupDo;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardPermissionApplyGroupPo;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardPermissionApplyPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CardPermissionApplyPoConverter {
    List<CardPermissionApplyGroupPo> toApplyGroupPoList(List<CardPermissionApplyGroupDo> permissionApplyGroupList);

    @Mapping(target = "cardApplyId", source = "cardApply.id")
    @Mapping(target = "cardId", source = "cardInfo.id")
    @Mapping(target = "parkCode", source = "parkCode")
    CardPermissionApplyPo toPo(CardPermissionApplyDo cardPermissionApplyDo);

    CardPermissionApplyDo toDo(CardPermissionApplyPo cardPermissionApplyPo);

    List<CardPermissionApplyGroupDo> toApplyGroupDoList(List<CardPermissionApplyGroupPo> poList);

    List<CardPermissionApplyDo> toPoList(List<CardPermissionApplyPo> poList);

    List<CardPermissionApplyDo> toDoList(List<CardPermissionApplyPo> cardPermissionApplyPo);
}
