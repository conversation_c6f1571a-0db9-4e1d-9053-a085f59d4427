package com.mi.oa.ee.safety.infra.remote.sdk;

import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.common.dto.AddressInfoDto;
import com.mi.oa.ee.safety.common.dto.CountryDto;
import com.mi.oa.ee.safety.common.dto.ParkCityDto;
import com.mi.oa.ee.safety.common.dto.ParkCountryDto;
import com.mi.oa.ee.safety.common.dto.ParkProvinceDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceBuildingDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceFloorDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceParkDto;
import com.mi.oa.ee.safety.common.enums.visitor.OACacheKeyEnum;
import com.mi.oa.ee.safety.infra.errorcode.SpaceErrorCodeEnum;
import com.mi.oa.ee.safety.infra.remote.converter.SpaceConverter;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.redis.annotation.OACacheSet;
import com.mi.oa.infra.oaucf.space.rep.SpaceBuildingVO;
import com.mi.oa.infra.oaucf.space.rep.SpaceFloorVO;
import com.mi.oa.infra.oaucf.space.rep.SpaceParkVO;
import com.mi.oa.infra.oaucf.space.rep.UserLocationVO;
import com.mi.oa.infra.oaucf.space.service.SpaceBuildingService;
import com.mi.oa.infra.oaucf.space.service.SpaceFloorService;
import com.mi.oa.infra.oaucf.space.service.SpaceParkService;
import com.mi.oa.infra.oaucf.space.service.StationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2022/8/19 13:01
 */

@Component
@Slf4j
public class SpaceSdk {

    @Autowired
    SpaceParkService spaceParkService;

    @Autowired
    SpaceBuildingService spaceBuildingService;

    @Autowired
    SpaceFloorService spaceFloorService;

    @Autowired
    StationService stationService;

    @Autowired
    SpaceConverter spaceConverter;

    @Resource
    private AddressSdk addressSdk;


    public SafetySpaceDto getStationInfoByUid(String uid) {
        if (StringUtils.isEmpty(uid)) {
            throw new BizException(SpaceErrorCodeEnum.PARK_UID_IS_EMPTY);
        }
        try {
            BaseResp<UserLocationVO> resp = stationService.getUserLocation(uid);
            if (resp.getCode() == 0) {
                return spaceConverter.userLocationToSpaceDto(resp.getData());
            } else {
                log.error("param uid:{} load space info occur something wrong:{}", uid, resp.getMessage());
            }
        } catch (Exception e) {
            log.error("param uid:{} getStationInfoByUid error {}", uid, e.getMessage());
        }
        return null;
    }

    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "SPACE_BUILDINGS_PARK_CODE",
            param = "parkCode", refreshCacheTime = 5 * 60)
    public SafetySpaceDto getBuildingsByParkCode(String parkCode) {
        if (StringUtils.isEmpty(parkCode)) {
            throw new BizException(SpaceErrorCodeEnum.PARK_PARAMS_IS_EMPTY);
        }
        try {
            BaseResp<List<SpaceBuildingVO>> resp = spaceParkService.getBuildingsByParkCode(parkCode);
            if (resp.getCode() == 0) {
                SafetySpaceDto safetySpaceDto = new SafetySpaceDto();
                safetySpaceDto.setBuildings(spaceConverter.toBuildingListDto(resp.getData()));
                return safetySpaceDto;
            } else {
                log.error("param parkCode:{} spaceParkService error {}", parkCode, resp.getMessage());
            }
        } catch (Exception e) {
            log.error("param parkCode:{} spaceParkService error {}", parkCode, e.getMessage());
        }
        return null;
    }

    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "SPACE_FLOORS_BUILDING_CODE",
            param = "buildingCode", refreshCacheTime = 5 * 60)
    public SafetySpaceDto getFloorsByBuildingCode(String buildingCode) {
        if (StringUtils.isEmpty(buildingCode)) {
            throw new BizException(SpaceErrorCodeEnum.PARK_PARAMS_IS_EMPTY);
        }
        try {
            BaseResp<List<SpaceFloorVO>> resp = spaceBuildingService.getFloorsByBuildingCode(buildingCode);
            if (resp.getCode() == 0) {
                SafetySpaceDto safetySpaceDto = new SafetySpaceDto();
                safetySpaceDto.setFloors(spaceConverter.toFloorListDto(resp.getData()));
                return safetySpaceDto;
            } else {
                log.error("param buildingCode:{} getFloorsByBuildingCode error {}", buildingCode, resp.getMessage());
            }
        } catch (Exception e) {
            log.error("param buildingCode:{} getFloorsByBuildingCode error {}", buildingCode, e.getMessage());
        }
        return null;
    }

    /**
     * @param parkCode
     * @return com.mi.oa.infra.oaucf.space.rep.SpaceParkVO
     * @desc 通过园区编码 获取园区详细信息
     * <AUTHOR> denghui
     * @date 2022/9/19 18:09
     */
    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "SPACE_PARK",
            param = "parkCode", refreshCacheTime = 5 * 60)
    public SafetySpaceParkDto getParkByCode(String parkCode) {
        try {
            if (StringUtils.isEmpty(parkCode)) {
                throw new BizException(SpaceErrorCodeEnum.PARK_PARAMS_IS_EMPTY);
            }
            BaseResp<SpaceParkVO> resp = spaceParkService.getParkDetailByParkCode(parkCode);
            if (resp.getCode() == 0) {
                return spaceConverter.toParkDto(resp.getData());
            } else {
                log.error("param parkCode:{} getParkDetailByParkCode error {}", parkCode, resp.getMessage());
            }
        } catch (Exception e) {
            log.error("param parkCode:{} getParkDetailByParkCode error {}", parkCode, e.getMessage());
        }
        return null;
    }

    /**
     * @param
     * @return java.util.List<com.mi.oa.infra.oaucf.space.rep.SpaceParkVO>
     * @desc 获取所有园区信息
     * <AUTHOR> denghui
     * @date 2022/9/19 18:10
     */
    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "SPACE_PARK_ALL", refreshCacheTime = 5 * 60)
    public List<SafetySpaceParkDto> getListParks() {
        try {
            BaseResp<List<SpaceParkVO>> resp = spaceParkService.getParks();
            if (resp.getCode() == 0) {
                return spaceConverter.toParkListDto(resp.getData());
            } else {
                log.error("getParks error {}", resp.getMessage());
            }
        } catch (Exception e) {
            log.error("getParks error {}", e.getMessage());
        }
        return null;
    }

    /**
     * @param buildingCode
     * @return com.mi.oa.infra.oaucf.space.rep.SpaceBuildingVO
     * @desc 获取楼栋详细信息
     * <AUTHOR> denghui
     * @date 2022/9/19 21:09
     */
    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "SPACE_BUILDING",
            param = "buildingCode", refreshCacheTime = 5 * 60)
    public SafetySpaceBuildingDto getBuildingByCode(String buildingCode) {
        if (StringUtils.isEmpty(buildingCode)) {
            throw new BizException(SpaceErrorCodeEnum.PARK_PARAMS_IS_EMPTY);
        }
        try {
            BaseResp<SpaceBuildingVO> resp = spaceBuildingService.getBuildingDetailByBuildingCode(buildingCode);
            if (resp.getCode() == 0) {
                return spaceConverter.toBuildingDto(resp.getData());
            } else {
                log.error("param buildingCode:{} getBuildingDetailByBuildingCode error {}", buildingCode, resp.getMessage());
            }
        } catch (Exception e) {
            log.error("param buildingCode:{} getBuildingDetailByBuildingCode error {}", buildingCode, e.getMessage());
        }
        return null;
    }

    /**
     * @param floorCode
     * @return com.mi.oa.infra.oaucf.space.rep.SpaceFloorVO
     * @desc 获取楼层详细信息
     * <AUTHOR> denghui
     * @date 2022/9/19 21:11
     */
    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "SPACE_FLOOR",
            param = "floorCode", refreshCacheTime = 5 * 60)
    public SafetySpaceFloorDto getFloorByCode(String floorCode) {
        if (StringUtils.isEmpty(floorCode)) {
            throw new BizException(SpaceErrorCodeEnum.PARK_PARAMS_IS_EMPTY);
        }
        try {
            BaseResp<SpaceFloorVO> resp = spaceFloorService.getFloorDetailByFloorCode(floorCode);
            if (resp.getCode() == 0) {
                return spaceConverter.toFloorDto(resp.getData());
            } else {
                log.error("param floorCode:{} getFloorDetailByFloorCode error {}", floorCode, resp.getMessage());
            }
        } catch (Exception e) {
            log.error("param floorCode:{} getFloorDetailByFloorCode error {}", floorCode, e.getMessage());
        }
        return null;
    }

    /**
     * @param
     * @return java.util.List<com.mi.oa.infra.oaucf.space.rep.SpaceParkVO>
     * @desc 获取园区楼栋楼层属性结构
     * <AUTHOR> denghui
     * @date 2022/9/27 20:16
     */
    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "SPACE_PARK_BUILDING_FLOOR_TREE",
            refreshCacheTime = 10 * 60)
    public List<SafetySpaceParkDto> getParkBuildingFloorTree() {
        try {
            BaseResp<List<SpaceParkVO>> resp = spaceParkService.getParkBuildingFloorTree();
            if (resp.getCode() == 0) {
                return spaceConverter.toParkListDto(resp.getData());
            } else {
                log.error("getParkBuildingFloorTree error {}", resp.getMessage());
            }
        } catch (Exception e) {
            log.error("getParkBuildingFloorTree error {}", e.getMessage());
        }
        return null;
    }

    /**
     * 通过组织编码获取园区楼栋楼层属性结构
     *
     * @param orgCode 组织编码
     * @return
     */
    public List<SafetySpaceParkDto> getParkBuildingFloorTreeByOrgCode(String orgCode) {
        try {
            BaseResp<List<SpaceParkVO>> resp = spaceParkService.getEnableParkBuildingFloorTreeByOrgCode(orgCode);
            if (resp.getCode() == 0) {
                return spaceConverter.toParkListDto(resp.getData());
            } else {
                log.error("getParkBuildingFloorTreeByOrgCode error {}", resp.getMessage());
            }
        } catch (Exception e) {
            log.error("getParkBuildingFloorTreeByOrgCode error {}", e.getMessage());
        }
        return null;
    }

    /**
     * @param
     * @return java.util.List<com.mi.oa.infra.oaucf.space.rep.SpaceParkVO>
     * @desc 获取所有园区 （包括冻结的）
     * <AUTHOR> denghui
     * @date 2022/10/13 18:01
     */
    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "SPACE_PARK_BUILDING_FLOOR_TREE_ALL",
            refreshCacheTime = 10 * 60)
    public List<SafetySpaceParkDto> getAllParkBuildingFloorTree() {
        try {
            BaseResp<List<SpaceParkVO>> resp = spaceParkService.getParkBuildingFloorTreeAll();
            if (resp.getCode() == 0) {
                return spaceConverter.toParkListDto(resp.getData());
            } else {
                log.error("getParkBuildingFloorTreeAll error {}", resp.getMessage());
            }
        } catch (Exception e) {
            log.error("getParkBuildingFloorTreeAll error {}", e.getMessage());
        }
        return null;
    }

    /**
     * 通过组织编码获取所有园区 （包括冻结的）
     *
     * @param orgCode 组织编码
     * @return
     */
    public List<SafetySpaceParkDto> getAllParkBuildingFloorTreeByOrgCode(String orgCode) {
        try {
            BaseResp<List<SpaceParkVO>> resp = spaceParkService.getParkBuildingFloorTreeAllByOrgCode(orgCode);
            if (resp.getCode() == 0) {
                return spaceConverter.toParkListDto(resp.getData());
            } else {
                log.error("getAllParkBuildingFloorTreeByOrgCode error {}", resp.getMessage());
            }
        } catch (Exception e) {
            log.error("getAllParkBuildingFloorTreeByOrgCode error {}", e.getMessage());
        }
        return null;
    }

    private ParkCountryDto toParkCountryDto(String countryId, Map<String, CountryDto> countryDtoMap) {
        return spaceConverter.toParkCountryDto(countryDtoMap.get(countryId));
    }

    private ParkProvinceDto toParkProvinceDto(String provinceId, Map<String, AddressInfoDto> provinceDtoMap) {
        return spaceConverter.toParkProvinceDto(provinceDtoMap.get(provinceId));
    }

    private ParkCityDto toParkCityDto(String cityId, Map<String, AddressInfoDto> cityDtoMap) {
        return spaceConverter.toParkCityDto(cityDtoMap.get(cityId));
    }

    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "SPACE_PARK_TREE_ALL",
            refreshCacheTime = 10 * 60)
    public List<ParkCityDto> getAllSpaceParkTree() {
        List<ParkCityDto> parkCityDtoList = Lists.newArrayList();
        List<SafetySpaceParkDto> parkList = getListParks();
        if (CollectionUtils.isEmpty(parkList)) {
            return parkCityDtoList;
        }
        List<Integer> cityIdList = parkList.stream().map(SafetySpaceParkDto::getCityId)
                .map(Integer::parseInt).collect(Collectors.toList());
        List<AddressInfoDto> cityList = addressSdk.getAddressById(cityIdList);
        Map<String, AddressInfoDto> cityMap = cityList.stream()
                .collect(Collectors.toMap(AddressInfoDto::getAddrId, Function.identity(), (a, b) -> a));
        Map<String, List<SafetySpaceParkDto>> cityDtoMap  = parkList.stream()
                .filter(item -> AddressSdk.isChinaMainland(item.getCountryId()))
                .collect(Collectors.groupingBy(SafetySpaceParkDto::getCityId));
        for (Entry<String, List<SafetySpaceParkDto>> entry : cityDtoMap.entrySet()) {
            ParkCityDto cityDto = toParkCityDto(entry.getKey(), cityMap);
            if (Objects.isNull(cityDto)) {
                continue;
            }
            cityDto.setParkList(entry.getValue());
            parkCityDtoList.add(cityDto);
        }
        return parkCityDtoList;
    }

    /**
     * @param countryId
     * @return java.util.List<com.mi.oa.infra.oaucf.space.rep.SpaceParkVO>
     * @desc 通过城市ID获取园区集合
     * <AUTHOR> denghui
     * @date 2022/12/1 14:22
     */
    public List<SafetySpaceParkDto> getListParksByCountryId(String countryId) {
        try {
            Long countryIdReq = Long.valueOf(countryId);
            BaseResp<List<SpaceParkVO>> resp = spaceParkService.getEnableParksByCountryId(countryIdReq);
            if (resp.getCode() == 0) {
                return spaceConverter.toParkListDto(resp.getData());
            } else {
                log.error("param cityId:{} getEnableParksByCityId error {}", countryId, resp.getMessage());
            }
        } catch (Exception e) {
            log.error("param cityId:{} getEnableParksByCityId error {}", countryId, e.getMessage());
        }
        return null;
    }

    /**
     * @param cityId
     * @return java.util.List<com.mi.oa.infra.oaucf.space.rep.SpaceParkVO>
     * @desc 通过城市ID获取园区集合
     * <AUTHOR> denghui
     * @date 2022/12/1 14:22
     */
    public List<SafetySpaceParkDto> getListParksByCityId(Integer cityId) {
        try {
            Long cityIdReq = Long.valueOf(cityId);
            BaseResp<List<SpaceParkVO>> resp = spaceParkService.getEnableParksByCityId(cityIdReq);
            if (resp.getCode() == 0) {
                return spaceConverter.toParkListDto(resp.getData());
            } else {
                log.error("param cityId:{} getEnableParksByCityId error {}", cityId, resp.getMessage());
            }
        } catch (Exception e) {
            log.error("param cityId:{} getEnableParksByCityId error {}", cityId, e.getMessage());
        }
        return null;
    }

    public List<SafetySpaceParkDto> getAllParksByCityId(Integer cityId) {
        try {
            Long cityIdReq = Long.valueOf(cityId);
            BaseResp<List<SpaceParkVO>> resp = spaceParkService.getAllParksByCityId(cityIdReq);
            if (resp.getCode() == 0) {
                return spaceConverter.toParkListDto(resp.getData());
            } else {
                log.error("param cityId:{} getAllParksByCityId error {}", cityId, resp.getMessage());
            }
        } catch (Exception e) {
            log.error("param cityId:{} getAllParksByCityId error {}", cityId, e.getMessage());
        }
        return null;
    }

    /**
     * @param name
     * @return java.util.List<com.mi.oa.infra.oaucf.space.rep.SpaceParkVO>
     * @desc 通过名字模糊搜索园区编码
     * <AUTHOR> denghui
     * @date 2023/4/12 15:21
     */
    public List<SafetySpaceParkDto> getParkCodesByFuzzyName(String name) {
        try {
            log.info("param name:{} fuzzySearchParksByName", name);
            BaseResp<List<SpaceParkVO>> resp = spaceParkService.fuzzySearchParksByName(name);
            log.info("param name:{} fuzzySearchParksByName, response:{}", name, resp);
            if (resp.getCode() == 0) {
                return spaceConverter.toParkListDto(resp.getData());
            } else {
                log.error("param name:{} fuzzySearchParksByName error {}", name, resp.getMessage());
            }
        } catch (Exception e) {
            log.error("param name:{} fuzzySearchParksByName error {}", name, e.getMessage());
        }
        return Lists.newArrayList();
    }

    /**
     * 通过组织编码查询所有园区（不包含明细）
     *
     * @param orgCode 组织编码
     * @return
     */
    public List<SafetySpaceParkDto> getAllParksByOrgCode(String orgCode) {
        try {
            BaseResp<List<SpaceParkVO>> resp = spaceParkService.getAllParksByOrgCode(orgCode);
            if (resp.getCode() == 0) {
                return spaceConverter.toParkListDto(resp.getData());
            } else {
                log.error("getAllParksByOrgCode error {}", resp.getMessage());
            }
        } catch (Exception e) {
            log.error("getAllParksByOrgCode error {}", e.getMessage());
        }
        return null;
    }
}
