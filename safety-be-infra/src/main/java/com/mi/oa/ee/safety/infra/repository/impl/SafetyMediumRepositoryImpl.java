package com.mi.oa.ee.safety.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.ee.safety.domain.model.SafetyMediumDo;
import com.mi.oa.ee.safety.infra.errorcode.InfraErrorCodeEnum;
import com.mi.oa.ee.safety.infra.repository.SafetyMediumRepository;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.SafetyMediumPOConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.SafetyMediumPO;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.SafetyMediumMapper;
import com.mi.oa.ee.safety.infra.repository.query.SafetyMediumQuery;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/8/19 15:50
 */
@Service
@Slf4j
public class SafetyMediumRepositoryImpl extends ServiceImpl<SafetyMediumMapper, SafetyMediumPO>
        implements SafetyMediumRepository {

    @Autowired
    SafetyMediumMapper safetyMediumMapper;

    @Autowired
    SafetyMediumPOConverter converter;

    @Resource
    private IdmRemote idmRemote;

    @Override
    public SafetyMediumDo getByMediumCode(String mediumCode) {
        return converter.poToDO(safetyMediumMapper.getSafetyMediumByMediumCode(mediumCode));
    }

    @Override
    public void batchSaveOrUpdate(List<SafetyMediumDo> safetyMediumDO) {
        if (CollectionUtils.isNotEmpty(safetyMediumDO)) {
            safetyMediumMapper.updateOrSave(converter.doListToPo(safetyMediumDO));
        }
    }

    @Override
    public void deleteSafetyMediumByCodes(List<String> codes) {
        LambdaQueryWrapper<SafetyMediumPO> wrapper = Wrappers.<SafetyMediumPO>lambdaQuery();
        wrapper.in(SafetyMediumPO::getMediumCode, codes);
        safetyMediumMapper.delete(wrapper);
    }

    @Override
    public List<SafetyMediumDo> getSafetyMediumByPhysicsCodesAndSupplierCode(List<String> physicsCodes,
                                                                             String supplierCode) {
        LambdaQueryWrapper<SafetyMediumPO> wrapper = Wrappers.<SafetyMediumPO>lambdaQuery();
        wrapper.in(SafetyMediumPO::getMediumPhysicsCode, physicsCodes)
                .eq(StringUtils.isNotEmpty(supplierCode), SafetyMediumPO::getSupplierCode, supplierCode)
                .eq(SafetyMediumPO::getIsDeleted, OAUCFCommonConstants.INT_ZERO);
        return converter.poListToDO(safetyMediumMapper.selectList(wrapper));
    }

    @Override
    public SafetyMediumDo getSafetyMediumByPhysicsCodeInMediumCodes(String physicsCode, String supplierCode, List<String> mediumCodes) {
        LambdaQueryWrapper<SafetyMediumPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SafetyMediumPO::getMediumPhysicsCode, physicsCode)
                .in(SafetyMediumPO::getMediumCode, mediumCodes)
                .eq(SafetyMediumPO::getSupplierCode, supplierCode);
        return converter.poToDO(getOne(wrapper));
    }

    @Override
    public List<SafetyMediumDo> getSafetyMediumByMediumCodes(List<String> mediumCodes) {
        LambdaQueryWrapper<SafetyMediumPO> wrapper = Wrappers.<SafetyMediumPO>lambdaQuery();
        wrapper.in(SafetyMediumPO::getMediumCode, mediumCodes)
                .eq(SafetyMediumPO::getIsDeleted, OAUCFCommonConstants.INT_ZERO);
        return converter.poListToDO(safetyMediumMapper.selectList(wrapper));
    }

    @Override
    public List<SafetyMediumDo> getSafetyMediumByPhysicsCodeAndEncryptCode(String mediumPhysicsCode, String mediumEncryptCode) {
        LambdaQueryWrapper<SafetyMediumPO> wrapper = Wrappers.<SafetyMediumPO>lambdaQuery();
        wrapper.eq(SafetyMediumPO::getMediumPhysicsCode, mediumPhysicsCode)
                .eq(SafetyMediumPO::getMediumEncryptCode, mediumEncryptCode);
        return converter.poListToDO(safetyMediumMapper.selectList(wrapper));
    }

    @Override
    public void save(SafetyMediumDo safetyMediumDo) {
        try {
            safetyMediumMapper.insert(converter.doToPO(safetyMediumDo));
        } catch (Exception e) {
            log.error("medium save failed :{}", e);
            throw new BizException(InfraErrorCodeEnum.SAFETY_MEDIUM_SAVE_FAILED);
        }
    }

    @Override
    public void deleteByMediumCode(String mediumCode) {
        try {
            LambdaQueryWrapper<SafetyMediumPO> queryWrapper = Wrappers.<SafetyMediumPO>lambdaQuery()
                    .eq(SafetyMediumPO::getMediumCode, mediumCode);
            safetyMediumMapper.delete(queryWrapper);
        } catch (Exception e) {
            log.error("delete medium code {} failed {}", mediumCode, e);
            throw new BizException(InfraErrorCodeEnum.SAFETY_MEDIUM_DELETE_FAILED);
        }
    }

    @Override
    public List<SafetyMediumDo> listByConditions(SafetyMediumQuery query) {
        List<SafetyMediumPO> safetyMediumPOS = safetyMediumMapper.listByConditions(query);
        return converter.poListToDO(safetyMediumPOS);
    }

    @Override
    public void restoreByMediumCode(String mediumCode) {
        String nowUser = idmRemote.getLoginUid();
        Long nowTime = ZonedDateTime.now().toEpochSecond();
        safetyMediumMapper.restoreMedium(mediumCode, nowUser, nowTime);
    }

}
