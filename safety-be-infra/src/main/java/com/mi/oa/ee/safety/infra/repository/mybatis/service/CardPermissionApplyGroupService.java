package com.mi.oa.ee.safety.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardPermissionApplyGroupPo;

import java.util.List;

public interface CardPermissionApplyGroupService extends IService<CardPermissionApplyGroupPo> {


    List<CardPermissionApplyGroupPo> findPermissionApplyGroupListByCodeWithIsDeleted(String permissionApplyCode);
}
