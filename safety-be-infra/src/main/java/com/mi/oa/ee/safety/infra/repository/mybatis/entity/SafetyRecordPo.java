package com.mi.oa.ee.safety.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.common.BasePO;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import lombok.*;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2023/02/22/05:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "safety_record", autoResultMap = true)
public class SafetyRecordPo extends BasePO<SafetyRecordPo> {
    /**
     * 用户UID uid
     */
    private String uid;

    /**
     * 用户账号 user_name
     */
    private String userName;

    /**
     * 载体编码 carrier_code
     */
    private String carrierCode;

    /**
     * 介质编码 medium_code
     */
    private String mediumCode;

    /**
     * 载体类型 0：默认  1：门禁供应商   2：闸机供应商   3：抬杆供应商 carrier_type
     */
    private Integer carrierType;

    /**
     * 供应商侧介质校验编码 supplier_medium_check_code
     */
    private String supplierMediumCheckCode;

    /**
     * 供应商侧载体序列号 supplier_carrier_control_serial
     */
    private String supplierCarrierControlSerial;

    /**
     * 出入标识 0：无   1：入 2： 出 in_or_out
     */
    private Integer inOrOut;

    /**
     * 记录时间 record_time
     */
    @TableField(typeHandler = ZonedDateTimeBigIntTypeHandler.class)
    private ZonedDateTime recordTime;

    /**
     * 租户 tenant_code
     */
    private String tenantCode;

    /**
     * 记录拓展信息 extend_info
     */
    private String extendInfo;

    /**
     * 组织树编码
     */
    private String orgTreeCode;

    private Integer cityId;

    private String parkCode;

    private String buildingCode;

    private String floorCode;

    private String regionCode;

    private String zoneOffset;
}