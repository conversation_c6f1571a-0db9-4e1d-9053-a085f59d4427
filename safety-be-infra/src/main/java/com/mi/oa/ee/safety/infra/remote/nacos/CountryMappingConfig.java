package com.mi.oa.ee.safety.infra.remote.nacos;

import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.mi.oa.ee.safety.common.dto.CountryMappingDto;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class CountryMappingConfig implements InitializingBean {

    private volatile List<CountryMappingDto> countryMappingList = null;

    private volatile List<CountryMappingDto> countryMappingListV2 = null;

    private final ConcurrentHashMap<String, CountryMappingDto> tripCountryNameCache = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, CountryMappingDto> countryIdCache = new ConcurrentHashMap<>();


    private final ConcurrentHashMap<String, CountryMappingDto> countryCodeCache = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, CountryMappingDto> countryCodeCacheV2 = new ConcurrentHashMap<>();

    @NacosInjected
    private ConfigService configService;

    /**
     * 获取开放国际门禁的国家配置
     *
     * @return 国家配置列表
     */
    public List<CountryMappingDto> countryMappingList() {
        return Collections.unmodifiableList(countryMappingList);
    }

    /**
     * 根据差旅系统的国家名称，查找国家配置
     * @param tripCountryName 差旅系统国家名称
     * @return 国家配置
     */
    public CountryMappingDto findByTripCountryName(String tripCountryName) {
        return tripCountryNameCache.get(tripCountryName);
    }

    public CountryMappingDto findByCountryId(String countryId) {
        if (StringUtils.isBlank(countryId)) {
            return null;
        }
        return countryIdCache.get(countryId);
    }

    public CountryMappingDto findByCountryCode(String countryCode) {
        if (StringUtils.isBlank(countryCode)) {
            return null;
        }
        return countryCodeCache.get(countryCode);
    }

    public CountryMappingDto findByCountryCodeV2(String countryCode) {
        if (StringUtils.isBlank(countryCode)) {
            return null;
        }
        return countryCodeCacheV2.get(countryCode);
    }

    @NacosConfigListener(dataId = "country_mapping_config", groupId = "ee.safety", type = ConfigType.JSON)
    public void onReceived(String content) {
        log.info("country mapping config change received, content: {}", content);

        try {
            // 检查内容是否为空
            if (StringUtils.isBlank(content)) {
                log.warn("country mapping config content is blank, keeping current config");
                return;
            }

            Type type = new TypeToken<List<CountryMappingDto>>() {}.getType();
            List<CountryMappingDto> newConfigs = GsonUtils.fromJson(content, type);

            // 验证解析结果
            if (newConfigs != null) {
                countryMappingList = newConfigs;
                log.info("country mapping config updated successfully, config size: {}", newConfigs.size());
                initCache();
            } else {
                log.warn("country mapping config parsed to null, keeping current config");
            }
        } catch (Exception e) {
            log.error("Failed to parse country mapping config, keeping current config. Content: {}", content, e);
        }
    }

    @NacosConfigListener(dataId = "country_mapping_config_v2", groupId = "ee.safety", type = ConfigType.JSON)
    public void onReceivedV2(String content) {
        log.info("country mapping config v2 change received, content: {}", content);

        try {
            // 检查内容是否为空
            if (StringUtils.isBlank(content)) {
                log.warn("country mapping config v2 content is blank, keeping current config");
                return;
            }

            Type type = new TypeToken<List<CountryMappingDto>>() {}.getType();
            List<CountryMappingDto> newConfigs = GsonUtils.fromJson(content, type);

            // 验证解析结果
            if (newConfigs != null) {
                countryMappingListV2 = newConfigs;
                log.info("country mapping config v2 updated successfully, config size: {}", newConfigs.size());
                initCache();
            } else {
                log.warn("country mapping config v2 parsed to null, keeping current config");
            }
        } catch (Exception e) {
            log.error("Failed to parse country mapping config v2, keeping current config. Content: {}", content, e);
        }
    }

    private void initCache() {
        try {
            // 清空现有缓存
            tripCountryNameCache.clear();
            countryIdCache.clear();
            countryCodeCache.clear();
            countryCodeCacheV2.clear();

            // 初始化 V1 配置缓存
            if (CollectionUtils.isNotEmpty(countryMappingList)) {
                for (CountryMappingDto mapping : countryMappingList) {
                    if (mapping != null) {
                        if (StringUtils.isNotBlank(mapping.getTripCountryName())) {
                            tripCountryNameCache.put(mapping.getTripCountryName(), mapping);
                        }
                        if (StringUtils.isNotBlank(mapping.getCountryId())) {
                            countryIdCache.put(mapping.getCountryId(), mapping);
                        }
                        if (StringUtils.isNotBlank(mapping.getCountyCode())) {
                            countryCodeCache.put(mapping.getCountyCode(), mapping);
                        }
                    }
                }
                log.info("Country mapping cache initialized, V1 config size: {}", countryMappingList.size());
            }

            // 初始化 V2 配置缓存
            if (CollectionUtils.isNotEmpty(countryMappingListV2)) {
                for (CountryMappingDto mapping : countryMappingListV2) {
                    if (mapping != null && StringUtils.isNotBlank(mapping.getCountyCode())) {
                        countryCodeCacheV2.put(mapping.getCountyCode(), mapping);
                    }
                }
                log.info("Country mapping cache initialized, V2 config size: {}", countryMappingListV2.size());
            }
        } catch (Exception e) {
            log.error("Failed to initialize country mapping cache", e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            // 初始化 V1 配置
            String content = configService.getConfig("country_mapping_config", "ee.safety", 3000);
            log.info("group : ee.safety, data_id : country_mapping_config. loading:{}", content);

            if (StringUtils.isNotBlank(content)) {
                Type type = new TypeToken<List<CountryMappingDto>>() {}.getType();
                List<CountryMappingDto> initialConfigs = GsonUtils.fromJson(content, type);

                if (initialConfigs != null) {
                    countryMappingList = initialConfigs;
                    log.info("country mapping config initialized successfully, V1 config size: {}", initialConfigs.size());
                } else {
                    log.warn("country mapping config initialized to null");
                    countryMappingList = Lists.newArrayList();
                }
            } else {
                log.warn("country mapping config is blank during initialization");
                countryMappingList = Lists.newArrayList();
            }

            // 初始化 V2 配置
            String contentV2 = configService.getConfig("country_mapping_config_v2", "ee.safety", 3000);
            log.info("group : ee.safety, data_id : country_mapping_config_v2. loading:{}", contentV2);

            if (StringUtils.isNotBlank(contentV2)) {
                Type type = new TypeToken<List<CountryMappingDto>>() {}.getType();
                List<CountryMappingDto> initialConfigsV2 = GsonUtils.fromJson(contentV2, type);

                if (initialConfigsV2 != null) {
                    countryMappingListV2 = initialConfigsV2;
                    log.info("country mapping config v2 initialized successfully, V2 config size: {}", initialConfigsV2.size());
                } else {
                    log.warn("country mapping config v2 initialized to null");
                    countryMappingListV2 = Lists.newArrayList();
                }
            } else {
                log.warn("country mapping config v2 is blank during initialization");
                countryMappingListV2 = Lists.newArrayList();
            }

            // 初始化缓存
            initCache();

        } catch (Exception e) {
            log.error("Failed to initialize country mapping config", e);
            countryMappingList = Lists.newArrayList();
            countryMappingListV2 = Lists.newArrayList();
            // 不重新抛出异常，避免影响应用启动
        }
    }
}
