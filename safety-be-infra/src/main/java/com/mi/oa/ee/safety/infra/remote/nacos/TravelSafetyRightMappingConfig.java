package com.mi.oa.ee.safety.infra.remote.nacos;

import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.shaded.com.google.common.reflect.TypeToken;
import com.mi.oa.ee.safety.common.dto.TravelSafetyRightMappingDto;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.*;

@Component
@Slf4j
public class TravelSafetyRightMappingConfig implements InitializingBean {
    @NacosInjected
    ConfigService configService;

    private volatile List<TravelSafetyRightMappingDto> travelSafetyRightMappingConfigs = null;

    @NacosConfigListener(dataId = "travel_mapping_config", groupId = "ee.safety", type = ConfigType.JSON)
    public void onChange(String content) {
        log.info("travel safety right change config:{}", content);

        try {
            // 检查内容是否为空
            if (StringUtils.isBlank(content)) {
                log.warn("travel safety right config content is blank, keeping current config");
                return;
            }

            Type type = new TypeToken<List<TravelSafetyRightMappingDto>>() {}.getType();
            List<TravelSafetyRightMappingDto> newConfigs = GsonUtils.fromJson(content, type);

            // 验证解析结果
            if (newConfigs != null) {
                travelSafetyRightMappingConfigs = newConfigs;
                log.info("travel safety right config updated successfully, config size: {}", newConfigs.size());
            } else {
                log.warn("travel safety right config parsed to null, keeping current config");
            }
        } catch (Exception e) {
            log.error("Failed to parse travel safety right config, keeping current config. Content: {}", content, e);
        }
    }

    /**
     * 获取符合规则的权限包code集合
     * @param deptId
     * @param cityId
     * @return
     */
    public List<String> getTravelSafetyRightMappingConfigs(List<String> deptId, String cityId, Integer empType) {
        List<String> codes = Lists.newArrayList();
        if (Objects.isNull(travelSafetyRightMappingConfigs)) {
            return codes;
        }

        for (TravelSafetyRightMappingDto travelSafetyRightMappingDto : travelSafetyRightMappingConfigs) {
            //员工类型不匹配，跳过
            if (!travelSafetyRightMappingDto.getEmplyeeTypeList().contains(empType)) {
                continue;
            }
            //目的城市不匹配，跳过
            if (!cityId.equals(travelSafetyRightMappingDto.getCityId())) {
                continue;
            }

            List<String> inDeptId = travelSafetyRightMappingDto.getDeptId();
            List<String> notInDeptId = travelSafetyRightMappingDto.getNotInDeptId();
            //与在指定部门中，并且不在排除的部门中
            if (Collections.disjoint(notInDeptId, deptId) && !Collections.disjoint(inDeptId, deptId)) {
                codes.addAll(travelSafetyRightMappingDto.getPermissionConfigCode());
            }
        }
        return codes;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            String config = configService.getConfig("travel_mapping_config", "ee.safety", 3000);
            log.info("travel safety right get config:{}", config);

            if (StringUtils.isNotBlank(config)) {
                Type type = new TypeToken<List<TravelSafetyRightMappingDto>>() {}.getType();
                List<TravelSafetyRightMappingDto> initialConfigs = GsonUtils.fromJson(config, type);

                if (initialConfigs != null) {
                    travelSafetyRightMappingConfigs = initialConfigs;
                    log.info("travel safety right config initialized successfully, config size: {}", initialConfigs.size());
                } else {
                    log.warn("travel safety right config initialized to null");
                    travelSafetyRightMappingConfigs = Lists.newArrayList();
                }
            } else {
                log.warn("travel safety right config is blank during initialization");
                travelSafetyRightMappingConfigs = Lists.newArrayList();
            }
        } catch (Exception e) {
            log.error("Failed to initialize travel safety right config", e);
            travelSafetyRightMappingConfigs = Lists.newArrayList();
            // 不重新抛出异常，避免影响应用启动
        }
    }
}
