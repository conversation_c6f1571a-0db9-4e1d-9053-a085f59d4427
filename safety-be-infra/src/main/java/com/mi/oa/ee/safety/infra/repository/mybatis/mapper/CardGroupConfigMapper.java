package com.mi.oa.ee.safety.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.ee.safety.domain.query.card.CardGroupConfigQuery;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardGroupConfigEnhancePo;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardGroupConfigPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/2 21:16
 */

public interface CardGroupConfigMapper extends BaseMapper<CardGroupConfigPo> {

    List<CardGroupConfigEnhancePo> conditionQueryList(@Param("query") CardGroupConfigQuery query);

    List<CardGroupConfigPo> findListByCarrierGroupCode(@Param("carrierGroupCode") String carrierGroupCode);

    IPage<CardGroupConfigPo> pageQuery(IPage<CardGroupConfigPo> page, @Param("query") CardGroupConfigQuery query);
}
