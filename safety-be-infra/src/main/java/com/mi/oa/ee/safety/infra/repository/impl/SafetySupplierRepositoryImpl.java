package com.mi.oa.ee.safety.infra.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.ee.safety.common.enums.visitor.OACacheKeyEnum;
import com.mi.oa.ee.safety.domain.model.SafetySupplierDo;
import com.mi.oa.ee.safety.infra.repository.SafetySupplierRepository;
import com.mi.oa.ee.safety.infra.repository.mybatis.converter.SafetySupplierPOConverter;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.SafetySupplierPO;
import com.mi.oa.ee.safety.infra.repository.mybatis.mapper.SafetySupplierMapper;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.redis.annotation.OACacheSet;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/25 10:24
 */
@Service
public class SafetySupplierRepositoryImpl implements SafetySupplierRepository {

    @Autowired
    SafetySupplierMapper safetySupplierMapper;

    @Autowired
    SafetySupplierPOConverter converter;

    @Override
    public List<SafetySupplierDo> getSupplierListByCondition(SafetySupplierDo safetySupplierDo) {
        LambdaQueryWrapper<SafetySupplierPO> wrapper = Wrappers.<SafetySupplierPO>lambdaQuery();
        wrapper.eq(StringUtils.isNotEmpty(safetySupplierDo.getSupplierCode()), SafetySupplierPO::getSupplierCode, safetySupplierDo.getSupplierCode())
                .eq(safetySupplierDo.getSupplierType() != null, SafetySupplierPO::getSupplierType, safetySupplierDo.getSupplierType())
                .like(StringUtils.isNotEmpty(safetySupplierDo.getName()), SafetySupplierPO::getName, safetySupplierDo.getName());
        List<SafetySupplierPO> safetySupplierPOS = safetySupplierMapper.selectList(wrapper);
        return converter.poListToDoList(safetySupplierPOS);
    }

    @Override
    public PageModel<SafetySupplierDo> page(SafetySupplierDo safetySupplierDo, Long pageNum, Long pageSize) {
        IPage<SafetySupplierPO> iPage = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<SafetySupplierPO> wrapper = Wrappers.lambdaQuery();
        wrapper
                .eq(ObjectUtil.isNotEmpty(safetySupplierDo.getSupplierCode()), SafetySupplierPO::getSupplierCode, safetySupplierDo.getSupplierCode())
                .like(ObjectUtil.isNotEmpty(safetySupplierDo.getName()), SafetySupplierPO::getName, safetySupplierDo.getName());
        IPage<SafetySupplierPO> page = safetySupplierMapper.selectPage(iPage, wrapper);

        return PageModel.build(converter.poListToDoList(page.getRecords()), page.getSize(), page.getPages(), page.getTotal());
    }

    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "GET_SUPPLIER_BY_SUPPLIER_CODE",
            param = "supplierCode", refreshCacheTime = 30 * 60)
    public SafetySupplierDo getSupplierBySupplierCode(String supplierCode) {
        LambdaQueryWrapper<SafetySupplierPO> queryWrapper = Wrappers.<SafetySupplierPO>lambdaQuery()
                .eq(StringUtils.isNotEmpty(supplierCode), SafetySupplierPO::getSupplierCode, supplierCode);
        return converter.poToDO(safetySupplierMapper.selectOne(queryWrapper));
    }

    @Override
    public List<SafetySupplierDo> getListBySupplierCodes(List<String> supplierCodeList) {
        LambdaQueryWrapper<SafetySupplierPO> queryWrapper = Wrappers.<SafetySupplierPO>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(supplierCodeList), SafetySupplierPO::getSupplierCode, supplierCodeList);
        return converter.poListToDoList(safetySupplierMapper.selectList(queryWrapper));
    }
}
