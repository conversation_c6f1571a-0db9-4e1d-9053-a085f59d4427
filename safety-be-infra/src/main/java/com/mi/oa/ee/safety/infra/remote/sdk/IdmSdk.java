package com.mi.oa.ee.safety.infra.remote.sdk;

import com.google.common.cache.Cache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.common.constants.SafetyConstants.Card;
import com.mi.oa.ee.safety.common.dto.AccountModel;
import com.mi.oa.ee.safety.common.dto.DeptDto;
import com.mi.oa.ee.safety.common.dto.PersonInfoModel;
import com.mi.oa.ee.safety.common.dto.SafetyZoneDto;
import com.mi.oa.ee.safety.common.enums.safety.SafetyConfigUserTypeEnum;
import com.mi.oa.ee.safety.common.enums.visitor.OACacheKeyEnum;
import com.mi.oa.ee.safety.domain.model.CardApplyDo;
import com.mi.oa.ee.safety.infra.errorcode.IdmErrorCodeEnum;
import com.mi.oa.ee.safety.infra.remote.converter.PersonModelConverter;
import com.mi.oa.ee.safety.infra.remote.converter.RemoteConverter;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.idm.api.*;
import com.mi.oa.infra.oaucf.idm.api.enums.AccountStatusEnum;
import com.mi.oa.infra.oaucf.idm.api.enums.AccountTypeEnum;
import com.mi.oa.infra.oaucf.idm.api.enums.UserIdTypeEnum;
import com.mi.oa.infra.oaucf.idm.api.rep.*;
import com.mi.oa.infra.oaucf.idm.api.req.CreatePersonDto;
import com.mi.oa.infra.oaucf.idm.api.req.ListAccountReq;
import com.mi.oa.infra.oaucf.redis.annotation.OACacheSet;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mi.oa.infra.organization.enums.PersonUniqueEnum;
import com.mi.oa.infra.organization.rep.OrgVO;
import com.mi.oa.infra.organization.rep.PersonResp;
import com.mi.oa.infra.organization.req.BatchPersonQueryDTO;
import com.mi.oa.infra.organization.req.PersonEntryReq;
import com.mi.oa.infra.organization.req.UpdatePersonReq;
import com.mi.oa.infra.organization.service.OrgService;
import com.mi.oa.infra.organization.service.PersonEntryService;
import com.mi.oa.infra.organization.service.PersonInfoV2Service;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2022/8/19 13:00
 */

@Component
@Slf4j
public class IdmSdk {

    @Autowired
    private IdmAccountV2Service idmAccountV2Service;

    @Autowired
    private IdmAccountService idmAccountService;

    @Autowired
    private IdmUserService idmUserService;

    @Autowired
    IdmDepartmentService idmDepartmentService;

    @Autowired
    private PersonModelConverter personModelConverter;

    @Autowired
    Cache<String, Object> safetyPersonLocalObjectCache;

    @Resource
    private Cache<String, Object> departCache;

    @Autowired
    PersonInfoV2Service personInfoV2Service;

    @Autowired
    PersonEntryService personEntryService;

    @Autowired
    IdmApplyService idmApplyService;

    @Resource
    private OrgService orgService;
    @Autowired
    private RemoteConverter remoteConverter;

    @Async("asyncServiceExecutor")
    public Future<List<PersonInfoModel>> getUserInfosByUidsForAsync(List<String> uids) {
        return new AsyncResult<>(getUserInfosByUids(uids));
    }

    /**
     * 批量根据uid列表获取对应的账号
     *
     * @param uids
     * @return java.util.List<com.mi.oa.ee.safety.common.dto.AccountModel>
     * <AUTHOR>
     * @date 2022/8/29 9:54
     */
    public List<PersonInfoModel> getUserInfosByUids(List<String> uids) {
        List<PersonInfoModel> reList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(uids)) {
            try {
                BatchPersonQueryDTO listPersonReq = new BatchPersonQueryDTO();
                listPersonReq.setPersonIds(uids);
                listPersonReq.setQueryOrgInfo(Boolean.TRUE);
                BaseResp<List<PersonResp>> resp = personInfoV2Service.getPersonInfoList(listPersonReq);
                if (resp.getCode() == 0) {
                    if (CollectionUtils.isNotEmpty(resp.getData())) {
                        for (PersonResp personResp : resp.getData()) {
                            reList.add(personModelConverter.toPersonModel(personResp));
                        }
                    } else {
                        log.warn("getUserInfosByUids empty {} ", uids);
                    }
                } else {
                    log.error("IdmAccountService error {} ", JacksonUtils.bean2Json(resp));
                    throw new BizException(IdmErrorCodeEnum.INFRA_IDM_SERVICE_ERROR);
                }
            } catch (Exception e) {
                log.error("IdmAccountService error", e);
                throw new BizException(IdmErrorCodeEnum.INFRA_IDM_NET_ERROR);
            }
        }
        return reList;
    }

    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "ORG_SDK_DETAIL_DEPT_INFO",
            param = "deptId", refreshCacheTime = 5 * 60)
    public DeptDto getDeptDetail(String deptId) {
        DeptDto deptDto = new DeptDto();
        if (StringUtils.isNotEmpty(deptId)) {
            try {
                BaseResp<OrgVO> resp = orgService.getDetail(deptId, false);
                if (resp.getCode() == 0) {
                    OrgVO orgVO = resp.getData();
                    if (Objects.nonNull(orgVO)) {
                        deptDto.setDeptId(orgVO.getOrgCode());
                        deptDto.setDeptName(orgVO.getOrgName());
                        deptDto.setParentDeptId(orgVO.getParentOrgCode());
                        deptDto.setDeptOwner(orgVO.getOrgOwner());
                    } else {
                        log.warn("getDeptDetail empty {} ", deptId);
                    }
                } else {
                    log.error("getDeptDetail error {} ", JacksonUtils.bean2Json(resp));
                    throw new BizException(IdmErrorCodeEnum.ORGANIZATION_GET_DEPT_FAILED, JacksonUtils.bean2Json(resp));
                }
            } catch (Exception e) {
                log.error("getDeptDetail error", e);
                throw new BizException(IdmErrorCodeEnum.ORGANIZATION_GET_DEPT_FAILED, e.getMessage());
            }
        }
        return deptDto;
    }

    public void loadDeptInfoByDeptIdFromLocalCache(DeptDto deptDto) {
        DeptDto now = null;
        try {
            now = (DeptDto) departCache.get(deptDto.getDeptId(), () -> findDeptInfoByDeptId(deptDto.getDeptId()));
        } catch (Exception e) {
            log.error("----- findDeptInfoByDeptIdFromLocalCache error", e);
        } finally {
            if (now != null) {
                deptDto.setLevel(now.getLevel());
                deptDto.setParentDeptId(now.getParentDeptId());
            }
        }
    }

    public DeptDto findDeptInfoByDeptId(String deptId) {
        DeptDto deptDto = new DeptDto();
        if (StringUtils.isNotEmpty(deptId)) {
            try {
                Resp<DepartmentDTO> resp = idmDepartmentService.findDeptByDeptId(deptId);
                if (resp.getCode() == 0) {
                    DepartmentDTO departmentDTO = resp.getData();
                    if (Objects.nonNull(departmentDTO)) {
                        if (Objects.isNull(departmentDTO.getDeptLevel())) {
                            throw new BizException(IdmErrorCodeEnum.DEPT_LEVEL_CAN_NOT_EMPTY);
                        }
                        deptDto.setLevel(String.valueOf(departmentDTO.getDeptLevel()));
                        deptDto.setParentDeptId(departmentDTO.getParentDeptId());
                        deptDto.setDeptId(deptId);
                    } else {
                        log.warn("findDeptInfoByDeptId empty {} ", deptDto);
                    }
                } else {
                    log.error("findDeptInfoByDeptId error {} ", JacksonUtils.bean2Json(resp));
                    throw new BizException(IdmErrorCodeEnum.IDM_GET_DEPT_FAILED, JacksonUtils.bean2Json(resp));
                }
            } catch (Exception e) {
                log.error("findDeptInfoByDeptId error", e);
                throw new BizException(IdmErrorCodeEnum.IDM_GET_DEPT_FAILED, e.getMessage());
            }
        }
        return deptDto;
    }

    /**
     * @param id
     * @param userIdTypeEnum
     * @return com.mi.oa.infra.oaucf.core.dto.BaseResp<com.mi.oa.infra.oaucf.idm.api.rep.AccountInfoDto>
     * @desc 通过userId获取用户信息
     * <AUTHOR> denghui
     * @date 2022/8/26 14:04
     */
    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "IDM_SDK_USER_INFO_ACCOUNT",
            param = "id", refreshCacheTime = 5 * 60)
    public AccountModel getAccountInfo(String id, UserIdTypeEnum userIdTypeEnum) {
        try {
            BaseResp<AccountInfoDto> resp = idmAccountV2Service.getAccount(id, userIdTypeEnum);
            if (resp.getCode() == 0) {
                //判空处理
                if (Objects.isNull(resp.getData())) {
                    return null;
                }
                return personModelConverter.dtoToModel(resp.getData());
            } else {
                throw new BizException(IdmErrorCodeEnum.INFRA_IDM_SERVICE_ERROR);
            }
        } catch (Exception e) {
            log.error("getAccountInfo error", e);
            throw new BizException(IdmErrorCodeEnum.INFRA_IDM_NET_ERROR);
        }
    }

    public List<SafetyZoneDto> getZoneCodeListFromLocalCache() {
        List<SafetyZoneDto> now = null;
        try {
            now = (List<SafetyZoneDto>) safetyPersonLocalObjectCache.get(OACacheKeyEnum.IDM_SDK_ZONE_CODE_LIST.getKey(),
                    () -> getZoneCodeList());
        } catch (Exception e) {
            log.error("----- getZoneCodeListFromLocalCache error", e);
        } finally {
            if (now != null && CollectionUtils.isNotEmpty(now)) {
                safetyPersonLocalObjectCache.put(OACacheKeyEnum.IDM_SDK_ZONE_CODE_LIST.getKey(), now);
            }
        }
        return now;
    }

    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "IDM_SDK_USER_INFO_ACCOUNT", refreshCacheTime = 5 * 60)
    public List<SafetyZoneDto> getZoneCodeList() {
        try {
            Resp<List<ZoneDto>> resp = idmApplyService.getZoneCodeList();
            if (resp.getCode() == 0) {
                return personModelConverter.dtoListToSafetyZoneDtoList(resp.getData());
            } else {
                throw new BizException(IdmErrorCodeEnum.INFRA_IDM_SERVICE_ERROR);
            }
        } catch (Exception e) {
            log.error("idmAccountV2Service error", e);
            throw new BizException(IdmErrorCodeEnum.INFRA_IDM_NET_ERROR);
        }
    }

    /**
     * @param name
     * @param limit
     * @return com.mi.oa.infra.oaucf.idm.api.rep.Resp<java.util.List < com.mi.oa.infra.oaucf.idm.api.rep.AccountDo>>
     * @desc 通过姓名模糊查询
     * <AUTHOR> denghui
     * @date 2022/8/26 14:04
     */
    public List<AccountModel> getAccountByFuzzyName(String name, String limit) {
        try {
            log.info("getAccountByFuzzyName name {} limit {}", name, limit);
            Resp<List<EsAccountDto>> resp = idmAccountService.fuzzySearchAccountByName(name, limit);
            log.info("getAccountByFuzzyName name {} limit {}, resp:{}", name, limit, JacksonUtils.bean2Json(resp));

            if (resp.getCode() == 0) {
                if (CollectionUtils.isEmpty(resp.getData())) {
                    return Lists.newArrayList();
                }
                return personModelConverter.esAccountDtoToModelList(resp.getData());
            } else {
                throw new BizException(IdmErrorCodeEnum.INFRA_IDM_SERVICE_ERROR);
            }
        } catch (Exception e) {
            log.error("getAccountByFuzzyName error", e);
            throw new BizException(IdmErrorCodeEnum.INFRA_IDM_NET_ERROR);
        }
    }

    /**
     * @param uid
     * @return com.mi.oa.infra.oaucf.idm.api.rep.Resp<java.util.List < com.mi.oa.infra.oaucf.idm.api.rep.DeptInfoDto>>
     * @desc 通过uid获取用户部门信息
     * <AUTHOR> denghui
     * @date 2022/8/26 14:51
     */
    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "IDM_SDK_DEPT_INFO",
            param = "uid", refreshCacheTime = 30 * 60)
    public Resp<List<DeptInfoDto>> getDeptInfoByUid(String uid) {
        try {
            log.info("getDeptInfoByUid uid:{}", uid);
            Resp<List<DeptInfoDto>> resp = idmDepartmentService.queryFullDeptByUid(uid);
            log.info("getDeptInfoByUid uid:{}, resp:{}", uid, JacksonUtils.bean2Json(resp));
            if (resp.getCode() == 0) {
                return resp;
            } else {
                throw new BizException(IdmErrorCodeEnum.INFRA_IDM_SERVICE_ERROR);
            }
        } catch (Exception e) {
            log.error("getDeptInfoByUid error", e);
            throw new BizException(IdmErrorCodeEnum.INFRA_IDM_SERVICE_ERROR);
        }
    }

    /**
     * @param uid
     * @return com.mi.oa.infra.oaucf.idm.api.rep.Resp<java.lang.String>
     * @desc 通过uid获取头像
     * <AUTHOR> denghui
     * @date 2022/8/26 15:46
     */
    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "IDM_SDK_AVATAR",
            param = "uid", refreshCacheTime = 30 * 60)
    public Resp<String> getAvatarByUid(String uid) {
        try {
            Resp<String> resp = idmUserService.findAvatarByUid(uid);
            if (resp.getCode() == 0) {
                return resp;
            } else {
                throw new BizException(IdmErrorCodeEnum.INFRA_IDM_SERVICE_ERROR);
            }
        } catch (Exception e) {
            log.error("getAvatarByUid error", e);
            throw new BizException(IdmErrorCodeEnum.INFRA_IDM_SERVICE_ERROR);
        }
    }

    /**
     * 创建用户到用户中心
     *
     * @param req
     * @return
     */
    public PersonInfoModel createPerson(PersonEntryReq req) {
        try {
            BaseResp<PersonResp> resp = personEntryService.entry(req);
            if (Objects.nonNull(resp) && resp.getCode() == 0) {
                return personModelConverter.toPersonModel(resp.getData());
            } else {
                throw new BizException(IdmErrorCodeEnum.INFRA_IDM_SERVICE_ERROR);
            }
        } catch (Exception e) {
            log.error("createPerson error", e);
            throw new BizException(IdmErrorCodeEnum.INFRA_IDM_SERVICE_ERROR);
        }
    }

    /**
     * @param personInfo
     * @param uid
     * @return void
     * @desc 更新用户
     * <AUTHOR> denghui
     * @date 2022/9/14 9:48
     */
    public void update(UpdatePersonReq personInfo, String uid) {
        // 这里
        try {
            log.info("update person uid:{}, params:{}", uid, personInfo);
            personInfoV2Service.updatePersonInfo(uid, personInfo);
        } catch (Exception e) {
            log.error("update error", e);
            throw new BizException(IdmErrorCodeEnum.INFRA_IDM_SERVICE_ERROR);
        }
    }

    /**
     * @param uidList
     * @return java.util.List<com.mi.oa.ee.safety.common.dto.PersonInfoModel>
     * @desc 查询状态为可用的人员列表
     * <AUTHOR> denghui
     * @date 2023/3/27 16:07
     */
    public List<PersonInfoModel> pagePersonByUidList(List<String> uidList) {
        if (CollectionUtils.isEmpty(uidList)) {
            return Lists.newArrayList();
        }
        List<PersonInfoModel> personList = Lists.newArrayListWithExpectedSize(uidList.size());
        List<List<String>> uidPartitionList = Lists.partition(uidList, Card.IDM_QUERY_BATCH_SIZE);
        BatchPersonQueryDTO listPersonReq = new BatchPersonQueryDTO();
        for (List<String> uids : uidPartitionList) {
            listPersonReq.setPersonIds(uids);
            listPersonReq.setQueryOrgInfo(Boolean.TRUE);
            try {
                log.info("request pagePersonByUidList param:{}", JacksonUtils.bean2Json(listPersonReq));
                BaseResp<List<PersonResp>> resp = personInfoV2Service.getPersonInfoList(listPersonReq);
                log.info("request pagePersonByUidList response:{}", JacksonUtils.bean2Json(resp));
                if (resp.getCode() == BaseResp.CODE_SUCCESS && CollectionUtils.isNotEmpty(resp.getData())) {
                    for (PersonResp personResp : resp.getData()) {
                        if (Objects.nonNull(personResp)) {
                            PersonInfoModel personInfoModel = personModelConverter.toPersonModel(personResp);
                            personList.add(personInfoModel);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("pagePersonByUidList error", e);
            }
        }
        return personList;
    }

    /**
     * 使用装好换uid
     * @param accountList
     * @return 账号和uid的映射关系
     */
    public Map<String, String> listPersonByAccount(List<String> accountList) {
        Map<String, String> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(accountList)) {
            return Maps.newHashMap();
        }
        List<List<String>> accountPartitionList = Lists.partition(accountList, Card.IDM_QUERY_BATCH_SIZE);
        for (List<String> accounts : accountPartitionList) {
            try {
                log.info("request pagePerson param:{}", JacksonUtils.bean2Json(accounts));
                BaseResp<List<PersonResp>> resp = personInfoV2Service
                        .getPersonInfoList(SafetyConfigUserTypeEnum.EMPLOYEE.getOrgTreeCode(), accounts, PersonUniqueEnum.ACCOUNT_NAME,
                                false, false);
                log.info("request pagePerson response:{}", JacksonUtils.bean2Json(resp));
                if (resp.getCode() == BaseResp.CODE_SUCCESS && Objects.nonNull(resp.getData())
                        && CollectionUtils.isNotEmpty(resp.getData())) {
                    for (PersonResp personResp : resp.getData()) {
                        if (Objects.nonNull(personResp) && Objects.nonNull(personResp.getPersonAccount())
                                && StringUtils.isNotEmpty(personResp.getPersonAccount().getAccountName())) {
                            result.put(personResp.getPersonAccount().getAccountName(), personResp.getPersonId());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("pagePersonByUidList error", e);
            }
        }
        return result;
    }

    /**
     * @param
     * @return void
     * @desc
     * <AUTHOR> denghui
     * @date 2022/12/3 17:24
     */
    public String createPerson(@NotNull CardApplyDo cardApplyDo) {
        if (Objects.isNull(cardApplyDo)) {
            return null;
        }
        try {
            PersonEntryReq personEntry = remoteConverter.toPersonEntry(cardApplyDo);

            log.info("createPerson params : {}", JacksonUtils.bean2Json(personEntry));

            BaseResp<PersonResp> personResp = personEntryService.entry(personEntry);

            log.info("createPerson response : {}", JacksonUtils.bean2Json(personResp));

            if (BaseResp.isSuccess(personResp.getCode())) {
                return personResp.getData().getPersonId();
            } else {
                throw new BizException(IdmErrorCodeEnum.INFRA_IDM_SERVICE_ERROR, personResp.getMessage());
            }
        } catch (FeignException e) {
            log.error("createPerson error", e);
            throw new BizException(IdmErrorCodeEnum.INFRA_IDM_SERVICE_ERROR, e.getMessage());
        }
    }

    /**
     * @param cardApplyDo
     * @return void
     * @desc 更新人员信息
     * <AUTHOR> denghui
     * @date 2022/12/27 19:44
     */
    public void updatePersonIdCardNumber(@NotNull CardApplyDo cardApplyDo) {
        if (Objects.isNull(cardApplyDo)) {
            return;
        }
        try {
            UpdatePersonReq updatePersonReq = UpdatePersonReq.builder()
                    .personId(cardApplyDo.getUid())
                    .idCardNumber(cardApplyDo.getIdNumber())
                    .build();

            log.info("updatePersonIdCardNumber params : {}", JacksonUtils.bean2Json(updatePersonReq));

            BaseResp<Boolean> resp = personInfoV2Service.updatePersonInfo(updatePersonReq.getPersonId(), updatePersonReq);

            log.info("updatePersonIdCardNumber response : {}", JacksonUtils.bean2Json(resp));

        } catch (Exception e) {
            log.error("updatePersonIdCardNumber error", e);
            throw new BizException(IdmErrorCodeEnum.INFRA_IDM_UPDATE_PERSON_ERROR);
        }
    }

    /**
     * @param uid
     * @return void
     * @desc 关闭用户
     * <AUTHOR> denghui
     * @date 2023/2/22 16:25
     */
    public void closePerson(String uid) {
        try {
            personInfoV2Service.personLeave(uid, SafetyConfigUserTypeEnum.VISITOR.getOrgTreeCode());
        } catch (Exception e) {
            throw new BizException(IdmErrorCodeEnum.IDM_CLOSE_FAIL, e.getMessage());
        }
    }

    /**
     * 根据账号和组织编码从缓存中获取员工信息
     *
     * @param account
     * @param orgTreeCode
     * @return com.mi.oa.ee.safety.common.dto.PersonInfoModel
     * <AUTHOR>
     * @date 2023/8/10 14:50
     */
    public PersonInfoModel findPersonInfoByAccountAndOrgFromLocalCache(String account, String orgTreeCode) {
        PersonInfoModel now = null;
        try {
            String key = account + "-" + orgTreeCode;
            now = (PersonInfoModel) safetyPersonLocalObjectCache.get(key,
                    () -> findPersonInfoForCache(account, orgTreeCode));
        } catch (Exception e) {
            log.error("----- findPersonInfoByAccountAndOrgFromLocalCache error", e);
        } finally {
            if (now != null && StringUtils.isNotEmpty(now.getUid())) {
                safetyPersonLocalObjectCache.put(now.getUid(), now);
            }
        }
        return now;
    }

    private PersonInfoModel findPersonInfoForCache(String account, String orgTreeCode) {
        PersonInfoModel personInfoModel = findPersonInfo(account, orgTreeCode, PersonUniqueEnum.ACCOUNT_NAME, true, false);
        if (personInfoModel == null) {
            return new PersonInfoModel();
        }
        return personInfoModel;
    }

    /**
     * 根据pid查询对应的员工信息
     *
     * @param pid
     * @return com.mi.oa.ee.safety.common.dto.PersonInfoModel
     * <AUTHOR>
     * @date 2023/8/10 14:51
     */
    public PersonInfoModel findPersonInfoByPidFromLocalCache(String pid) {
        PersonInfoModel now = null;
        try {
            now = (PersonInfoModel) safetyPersonLocalObjectCache.get(pid,
                    () -> findPersonInfo(pid));
        } catch (Exception e) {
            log.error("----- findPersonInfoByPidFromLocalCache error", e);
        } finally {
            if (now != null && StringUtils.isNotEmpty(now.getOrgTreeCode())) {
                String key = now.getAccountName() + "-" + now.getOrgTreeCode();
                safetyPersonLocalObjectCache.put(key, now);
            }
        }
        return now;
    }

    /**
     * @param pid
     * @return void
     * @desc 获取人员信息
     * <AUTHOR> denghui
     * @date 2023/6/5 15:04
     */
    public PersonInfoModel findPersonInfo(String pid) {
        try {
            BaseResp<PersonResp> resp = personInfoV2Service.getPersonInfo(pid, null);
            log.info("findPerson by pid:{}, resp:{} ", pid, JacksonUtils.bean2Json(resp));

            if (resp.getCode() == 0 && resp.getData() != null) {
                PersonResp person = resp.getData();
                return personModelConverter.toPersonModel(person);
            } else {
                log.error("return resp error:{}", resp.getMessage());
            }
        } catch (Exception e) {
            log.error("findPerson by pid:{} failed, error messages:{}", pid, e.getMessage());
        }
        //返回一个空对象，为了能够正常的存缓存
        return new PersonInfoModel();
    }

    /**
     * @param uniqueValue
     * @param orgTreeCode
     * @param uniqueType
     * @param queryOrgInfo
     * @param queryReportInfo
     * @return com.mi.oa.ee.safety.common.dto.PersonInfoModel
     * @desc 根据唯一值获取人员信息
     * <AUTHOR> denghui
     * @date 2023/6/6 17:09
     */
    public PersonInfoModel findPersonInfo(String uniqueValue, String orgTreeCode, PersonUniqueEnum uniqueType,
                                          Boolean queryOrgInfo, Boolean queryReportInfo) {
        try {
            BaseResp<PersonResp> resp = personInfoV2Service.getPersonInfo(orgTreeCode, uniqueValue, uniqueType,
                    queryOrgInfo, queryReportInfo);
            log.info("findPersonInfo resp:{} ", JacksonUtils.bean2Json(resp));
            if (resp.getCode() == 0) {
                PersonResp person = resp.getData();
                return personModelConverter.toPersonModel(person);
            } else {
                log.error("return resp error:{}", resp.getMessage());
            }
        } catch (Exception e) {
            log.error("findPersonInfo by pid:{} failed, error messages:{}", uniqueValue, e.getMessage());
        }
        return null;
    }

    public void clearCache(String personId) {
        safetyPersonLocalObjectCache.invalidate(personId);
    }

    /**
     * 通过手机号查询idm用户信息
     * @param phone
     * @return
     */
    public AccountInfoDto getAccountByPhone(String phone) {
        try {
            ListAccountReq listAccountReq = new ListAccountReq();
            listAccountReq.setMobile(org.assertj.core.util.Lists.newArrayList(phone));
            BaseResp<PageVO<AccountInfoDto>> pageVOBaseResp = idmAccountV2Service.listAccount(listAccountReq);
            List<AccountInfoDto> accountList = pageVOBaseResp.getData().getList();
            accountList = accountList.stream().filter(account ->
                    !account.getAccountType().equals(AccountTypeEnum.FUNCTION)
                            && !account.getAccountStatus().equals(AccountStatusEnum.CLOSE)).collect(Collectors.toList());
            if (org.springframework.util.CollectionUtils.isEmpty(accountList)) {
                log.error("根据手机号[{}]查询不到员工信息", phone);
            }
            if (accountList.size() > 1) {
                log.error("根据手机号[{}]查询到多个员工信息", phone);
            }
            return accountList.get(0);
        } catch (Exception e) {
            log.error("根据[{}],查询idm用户信息异常:[{}]", phone, e);
        }
        return null;
    }
}
