package com.mi.oa.ee.safety.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/4/7 20:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "card_group_config_record ", autoResultMap = true)
public class CardGroupConfigEnhancePo extends CardGroupConfigPo {

    private String parkCode;

    private String buildingCode;

    private String floorCode;
}
