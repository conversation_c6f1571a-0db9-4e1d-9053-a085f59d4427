package com.mi.oa.ee.safety.infra.common.x5.rsaserver;

/**
 * description
 * <p>
 * Copyright (C) 2019 by liuguangping. All rights reserved
 * <p>
 * To contact the author write to {@link:<EMAIL>}
 *
 * @ClassName
 * @Description
 * <AUTHOR>
 * @Encoding UTF-8
 * @Version cn.mioffice.common.x5server.response, v1.0 2019/04/27 00:18
 */
public class RsaServerProperties {
    private String accessApp; //指定服务可访问系统名称和秘钥 json格式  可以指定多个 eg.'{mifamily_repair : 24DB7F19B57B03FAC82EExxxxx, test : 173643DHKg&*..}'

    //指定过滤器， 针对指定url开启Rsa校验,以逗号分隔  eg.  /api/*
    private String filterPatterns;

    public String getAccessApp() {
        return accessApp;
    }

    public void setAccessApp(String accessApp) {
        this.accessApp = accessApp;
    }

    public String getFilterPatterns() {
        return filterPatterns;
    }

    public void setFilterPatterns(String filterPatterns) {
        this.filterPatterns = filterPatterns;
    }
}
