package com.mi.oa.ee.safety.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.VisitorParkVisitReasonPO;

import java.util.List;

/**
 * table_name : visitor_park_visit_reason
 *
 * <AUTHOR>
 * @date 2022/08/18/04:11
 */
public interface VisitorParkVisitReasonMapper extends BaseMapper<VisitorParkVisitReasonPO> {
    /**
     * @mbg.generated
     */
    VisitorParkVisitReasonPO selectByPrimaryKey(Long id);

    List<String> getListByParkCode(String parkCode, Integer applyType);

    List<VisitorParkVisitReasonPO> getListByReasonIds(List<Long> reasonIds);
}