package com.mi.oa.ee.safety.infra.remote.mq.producer;

import api.ClientFactory;
import api.config.ConfigKey;
import api.producer.NormalProducer;
import com.mi.oa.ee.safety.infra.remote.mq.RocketMqProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

import javax.annotation.Resource;
import java.util.Properties;

@Slf4j
@EnableConfigurationProperties(RocketMqProperties.class)
public abstract class BaseProducer implements InitializingBean, DisposableBean {

    @Resource
    private RocketMqProperties rocketMqProperties;

    protected NormalProducer producer;

    private Properties properties;

    protected abstract String getProducerGroup();

    protected void initProperties() {
        properties = new Properties();
        properties.setProperty(ConfigKey.PRODUCER_GROUP, getProducerGroup());
        properties.setProperty(ConfigKey.ACCESS_KEY, rocketMqProperties.getAccessKey());
        properties.setProperty(ConfigKey.SECRET_KEY, rocketMqProperties.getSecretKey());
        properties.setProperty(ConfigKey.NAME_SERVER_ADDR, rocketMqProperties.getAddress());
    }

    protected void createProducer() {
        producer = ClientFactory.createNormalProducer(properties);
        try {
            producer.start();
        } catch (MQClientException e) {
            log.error("start rocket mq error ", e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        initProperties();
        createProducer();
    }

    @Override
    public void destroy() throws Exception {
        if (producer != null) {
            producer.shutdown();
        }
    }
}
