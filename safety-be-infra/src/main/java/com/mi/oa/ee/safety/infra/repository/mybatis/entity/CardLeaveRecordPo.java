package com.mi.oa.ee.safety.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.common.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2023/08/09/08:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "card_leave_record", autoResultMap = true)
public class CardLeaveRecordPo extends BasePO<CardLeaveRecordPo> {
    /**
     * 人员pid uid
     */
    private String uid;

    /**
     * 离职流程ps单号 ps_leave_number
     */
    private String psLeaveNumber;

    /**
     * user_name
     */
    private String userName;

    /**
     * employee_no
     */
    private String employeeNo;

    /**
     * 预离职日期
     */
    private ZonedDateTime preLeaveDate;

    /**
     * 记录状态 0:预离职 1：已离职 status
     */
    private Integer status;

    /**
     * 备注 remark
     */
    private String remark;
}