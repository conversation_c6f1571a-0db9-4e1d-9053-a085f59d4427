package com.mi.oa.ee.safety.infra.repository.mybatis.converter;

import com.mi.oa.ee.safety.domain.model.VisitorReceptionLevelDo;
import com.mi.oa.ee.safety.infra.repository.mybatis.entity.VisitorReceptionLevelPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2023/5/10 17:03
 */
@Mapper(componentModel = "spring")
public interface VisitorReceptionLevelPoConverter {
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    VisitorReceptionLevelPO toPo(VisitorReceptionLevelDo levelDo);

    VisitorReceptionLevelDo toDo(VisitorReceptionLevelPO po);

    List<VisitorReceptionLevelDo> toDoList(List<VisitorReceptionLevelPO> list);
}
