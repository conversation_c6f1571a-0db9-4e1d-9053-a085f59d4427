package com.mi.oa.ee.safety.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.common.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2024/04/05/12:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "card_permission_apply", autoResultMap = true)
public class CardPermissionApplyPo extends BasePO<CardPermissionApplyPo> {
    /**
     * 授予人uid uid
     */
    private String uid;

    /**
     * 工卡申请单id card_apply_id
     */
    private Long cardApplyId;

    /**
     * 工卡信息id card_id
     */
    private Long cardId;

    /**
     * bpm编码 bpm_code
     */
    private String bpmCode;

    /**
     * 权限申请单号 permission_apply_code
     */
    private String permissionApplyCode;

    /**
     * 权限申请状态 permission_apply_status
     */
    private Integer permissionApplyStatus;

    /**
     * 门禁类型 control_type
     */
    private String controlType;

    /**
     * 申请园区编码 park_code
     */
    private String parkCode;

    /**
     * 申请原因 reason_name
     */
    private String reasonName;

    /**
     * 权限开始时间 start_time
     */
    private ZonedDateTime startTime;

    /**
     * 权限结束时间 end_time
     */
    private ZonedDateTime endTime;

    /**
     * 备注 remark
     */
    private String remark;

//    /**
//     * 是否进行推送 1: 推送, 0: 不推送
//     */
//    private Integer isNotify;
}