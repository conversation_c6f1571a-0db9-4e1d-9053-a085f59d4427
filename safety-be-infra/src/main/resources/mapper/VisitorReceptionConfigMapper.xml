<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.ee.safety.infra.repository.mybatis.mapper.VisitorReceptionConfigMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.ee.safety.infra.repository.mybatis.entity.VisitorReceptionConfigPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="park_id" jdbcType="BIGINT" property="parkId" />
    <result column="park_code" jdbcType="VARCHAR" property="parkCode" />
    <result column="building_code" jdbcType="VARCHAR" property="buildingCode" />
    <result column="floor_code" jdbcType="VARCHAR" property="floorCode" />
    <result column="reception_guest_id" jdbcType="BIGINT" property="receptionGuestId" />
    <result column="reception_level_id" jdbcType="BIGINT" property="receptionLevelId" />
    <result column="level_desc" jdbcType="VARCHAR" property="levelDesc" />
    <result column="sign_type" jdbcType="INTEGER" property="signType" />
    <result column="uploadable" jdbcType="BIT" property="uploadable" />
    <result column="service_id_list" jdbcType="VARCHAR" property="serviceIdList" typeHandler="com.mi.oa.ee.safety.infra.repository.mybatis.common.IdListTypeHandler" />
    <result column="is_free_parking" jdbcType="BIT" property="isFreeParking" />
    <result column="parking_service_desc" jdbcType="VARCHAR" property="parkingServiceDesc" />
    <result column="meeting_service_desc" jdbcType="VARCHAR" property="meetingServiceDesc" />
    <result column="config_status" jdbcType="INTEGER" property="configStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
  </resultMap>
  <sql id="Base_Column_List">
    id, park_id, park_code, building_code, floor_code, reception_guest_id, reception_level_id, 
    level_desc, sign_type, uploadable, service_id_list, is_free_parking, parking_service_desc, 
    meeting_service_desc, config_status, remark, sort, is_deleted, create_user, create_time, 
    update_user, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from visitor_reception_config
    where id = #{id,jdbcType=BIGINT}
  </select>
</mapper>