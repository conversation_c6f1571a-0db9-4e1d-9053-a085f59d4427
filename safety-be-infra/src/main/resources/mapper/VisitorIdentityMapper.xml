<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.ee.safety.infra.repository.mybatis.mapper.VisitorIdentityMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.ee.safety.infra.repository.mybatis.entity.VisitorIdentityPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_id" jdbcType="BIGINT" property="applyId" />
    <result column="uid" jdbcType="VARCHAR" property="uid" />
    <result column="card_no" jdbcType="VARCHAR" property="cardNo" typeHandler="com.mi.oa.ee.safety.infra.repository.mybatis.common.EncryptHandler" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
  </resultMap>
  <sql id="Base_Column_List">
    id, apply_id, uid, card_no, name, is_deleted, create_user, create_time, update_user, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from visitor_identity
    where id = #{id,jdbcType=BIGINT}
  </select>
</mapper>