<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardPermissionApplyMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardPermissionApplyPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="uid" jdbcType="VARCHAR" property="uid" />
    <result column="card_apply_id" jdbcType="BIGINT" property="cardApplyId" />
    <result column="card_id" jdbcType="BIGINT" property="cardId" />
    <result column="bpm_code" jdbcType="VARCHAR" property="bpmCode" />
    <result column="permission_apply_code" jdbcType="VARCHAR" property="permissionApplyCode" />
    <result column="permission_apply_status" jdbcType="INTEGER" property="permissionApplyStatus" />
    <result column="control_type" jdbcType="VARCHAR" property="controlType" />
    <result column="park_code" jdbcType="VARCHAR" property="parkCode" />
    <result column="reason_name" jdbcType="VARCHAR" property="reasonName" />
    <result column="start_time" jdbcType="BIGINT" property="startTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="end_time" jdbcType="BIGINT" property="endTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
  </resultMap>
  <sql id="Base_Column_List">
    id, uid, card_apply_id, card_id, bpm_code, permission_apply_code, permission_apply_status,
    control_type, park_code, reason_name, start_time, end_time, remark, is_deleted, create_user,
    create_time, update_user, update_time
  </sql>
  <sql id="Base_Column_List_t">
    t.id, t.uid, t.card_apply_id, t.card_id, t.bpm_code, t.permission_apply_code, t.permission_apply_status,
    t.control_type, t.park_code, t.reason_name, t.start_time, t.end_time, t.remark, t.is_deleted, t.create_user,
    t.create_time, t.update_user, t.update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from card_permission_apply
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="pageApply"
            resultType="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardPermissionApplyPo">
        select distinct <include refid="Base_Column_List_t" /> from card_permission_apply t
        left join card_apply t1 on t.card_apply_id = t1.id
        where t.is_deleted = 0 and t1.is_deleted = 0
        <if test="query.firstDeptId != null and query.firstDeptId != ''">
          and t1.first_dept_id = #{query.firstDeptId}
        </if>
        <if test="query.secondDeptId != null and query.secondDeptId != ''">
          and t1.second_dept_id = #{query.secondDeptId}
        </if>
        <if test="query.thirdDeptId != null and query.thirdDeptId != ''">
          and t1.third_dept_id = #{query.thirdDeptId}
        </if>
        <if test="query.fourthDeptId != null and query.fourthDeptId != ''">
          and t1.fourth_dept_id = #{query.fourthDeptId}
        </if>
        <if test="query.applyUser != null and query.applyUser != ''">
          and t.create_user = #{query.applyUser}
        </if>
        <if test="query.permissionApplyStatus != null">
          and t.permission_apply_status = #{query.permissionApplyStatus}
        </if>
        <if test="query.parkCode != null and query.parkCode != ''">
          and t.park_code = #{query.parkCode}
        </if>
        <if test="query.controlType != null and query.controlType != ''">
          and t.control_type = #{query.controlType}
        </if>
        order by t.create_time desc
    </select>
</mapper>