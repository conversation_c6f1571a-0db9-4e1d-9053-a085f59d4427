<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.ee.safety.infra.repository.mybatis.mapper.UserBehaviorRecordMapper">
    <resultMap id="BaseResultMap" type="com.mi.oa.ee.safety.infra.repository.mybatis.entity.UserBehaviorRecordPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="uid" jdbcType="VARCHAR" property="uid"/>
        <result column="behavior_code" jdbcType="VARCHAR" property="behaviorCode"/>
        <result column="behavior_value" jdbcType="VARCHAR" property="behaviorValue"/>
        <result column="is_deleted" jdbcType="BIGINT" property="isDeleted"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"
                typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"
                typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, uid,, behavior_code, behavior_value, is_deleted, create_user,create_time, update_user, update_time
    </sql>

</mapper>