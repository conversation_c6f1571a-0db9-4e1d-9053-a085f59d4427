<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardInfoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardInfoPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="card_apply_id" jdbcType="BIGINT" property="cardApplyId" />
    <result column="uid" jdbcType="VARCHAR" property="uid" />
    <result column="card_status" jdbcType="TINYINT" property="cardStatus" />
    <result column="medium_code" jdbcType="VARCHAR" property="mediumCode" />
    <result column="card_num" jdbcType="VARCHAR" property="cardNum"/>
    <result column="medium_physics_code" jdbcType="VARCHAR" property="mediumPhysicsCode"/>
    <result column="prefix_encrypt_code" jdbcType="VARCHAR" property="prefixEncryptCode"/>
    <result column="suffix_encrypt_code" jdbcType="VARCHAR" property="suffixEncryptCode"/>
    <result column="medium_encrypt_code" jdbcType="VARCHAR" property="mediumEncryptCode"/>
    <result column="card_type" jdbcType="TINYINT" property="cardType" />
    <result column="has_special_auth" jdbcType="TINYINT" property="hasSpecialAuth"/>
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="back_time" jdbcType="BIGINT" property="backTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
  </resultMap>
  <sql id="Base_Column_List">
    id, card_apply_id, uid, card_status, medium_code,card_num, medium_physics_code,
    prefix_encrypt_code, suffix_encrypt_code,medium_encrypt_code, card_type,
    has_special_auth, remark, tenant_code, is_deleted, create_user, create_time, update_user, update_time
  </sql>
  <sql id="Base_Column_List_t">
    t.id, t.card_apply_id, t.uid, t.card_status, t.medium_code,t.card_num, t.medium_physics_code,
    t.prefix_encrypt_code, t.suffix_encrypt_code, t.medium_encrypt_code,
    t.card_type, t.back_time,t.has_special_auth,
    t.remark, t.tenant_code, t.is_deleted, t.create_user, t.create_time, t.update_user, t.update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from card_info
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="pageConditionList" resultType="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardInfoPO">
    select
    distinct
    <include refid="Base_Column_List_t"/>
    from card_info as t
    left join card_apply as t1 on t.card_apply_id = t1.id
    left join safety_right as t2 on t2.medium_code = t.medium_code
    left join safety_carrier_group as t3 on t3.carrier_group_code = t2.carrier_group_code
    left join card_time_validity as t4 on t4.card_id = t.id
    <where>
      and t.is_deleted = 0
      <if test="pageConditionDto.preLeaveUidList != null and pageConditionDto.preLeaveUidList.size() > 0">
        and t.uid in
        <foreach collection="pageConditionDto.preLeaveUidList" open="(" close=")" separator="," item="item">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="pageConditionDto.cityId != null">
        and t1.city_id = #{pageConditionDto.cityId,jdbcType=INTEGER}
      </if>
      <if test="pageConditionDto.employeeNo != null and pageConditionDto.employeeNo != ''">
        and t1.emp_no = #{pageConditionDto.employeeNo,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.phone != null and pageConditionDto.phone != ''">
        and t1.phone = #{pageConditionDto.phone,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.hasSpecialAuth != null">
        and t.has_special_auth = #{pageConditionDto.hasSpecialAuth,jdbcType=INTEGER}
      </if>
      <if test="pageConditionDto.cardType != null and pageConditionDto.cardType != 0">
        and t.card_type = #{pageConditionDto.cardType,jdbcType=INTEGER}
      </if>
      <if test="parkCodes != null and parkCodes.size() > 0">
        and (t1.park_code in
        <foreach collection="parkCodes" open="(" close=")" separator="," item="item">
          #{item,jdbcType=VARCHAR}
        </foreach>
        or t1.park_code = '')
      </if>
      <if test="pageConditionDto.parkCode != null and pageConditionDto.parkCode != ''">
        and t1.park_code = #{pageConditionDto.parkCode,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.carrierGroupCode != null and pageConditionDto.carrierGroupCode != ''">
        and t3.carrier_group_code = #{pageConditionDto.carrierGroupCode,jdbcType=VARCHAR}
        and t2.is_deleted = 0
      </if>
      <if test="pageConditionDto.firstDeptId != null and pageConditionDto.firstDeptId != ''">
        and t1.first_dept_id = #{pageConditionDto.firstDeptId,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.secondDeptId != null and pageConditionDto.secondDeptId != ''">
        and t1.second_dept_id = #{pageConditionDto.secondDeptId,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.thirdDeptId != null and pageConditionDto.thirdDeptId != ''">
        and t1.third_dept_id = #{pageConditionDto.thirdDeptId,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.fourthDeptId != null and pageConditionDto.fourthDeptId != ''">
        and t1.fourth_dept_id = #{pageConditionDto.fourthDeptId,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.cardStatus != null">
        and t.card_status = #{pageConditionDto.cardStatus,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.responsible != null and pageConditionDto.responsible != ''">
        and t1.responsible = #{pageConditionDto.responsible,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.uid != null and pageConditionDto.uid != ''">
        and t1.uid = #{pageConditionDto.uid,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.companyName != null and pageConditionDto.companyName != ''">
        and t1.company_name like concat('%',#{pageConditionDto.companyName,jdbcType=VARCHAR},'%')
      </if>
      <if test="pageConditionDto.mediumPhysicsCode != null and pageConditionDto.mediumPhysicsCode !=''">
        and t.medium_physics_code = #{pageConditionDto.mediumPhysicsCode,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.mediumEncryptCode != null and pageConditionDto.mediumEncryptCode !=''">
        and t.medium_encrypt_code = #{pageConditionDto.mediumEncryptCode,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.cardStatusList != null and pageConditionDto.cardStatusList.size() > 0">
        and t.card_status in
        <foreach collection="pageConditionDto.cardStatusList" open="(" close=")" separator="," item="item">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="pageConditionDto.expireTimeFrom != null">
        and t1.end_time >= #{pageConditionDto.expireTimeFrom, typeHandler=com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler}
      </if>
      <if test="pageConditionDto.expireTimeTo != null">
        and t1.end_time &lt;= #{pageConditionDto.expireTimeTo, typeHandler=com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler}
      </if>
      and t.card_status != 1
    </where>
    order by t.create_time desc
  </select>

  <select id="pageConditionListV2" resultType="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardInfoPO">
    select
    <include refid="Base_Column_List_t"/>
    from card_info as t
    inner join card_apply as t1 on t.card_apply_id = t1.id
    <where>
      and t.is_deleted = 0 and t.card_status != 1
      <if test="query.authParkCodeList != null and query.authParkCodeList.size() > 0">
        and (t1.park_code in
        <foreach collection="query.authParkCodeList" open="(" close=")" separator="," item="item">
          #{item,jdbcType=VARCHAR}
        </foreach>
        or t1.park_code = '')
      </if>
      <if test="query.cardIdList != null and query.cardIdList.size() > 0">
        and t.id in
        <foreach collection="query.cardIdList" open="(" close=")" separator="," item="item">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="query.cityId != null and query.cityId != ''">
        and t1.city_id = #{query.cityId,jdbcType=VARCHAR}
      </if>
      <if test="query.receiptParkCode != null and query.receiptParkCode != ''">
        and t1.receipt_park_code = #{query.receiptParkCode,jdbcType=VARCHAR}
      </if>
      <if test="query.parkCode != null and query.parkCode != ''">
        and t1.park_code = #{query.parkCode,jdbcType=VARCHAR}
      </if>
      <if test="query.cardType != null">
        and t.card_type = #{query.cardType,jdbcType=INTEGER}
      </if>
      <if test="query.firstDeptId != null and query.firstDeptId != ''">
        and t1.first_dept_id = #{query.firstDeptId,jdbcType=VARCHAR}
      </if>
      <if test="query.secondDeptId != null and query.secondDeptId != ''">
        and t1.second_dept_id = #{query.secondDeptId,jdbcType=VARCHAR}
      </if>
      <if test="query.thirdDeptId != null and query.thirdDeptId != ''">
        and t1.third_dept_id = #{query.thirdDeptId,jdbcType=VARCHAR}
      </if>
      <if test="query.fourthDeptId != null and query.fourthDeptId != ''">
        and t1.fourth_dept_id = #{query.fourthDeptId,jdbcType=VARCHAR}
      </if>
      <if test="query.cardStatus != null">
        and t.card_status = #{query.cardStatus,jdbcType=INTEGER}
      </if>
      <if test="query.employeeId != null and query.employeeId != ''">
        and t1.emp_no = #{query.employeeId,jdbcType=VARCHAR}
      </if>
      <if test="query.uid != null and query.uid != ''">
        and t.uid = #{query.uid,jdbcType=VARCHAR}
      </if>
      <if test="query.uidList != null and query.uidList.size() > 0">
        and t.uid in
        <foreach collection="query.uidList" open="(" close=")" separator="," item="item">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
    <if test="query.preLeaveUidList != null and query.preLeaveUidList.size() > 0">
      and t.uid in
      <foreach collection="query.preLeaveUidList" open="(" close=")" separator="," item="item">
        #{item,jdbcType=VARCHAR}
      </foreach>
    </if>
      <if test="query.mediumPhysicsCode != null and query.mediumPhysicsCode !=''">
        and t.medium_physics_code = #{query.mediumPhysicsCode,jdbcType=VARCHAR}
      </if>
    <if test="query.mediumEncryptCode != null and query.mediumEncryptCode != ''">
      and t.medium_encrypt_code = #{query.mediumEncryptCode,jdbcType=VARCHAR}
    </if>
      <if test="query.cardNum != null and query.cardNum !=''">
        and t.card_num = #{query.cardNum,jdbcType=VARCHAR}
      </if>
    </where>
    order by t.update_time desc
  </select>

  <select id="getListCardInfoByPhysicCodes"
          resultType="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardInfoTempPo">
    select distinct t1.uid,t1.medium_physics_code,t1.medium_encrypt_code,t1.create_time,t2.name,t2.email,t2.partner_account,t2.phone
    from card_info as t1 left join card_apply as t2 on t1.card_apply_id = t2.id
    where t1.is_deleted = 0 and t2.is_deleted = 0 and t1.card_status in (1,2,4,6)
    and t1.medium_physics_code in
    <foreach collection="mediumPhysicsCodes" item="item" separator="," open="(" close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>

  <select id="getListCardInfoByEncryptCodes"
          resultType="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardInfoTempPo">
    select distinct t1.uid,t1.medium_physics_code,t1.medium_encrypt_code,t1.create_time,t2.name,t2.email,t2.partner_account,t2.phone
    from card_info as t1 left join card_apply as t2 on t1.card_apply_id = t2.id
    where t1.is_deleted = 0 and t2.is_deleted = 0 and t1.card_status in (1,2,4,6)
    and t1.medium_encrypt_code in
    <foreach collection="mediumEncryptCodes" item="item" separator="," open="(" close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>

  <select id="getListCardInfoByUid"
          resultType="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardInfoTempPo">
    select distinct t1.uid,t1.medium_physics_code,t1.medium_encrypt_code,t1.create_time,t2.name,t2.email,t2.partner_account,t2.phone
    from card_info as t1 left join card_apply as t2 on t1.card_apply_id = t2.id
    where t1.is_deleted = 0 and t2.is_deleted = 0 and t1.card_status in (1,2,4,6,9)
    and t1.uid in
    <foreach collection="uid" item="item" separator="," open="(" close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>
  <select id="getListCardInfoByAccounts"
          resultType="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardInfoTempPo">
    select distinct t1.uid,t1.medium_physics_code,t1.medium_encrypt_code,t1.create_time,t2.name,t2.email,t2.partner_account,t2.phone
    from card_info as t1 left join card_apply as t2 on t1.card_apply_id = t2.id
    where t1.is_deleted = 0 and t2.is_deleted = 0 and t1.card_status in (1,2,4,6,9)
    and t2.partner_account in
    <foreach collection="accounts" item="item" separator="," open="(" close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>

  <select id="getNeedUpdateTimeList" resultType="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardInfoPO">
  select
  <include refid="Base_Column_List_t"/>
  from card_info t
  left join card_time_validity t2 on t.id = t2.card_id
  where t2.end_time > ********** and t.card_status = 2 and t.card_type = 1 and t2.end_time != ********** and t.uid not in
  ('19d0473c7216412ab788541fb45e5370','19d273ade2ed46ab877e92d190a2d814','28c672cdc4a74f37b649afe4c4d493af','3c7d9b0d84cb45dca2b569cb550fe67e','5074313dd1034c718f1c42837294fb55','5924606f99364c4fbcb7167c202c7e10','5e7e20f38b6c4da7ae6155a4153b4b66','66e8999b537d46558e69ebf2d07b30bc','695ecfd281ec44848907061bfbf9746e','701ef605db4b4561b67829295851a398','75153f23398b4e04b0d30fdb0144b3bd','776894639d234e4a862db04cec92f553','7df53fef8fee4515839f19957f4cf57b','860ec84091674aeda5253cd55394145e','8ae4ea31e8644182890e9389b20302b4','917950ef36f84c03af856a2affc9bbbd','977c5afd304d4ddd9b81cf186da8a1a0','9877d83687514a7f9aa519e3e60de5f3','bbf76ede9a97414d9d009e529820bed4','c40a277059434936b7661f482dd8245a','c82e3be7a76d4d028d44614469ffac7d','ca2f12949e104307ba6d8012010dead5','cd391ed8132d433daace19da07074f6f','d1ce0139807b41c5ad3243be9674899e','d5afaf9bfe0947ce84decbaba4a58b0a','d74591a1153b47498b8432239dbc0c08','de88d962441a43eeb3385e3fd5da3da3','f897331636c54ecbbc26f31fa9a9e6de','fb74767cb4c04f3087fb015ef734718c','fd55f04d97514738b83ba18ed5393ef9')
  order by t.create_time desc;
  </select>

  <select id="findOpenedRightList" resultType="com.mi.oa.ee.safety.common.dto.CardUserRight">
    select t1.uid,t1.partner_account as partnerAccount,t1.name as disPlayName,
           t4.supplier_access_code as supplierAccessCode,t4.name as groupName
    from `card_apply` t1 left join card_info t2 on t1.id = t2.card_apply_id
    left join safety_right t3 on t2.medium_code = t3.medium_code
    left join safety_carrier_group t4 on t3.carrier_group_code = t4.carrier_group_code
    where t1.uid = #{uid,jdbcType=VARCHAR} group by t1.uid,t2.medium_code,t4.supplier_access_code;
  </select>
  <select id="pageConditionListV3" resultType="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardInfoPO">
    select
    distinct
    <include refid="Base_Column_List_t"/>
    from card_info as t
    left join card_apply as t1 on t.card_apply_id = t1.id
    <if test="pageConditionDto.carrierGroupCode != null and pageConditionDto.carrierGroupCode != ''">
      left join safety_right as t2 on t2.medium_code = t.medium_code
    </if>
    left join card_time_validity as t4 on t4.card_id = t.id
    <where>
      and t.is_deleted = 0
      <if test="pageConditionDto.preLeaveUidList != null and pageConditionDto.preLeaveUidList.size() > 0">
        and t.uid in
        <foreach collection="pageConditionDto.preLeaveUidList" open="(" close=")" separator="," item="item">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="pageConditionDto.cityId != null">
        and t1.city_id = #{pageConditionDto.cityId,jdbcType=INTEGER}
      </if>
      <if test="pageConditionDto.employeeNo != null and pageConditionDto.employeeNo != ''">
        and t1.emp_no = #{pageConditionDto.employeeNo,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.phone != null and pageConditionDto.phone != ''">
        and t1.phone = #{pageConditionDto.phone,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.hasSpecialAuth != null">
        and t.has_special_auth = #{pageConditionDto.hasSpecialAuth,jdbcType=INTEGER}
      </if>
      <if test="pageConditionDto.cardType != null and pageConditionDto.cardType != 0">
        and t.card_type = #{pageConditionDto.cardType,jdbcType=INTEGER}
      </if>
      <if test="parkCodes != null and parkCodes.size() > 0">
        and (t1.park_code in
        <foreach collection="parkCodes" open="(" close=")" separator="," item="item">
          #{item,jdbcType=VARCHAR}
        </foreach>
        or t1.park_code = '')
      </if>
      <if test="pageConditionDto.parkCode != null and pageConditionDto.parkCode != ''">
        and t1.park_code = #{pageConditionDto.parkCode,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.carrierGroupCode != null and pageConditionDto.carrierGroupCode != ''">
        and t2.carrier_group_code = #{pageConditionDto.carrierGroupCode,jdbcType=VARCHAR}
        and t2.is_deleted = 0
      </if>
      <if test="pageConditionDto.firstDeptId != null and pageConditionDto.firstDeptId != ''">
        and t1.first_dept_id = #{pageConditionDto.firstDeptId,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.secondDeptId != null and pageConditionDto.secondDeptId != ''">
        and t1.second_dept_id = #{pageConditionDto.secondDeptId,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.thirdDeptId != null and pageConditionDto.thirdDeptId != ''">
        and t1.third_dept_id = #{pageConditionDto.thirdDeptId,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.fourthDeptId != null and pageConditionDto.fourthDeptId != ''">
        and t1.fourth_dept_id = #{pageConditionDto.fourthDeptId,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.cardStatus != null">
        and t.card_status = #{pageConditionDto.cardStatus,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.responsible != null and pageConditionDto.responsible != ''">
        and t1.responsible = #{pageConditionDto.responsible,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.uid != null and pageConditionDto.uid != ''">
        and t1.uid = #{pageConditionDto.uid,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.companyName != null and pageConditionDto.companyName != ''">
        and t1.company_name like concat('%',#{pageConditionDto.companyName,jdbcType=VARCHAR},'%')
      </if>
      <if test="pageConditionDto.mediumPhysicsCode != null and pageConditionDto.mediumPhysicsCode !=''">
        and t.medium_physics_code = #{pageConditionDto.mediumPhysicsCode,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.mediumEncryptCode != null and pageConditionDto.mediumEncryptCode !=''">
        and t.medium_encrypt_code = #{pageConditionDto.mediumEncryptCode,jdbcType=VARCHAR}
      </if>
      <if test="pageConditionDto.cardStatusList != null and pageConditionDto.cardStatusList.size() > 0">
        and t.card_status in
        <foreach collection="pageConditionDto.cardStatusList" open="(" close=")" separator="," item="item">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="pageConditionDto.expireTimeFrom != null">
        and t1.end_time >= #{pageConditionDto.expireTimeFrom, typeHandler=com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler}
      </if>
      <if test="pageConditionDto.expireTimeTo != null">
        and t1.end_time &lt;= #{pageConditionDto.expireTimeTo, typeHandler=com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler}
      </if>
      and t.card_status != 1
    </where>
    order by t.create_time desc
  </select>

</mapper>