<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.ee.safety.infra.repository.mybatis.mapper.VisitorParkParkingConfigMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.ee.safety.infra.repository.mybatis.entity.VisitorParkParkingConfigPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="park_code" jdbcType="VARCHAR" property="parkCode" />
    <result column="space_id" jdbcType="VARCHAR" property="spaceId" />
    <result column="is_private" jdbcType="BIT" property="isPrivate" />
    <result column="visit_type" jdbcType="INTEGER" property="visitType" />
    <result column="visitor_role" jdbcType="VARCHAR" property="visitorRole" />
    <result column="parking_guidance" jdbcType="VARCHAR" property="parkingGuidance" />
    <result column="parking_guidance_url" jdbcType="VARCHAR" property="parkingGuidanceUrl" />
    <result column="parking_receive_area" jdbcType="VARCHAR" property="parkingReceiveArea" />
    <result column="parking_receive_url" jdbcType="VARCHAR" property="parkingReceiveUrl" />
    <result column="parking_receive_key" jdbcType="VARCHAR" property="parkingReceiveKey" />
    <result column="is_apply_limit" jdbcType="BIT" property="isApplyLimit" />
    <result column="apply_limit_num" jdbcType="INTEGER" property="applyLimitNum" />
    <result column="parking_status" jdbcType="INTEGER" property="parkingStatus" />
    <result column="parking_reminder" jdbcType="VARCHAR" property="parkingReminder" />
    <result column="is_fm_open" jdbcType="BIT" property="isFmOpen" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
  </resultMap>
  <sql id="Base_Column_List">
    id, park_code, space_id, is_private, visit_type, visitor_role, parking_guidance, parking_guidance_url,
    parking_receive_area, parking_receive_url, parking_receive_key, is_apply_limit, apply_limit_num, 
    parking_status, parking_reminder, is_fm_open, is_deleted, create_user, create_time, update_user,
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from visitor_park_parking_config
    where id = #{id,jdbcType=BIGINT}
  </select>
</mapper>