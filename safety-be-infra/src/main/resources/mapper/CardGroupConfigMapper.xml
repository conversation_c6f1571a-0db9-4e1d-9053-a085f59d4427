<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.ee.safety.infra.repository.mybatis.mapper.CardGroupConfigMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardGroupConfigPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="card_group_code" jdbcType="VARCHAR" property="cardGroupCode"/>
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" typeHandler="com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler" />
  </resultMap>
  <sql id="Base_Column_List">
    id, card_group_code, card_group_name, control_type, data_source, province_id, apply_flag, carrier_group_code, city_id, dept_id, dept_name, dept_name_path, park_code, record_status, admin_user, remark, is_deleted, create_user, create_time, update_user, update_time
  </sql>
    <select id="conditionQueryList"
            resultType="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardGroupConfigEnhancePo">
      select t1.card_group_code,t1.card_group_name,t1.control_type,t1.data_source,t1.remark,t1.province_id,
             t1.apply_flag,t1.dept_id,t1.admin_user,t1.carrier_group_code,t1.record_status,
             t1.city_id,t1.dept_name,t1.dept_name_path,t1.id,t4.park_code,t4.building_code,t4.floor_code
      from card_group_config_record t1 left join safety_carrier_group t2 on FIND_IN_SET(t2.carrier_group_code,t1.carrier_group_code)
      LEFT JOIN safety_carrier_group_carrier t3 on t2.carrier_group_code = t3.carrier_group_code
      left join safety_carrier t4 on t3.carrier_code = t4.carrier_code
      where t1.is_deleted = 0 and t1.record_status = 0 and t2.is_deleted = 0 and t3.is_deleted = 0 and t4.is_deleted = 0
        and t2.carrier_group_type = 1 and t4.carrier_type = 1 and t1.apply_flag = 1
      <if test="query.cardGroupCodeList != null and query.cardGroupCodeList.size() > 0">
          and t1.card_group_code in
            <foreach collection="query.cardGroupCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
      </if>
      <if test="query.controlType != null and query.controlType != ''">
          and t1.control_type = #{query.controlType}
      </if>
      <if test="query.cardGroupName != null and query.cardGroupName != ''">
        and t1.card_group_name like concat('%', #{query.cardGroupName}, '%')
      </if>
      <if test="query.parkCode != null and query.parkCode != ''">
        and t1.park_code = #{query.parkCode}
      </if>
      group by t1.card_group_code,t4.park_code,t4.building_code,t4.floor_code
      order by t1.id desc
    </select>
  <select id="findListByCarrierGroupCode"
          resultType="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardGroupConfigPo">
    select
        <include refid="Base_Column_List"/>
        from card_group_config_record
    where find_in_set(#{carrierGroupCode}, carrier_group_code) and is_deleted = 0
  </select>

  <select id="pageQuery" resultType="com.mi.oa.ee.safety.infra.repository.mybatis.entity.CardGroupConfigPo">
    select t1.id, t1.card_group_code, t1.card_group_name, t1.control_type, t1.data_source,
           t1.province_id, t1.apply_flag, t1.carrier_group_code, t1.city_id, t1.dept_id,
           t1.dept_name, t1.dept_name_path, t1.park_code, t1.record_status, t1.admin_user,
           t1.remark, t1.is_deleted, t1.create_user, t1.create_time, t1.update_user, t1.update_time
    from card_group_config_record t1
    <if test="query.carrierGroupName != null and query.carrierGroupName != ''">
      left join safety_carrier_group t2 on FIND_IN_SET(t2.carrier_group_code, t1.carrier_group_code)
    </if>
    where t1.is_deleted = 0
    <if test="query.cardGroupName != null and query.cardGroupName != ''">
      and t1.card_group_name like concat('%', #{query.cardGroupName}, '%')
    </if>
    <if test="query.parkCode != null and query.parkCode != ''">
      and t1.park_code = #{query.parkCode}
    </if>
    <if test="query.cityId != null and query.cityId != ''">
      and t1.city_id = #{query.cityId}
    </if>
    <if test="query.provinceId != null and query.provinceId != ''">
      and t1.province_id = #{query.provinceId}
    </if>
    <if test="query.controlType != null and query.controlType != ''">
      and t1.control_type = #{query.controlType}
    </if>
    <if test="query.adminUid != null and query.adminUid != ''">
      and t1.admin_user like concat('%', #{query.adminUid}, '%')
    </if>
    <if test="query.cardGroupCode != null and query.cardGroupCode != ''">
      and t1.card_group_code = #{query.cardGroupCode}
    </if>
    <if test="query.applyFlag != null">
      and t1.apply_flag = #{query.applyFlag}
    </if>
    <if test="query.recordStatus != null">
      and t1.record_status = #{query.recordStatus}
    </if>
    <if test="query.carrierGroupName != null and query.carrierGroupName != ''">
      and t2.is_deleted = 0 and t2.name like concat('%', #{query.carrierGroupName}, '%')
    </if>
    <if test="query.deptNamePath != null and query.deptNamePath != ''">
      and t1.dept_name_path like concat(#{query.deptNamePath}, '%')
    </if>
    <if test="query.cardGroupCodeList != null and query.cardGroupCodeList.size() > 0">
      and t1.card_group_code in
      <foreach collection="query.cardGroupCodeList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="query.carrierGroupName != null and query.carrierGroupName != ''">
      group by t1.id
    </if>
    order by t1.id desc
  </select>
</mapper>