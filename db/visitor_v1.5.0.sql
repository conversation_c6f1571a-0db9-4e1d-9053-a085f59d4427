ALTER TABLE `visitor_office_park`
    MODIFY COLUMN `apply_type` int (4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '园区访客申请类型，1：普通，2：团访 3：接待, 4：临时驻场' AFTER `floor_name`,
    ADD COLUMN `park_notice_title` varchar (20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '来访须知标题' AFTER `visitor_role`,
    ADD COLUMN `park_notice_content` varchar (2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '来访须知内容' AFTER `park_notice_title`;

CREATE TABLE `visitor_apply_visitor_parking`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `apply_id`        bigint(20) NOT NULL DEFAULT '0' COMMENT '申请单ID',
    `visitor_info_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '申请访客ID',
    `plate_number`    varchar(16) NOT NULL DEFAULT '' COMMENT '车牌号',
    `is_release`      tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '车位资源是否释放：0 - 否，1 - 是',
    `is_deleted`      bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除：0：有效；非0：删除',
    `create_user`     varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`     bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_user`     varchar(64) NOT NULL DEFAULT '' COMMENT '最后更新人',
    `update_time`     bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '最后更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY               `idx_apply_id` (`apply_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='邀请单访客车辆信息表';

CREATE TABLE `visitor_park_parking_config`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `park_code`            varchar(128)  NOT NULL DEFAULT '' COMMENT '来访园区编码',
    `is_private`           tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否提供自有车位：0 - 否，1 - 是',
    `visit_type`           int(4) unsigned NOT NULL DEFAULT '1' COMMENT '访客类型，1：普通访客，2：高管访客',
    `visitor_role`         varchar(36)   NOT NULL DEFAULT '' COMMENT '车位访客角色',
    `parking_guidance`     varchar(200)  NOT NULL DEFAULT '' COMMENT '车位指引',
    `parking_guidance_url` varchar(128)  NOT NULL DEFAULT '' COMMENT '停车指引图',
    `parking_guidance_key` varchar(128)  NOT NULL DEFAULT '' COMMENT '停车指引图上传飞书img_key',
    `parking_receive_area` varchar(200)  NOT NULL DEFAULT '' COMMENT '接待区域',
    `parking_receive_url`  varchar(128)  NOT NULL DEFAULT '' COMMENT '接待指引图',
    `is_apply_limit`       tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '申请是否限制：0 - 否，1 - 是',
    `apply_limit_num`      int(8) unsigned NOT NULL DEFAULT '0' COMMENT '申请限制数量',
    `parking_status`       int(4) unsigned NOT NULL DEFAULT '0' COMMENT '车位配置状态：0 - 禁用，1 - 启用',
    `parking_reminder`     varchar(1000) NOT NULL DEFAULT '' COMMENT '车位申请提示语',
    `is_deleted`           bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除：0：有效；非0：删除',
    `create_user`          varchar(64)   NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`          bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_user`          varchar(64)   NOT NULL DEFAULT '' COMMENT '最后更新人',
    `update_time`          bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '最后更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='园区车位配置表';

CREATE TABLE `visitor_park_parking_time_slot`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `park_code`            varchar(128) NOT NULL DEFAULT '' COMMENT '园区编码',
    `parking_config_id`    bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '园区车位配置ID',
    `is_limit`             tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '车位是否限制：0 - 否，1 - 是',
    `parking_limit_num`    int(8) unsigned NOT NULL DEFAULT '0' COMMENT '车位限制数量',
    `parking_start_minute` int(8) unsigned NOT NULL DEFAULT '0' COMMENT '车位申请当日开始分钟数',
    `parking_end_minute`   int(8) unsigned NOT NULL DEFAULT '0' COMMENT '车位申请当日结束分钟数',
    `is_deleted`           bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除：0：有效；非0：删除',
    `create_user`          varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`          bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_user`          varchar(64)  NOT NULL DEFAULT '' COMMENT '最后更新人',
    `update_time`          bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '最后更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='园区车位配置时间段表';

-- 以下模板还没有替换生产环境
INSERT INTO `safety_ums_config` (`id`, `app_code`, `message_type`, `message_name`, `bot_biz_id`, `template_biz_id`,
                                 `template_content`, `params`)
VALUES (78, 'visitor', 3, '邀请单通过，短信通知访客', 'B0001', 'TMB000100148',
        '您好${visitorName!}先生/女士，${applyUserName!}邀请您于${visitDateTime!}到访小米。来访地址：${parkName!}(${parkAddressDetail!})，车辆信息：${visitorPlateNumber!}，${parkingGuidance!}。到访前微信搜索“小米访客”小程序完成信息登记，到达后请于访客区办理访客凭证。祝您来访愉快！',
        '{}');
INSERT INTO `safety_ums_config` (`id`, `app_code`, `message_type`, `message_name`, `bot_biz_id`, `template_biz_id`)
VALUES (79, 'visitor', 1, '车辆访客接待通知', 'B0198', 'TLB019800170'),
       (80, 'visitor', 2, '车辆访客邀约邮件', 'B0051', 'TEB005100378'),
       (81, 'visitor', 1, '海外车辆访客接待通知', 'B0198', 'TLB019800171'),
       (82, 'visitor', 3, '车辆访客到访预提醒', 'B0001', 'TMB000100149'),
       (83, 'visitor', 1, '车辆访客接待预提醒', 'B0198', 'TLB019800172'),
       (84, 'visitor', 2, '境外车辆访客到访预提醒', 'B0051', 'TEB005100379'),
       (85, 'visitor', 1, '境外车辆访客接待预提醒', 'B0198', 'TLB019800173'),
       (86, 'visitor', 1, '项目驻场异常告警', 'B0198', 'TLB019800174')
;

UPDATE `safety_ums_config`
SET `template_content` = '您好${visitorName!}先生/女士，${applyUserName!}邀请您于${visitDateTime!}到访小米。来访地址：${parkAddress!}。到访前微信搜索“小米访客”小程序完成信息登记，到达后请于访客区办理访客凭证。祝您来访愉快！'
WHERE `id` = 1;

UPDATE `safety_ums_config`
SET `template_content` = '您好${visitorName!}先生/女士，\n\r${applyUserName!}邀请您到${parkFullName!}驻场办公，驻场期间请您严格遵守小米安全管理相关规定，严禁进入保密区域；发生违规行为，将按规定处罚并追究法律责任。\n\r签入地点：${parkAddress!}，\n\r驻场期限：${visitDate!}至${visitEndDate!}，\n\r到访前微信搜索“小米访客”小程序完成信息登记，到达后请您按现场指引办理签入。感谢您的支持与配合！'
WHERE `id` = 18;