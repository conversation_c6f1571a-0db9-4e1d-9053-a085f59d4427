ALTER TABLE `visitor_office_park`
    ADD COLUMN `park_sort` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '排序值' AFTER `park_notice_content`,
    ADD COLUMN `building_sort` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '楼栋排序值' AFTER `park_sort`,
    ADD COLUMN `floor_sort` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '楼层排序值' AFTER `building_sort`;

ALTER TABLE `visitor_reception_config`
    ADD COLUMN `sort` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '排序值' AFTER `remark`,
    ADD COLUMN `is_free_parking` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否免费停车' AFTER `service_id_list`,
    ADD COLUMN `parking_service_desc` varchar(50) NOT NULL DEFAULT '' COMMENT '车位服务说明，不提供免费车位时提示语' AFTER `is_free_parking`,
    ADD COLUMN `meeting_service_desc` varchar(100) NOT NULL DEFAULT '' COMMENT '会议摆台说明' AFTER `parking_service_desc`;

INSERT INTO `visitor_reception_service` (`name`, `code`, `service_item_json`, `is_deleted`, `create_user`,
                                         `create_time`,
                                         `update_user`, `update_time`)
VALUES ('跨职场接待服务', 'crossPark',
        '[{"code": "crossParkName", "name": "跨职场名称", "type": "input", "bpmKey": "input_86612709e48e", "required": true, "terminal": ["pc", "larkmini"]}, {"code": "crossParkRequirement", "name": "跨职场接待要求", "type": "input", "bpmKey": "textarea_84db68848f7d", "required": false, "terminal": ["pc", "larkmini"]}]',
        0, 'b39545bd150448f7b40f8aba401ee2c2', 1697442026, 'b39545bd150448f7b40f8aba401ee2c2', 1697442026);

UPDATE `visitor_reception_service`
SET `code` = 'parking'
WHERE `id` = 1;
UPDATE `visitor_reception_service`
SET `code` = 'meeting'
WHERE `id` = 2;
UPDATE `visitor_reception_service`
SET `code` = 'dinner'
WHERE `id` = 3;
UPDATE `visitor_reception_service`
SET `code` = 'welcome'
WHERE `id` = 4;
UPDATE `visitor_reception_service`
SET `code` = 'confidentiality'
WHERE `id` = 5;

INSERT INTO `safety_ums_config` (`id`, `app_code`, `message_type`, `message_name`, `bot_biz_id`, `template_biz_id`)
VALUES (87, 'visitor', 1, '跨职场接待申请单创建飞书通知园区管理员', 'B0198', 'TLB019800175'),
       (88, 'visitor', 1, '高级接待明日接待提醒', 'B0198', 'TLB019800176');

update `visitor_reception_config`
set `is_free_parking`      = 1,
    `meeting_service_desc` = '将为您提供VIP摆台(名牌、茶杯、有品水、玻璃杯、垫纸板、纸笔、干湿纸巾等)';

ALTER TABLE `visitor_reception_service`
    ADD COLUMN `code` varchar(32) NOT NULL DEFAULT '' COMMENT '接待服务编码' AFTER `name`;

update `visitor_reception_config`
set `sort` = 100
where `sort` = 1;
update `visitor_office_park`
set `park_sort` = 100
where `park_sort` = 1;
update `visitor_office_park`
set `building_sort` = 100
where `building_sort` < 1
  and `building_code` != '';
update `visitor_office_park`
set `floor_sort` = 100
where `floor_sort` < 1
  and `floor_code` != '';