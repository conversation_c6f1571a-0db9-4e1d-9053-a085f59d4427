CREATE TABLE `visitor_office_park_admin`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `park_id`     bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '园区表id',
    `park_admin`  varchar(36) NOT NULL DEFAULT '' COMMENT '园区管理员uid',
    `is_deleted`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除：0：有效；非0：删除',
    `create_user` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_user` varchar(64) NOT NULL DEFAULT '' COMMENT '最后更新人',
    `update_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '最后更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='访客园区管理员表';

ALTER TABLE `visitor_office_park`
    ADD COLUMN `parkable` int(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否可停车：0 - 否，1 - 是' AFTER `park_status`;

ALTER TABLE `visitor_apply_visitor_info`
    ADD COLUMN `plate_number` varchar(16) NOT NULL DEFAULT '' COMMENT '车辆信息' AFTER `check_out_time`;

ALTER TABLE `visitor_apply_visitor_info`
    ADD COLUMN `visit_type` int(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '访客类型，1：普通访客，2：高管访客' AFTER `visit_code`;

ALTER TABLE `visitor_apply_visitor_info`
    ADD COLUMN `privacable` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否可隐私：0 - 否，1 - 是' AFTER `plate_number`;

INSERT INTO `safety_ums_config` (`id`, `app_code`, `message_type`, `message_name`, `bot_biz_id`, `template_biz_id`)
VALUES (15, 'visitor', 1, '今日高管访客提醒', 'B0198', 'TLB019800120'),
       (16, 'visitor', 1, '高管访客取消提醒', 'B0198', 'TLB019800119'),
       (17, 'visitor', 1, '明日高管访客提醒', 'B0198', 'TLB019800118');

UPDATE `safety_ums_config`
SET `template_content` = '${visitorName!}您好，${receiverName!}邀请您于${visitDate!}到访小米。来访地址：${parkAddress!}。到访前微信搜索“小米访客”小程序完成信息登记，到达后请于访客区办理访客凭证。祝您来访愉快！'
WHERE `id` = 1;
