create table card_electron_user (
    id            bigint unsigned auto_increment comment 'ID',
    `uid`         VARCHAR(36)  NOT NULL DEFAULT '' COMMENT '用户UID',
    `remark`              VARCHAR(255) NOT NULL DEFAULT '' COMMENT '备注',
    is_deleted    bigint(20) default 0 not null comment '是否删除：0：有效；非0：删除',
    create_user   varchar(32)  default '' not null comment '创建人',
    create_time   bigint unsigned default 0 not null comment '创建时间',
    update_user   varchar(32)  default '' not null comment '最后更新人',
    update_time   bigint unsigned default 0 not null comment '最后更新时间',
    PRIMARY KEY (id),
    UNIQUE INDEX `uidx_uid` (`uid`) USING BTREE,
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 comment '电子卡用户表';

ALTER TABLE `safety_person_medium`
    ADD COLUMN `is_main` int(4) NOT NULL DEFAULT '1' COMMENT '是否主要关系 1:是  0：否';






