package com.mi.oa.ee.safety.application.impl.common.export;

import com.mi.info.comb.neptune.client.NeptuneClient;
import com.mi.oa.ee.safety.application.dto.card.shared.CardApplyImportExcelDto;
import com.mi.oa.ee.safety.application.dto.card.shared.DoBusinessImportDto;
import com.mi.oa.ee.safety.application.service.card.shared.CommonCardService;
import com.mi.oa.ee.safety.application.service.common.CommonService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.enums.ImportResultEnum;
import com.mi.oa.ee.safety.common.enums.SafetyPersonStatusEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyConfigUserTypeEnum;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;
import com.mi.oa.ee.safety.domain.service.SafetyPersonDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.mi.oa.ee.safety.domain.enums.ImportExceptionEnum;
import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service(CardBatchPermissionGroupDeleteImportExecutorImpl.SERVICE_NAME)
public class CardBatchPermissionGroupDeleteImportExecutorImpl extends CardApplyAbstractImportExecutor {

    public static final String SERVICE_NAME = "cardBatchPermissionGroupDeleteImportExecutor";

    @Resource
    private CommonCardService commonCardService;

    @Resource
    CommonService commonService;
    @Override
    protected String getFileName() {
        return NeptuneClient.getInstance().parseEntryTemplate("{{{批量删除权限包导入结果}}}") + SafetyConstants.Common.UNDER_LINE
                + CodeUtils.generateVisitorCode(6, true) + SafetyConstants.Common.POINT + SafetyConstants.Common.EXCEL_SUFFIX;
    }

    @Override
    void doBusinessForCard(DoBusinessImportDto businessParam, Map<String, String> accountEmpNoMap) {
        commonCardService.batchDeletePermissionGroup(businessParam, accountEmpNoMap);
    }

    @Override
    Boolean checkImportExcelDto(CardApplyImportExcelDto cardApplyImportExcelDto) {
        return commonService.check(cardApplyImportExcelDto);
    }
}
