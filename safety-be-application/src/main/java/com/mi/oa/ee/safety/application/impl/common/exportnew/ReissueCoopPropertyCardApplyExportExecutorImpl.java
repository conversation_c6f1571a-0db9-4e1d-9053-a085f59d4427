package com.mi.oa.ee.safety.application.impl.common.exportnew;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializer;
import com.mi.info.comb.neptune.client.NeptuneClient;
import com.mi.oa.ee.safety.application.dto.card.reissue.ReissueCardApplyDto;
import com.mi.oa.ee.safety.application.dto.common.AsyncExportTaskDto;
import com.mi.oa.ee.safety.application.dto.common.AsyncReissueCardApplyExportQueryDto;
import com.mi.oa.ee.safety.application.dto.common.AsyncReissueCoopPropCardApplyExportExcelDto;
import com.mi.oa.ee.safety.application.impl.common.export.AbstractExportDataExecutor;
import com.mi.oa.ee.safety.application.impl.common.export.data.ReissueCoopPropertyCardApplyExportDataExecutor;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.deserializer.ZonedDateTimeDeserializer;
import com.mi.oa.ee.safety.common.excel.function.ExportFunction;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.domain.enums.ExportTypeEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZonedDateTime;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/7/15 15:08
 */
@Service(ReissueCoopPropertyCardApplyExportExecutorImpl.SERVICE_NAME)
public class ReissueCoopPropertyCardApplyExportExecutorImpl extends AbstractExportDataExecutor<
        AsyncReissueCardApplyExportQueryDto,
        ReissueCardApplyDto,
        AsyncReissueCoopPropCardApplyExportExcelDto> {

    public static final String SERVICE_NAME = "reissueCoopPropertyCardApplyExportExecutor";

    @Resource
    private ReissueCoopPropertyCardApplyExportDataExecutor reissueCoopPropertyCardApplyExportDataExecutor;

    //todo 为了解决getFileName方法中的问题而采取的临时方案 后续应该把合作卡和物业卡的补办申请列表导出分开
    private AsyncExportTaskDto tempAsyncExportTaskDto;

    @Override
    protected AsyncReissueCardApplyExportQueryDto getParam(AsyncExportTaskDto asyncExportTaskDto) {
        tempAsyncExportTaskDto = asyncExportTaskDto;
        Gson gson = new GsonBuilder().registerTypeAdapter(Integer.class, (JsonDeserializer<Integer>) (json, typeOfT, context) -> {
            if (json.getAsString().isEmpty()) {
                return null;  // 或者返回一个默认值，例如 0
            }
            try {
                return json.getAsInt();
            } catch (NumberFormatException e) {
                return null;  // 或者返回一个默认值，例如 0
            }
        }).registerTypeAdapter(ZonedDateTime.class, new ZonedDateTimeDeserializer()).create();
        // 自定义gson 因为传值为空可能会报错
        AsyncReissueCardApplyExportQueryDto asyncReissueCardApplyExportQueryDto =
                gson.fromJson(asyncExportTaskDto.getParam(), AsyncReissueCardApplyExportQueryDto.class);
        asyncReissueCardApplyExportQueryDto.setTaskId(asyncExportTaskDto.getId());
        return asyncReissueCardApplyExportQueryDto;
    }

    @Override
    protected ExportFunction<AsyncReissueCardApplyExportQueryDto, ReissueCardApplyDto, AsyncReissueCoopPropCardApplyExportExcelDto> getExportFunction() {
        return reissueCoopPropertyCardApplyExportDataExecutor;
    }

    @Override
    protected Class<AsyncReissueCoopPropCardApplyExportExcelDto> exportType() {
        return AsyncReissueCoopPropCardApplyExportExcelDto.class;
    }

    @Override
    protected String getFileName() {
        if (tempAsyncExportTaskDto == null) {
            return NeptuneClient.getInstance().parseEntryTemplate(ExportTypeEnum.REISSUE_CARD_APPLY_LIST_EXPORT.getDesc()) +
                    SafetyConstants.Common.UNDER_LINE
                    + CodeUtils.generateVisitorCode(6, true) + SafetyConstants.Common.POINT + SafetyConstants.Common.CSV_SUFFIX;
        }
        if (tempAsyncExportTaskDto.getOpType().equals(ExportTypeEnum.REISSUE_COOP_PROPERTY_CARD_APPLY_LIST_EXPORT.getType())) {
            return NeptuneClient.getInstance().parseEntryTemplate(ExportTypeEnum.REISSUE_COOP_PROPERTY_CARD_APPLY_LIST_EXPORT.getDesc())
                    + SafetyConstants.Common.UNDER_LINE
                    + CodeUtils.generateVisitorCode(6, true) + SafetyConstants.Common.POINT + SafetyConstants.Common.CSV_SUFFIX;
        } else {
            return NeptuneClient.getInstance().parseEntryTemplate(ExportTypeEnum.REISSUE_PROP_PROPERTY_CARD_APPLY_LIST_EXPORT.getDesc())
                    + SafetyConstants.Common.UNDER_LINE
                    + CodeUtils.generateVisitorCode(6, true) + SafetyConstants.Common.POINT + SafetyConstants.Common.CSV_SUFFIX;
        }
    }
}
