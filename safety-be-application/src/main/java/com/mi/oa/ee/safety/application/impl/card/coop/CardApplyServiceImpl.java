package com.mi.oa.ee.safety.application.impl.card.coop;

import cn.hutool.core.date.DateTime;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Lists;
import com.mi.oa.ee.safety.application.converter.card.CardApplyDtoConverter;
import com.mi.oa.ee.safety.application.converter.safety.SafetySpaceConverter;
import com.mi.oa.ee.safety.application.dto.card.BatchModifyParkDto;
import com.mi.oa.ee.safety.application.dto.card.CardAvatarRemoteDto;
import com.mi.oa.ee.safety.application.dto.card.CardPartnerApplyDto;
import com.mi.oa.ee.safety.application.dto.card.coop.CoopCardApplyEditDto;
import com.mi.oa.ee.safety.application.dto.card.shared.CardApplyImportDto;
import com.mi.oa.ee.safety.application.dto.visitor.BpmCallBackDto;
import com.mi.oa.ee.safety.application.errorcode.CardApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.application.service.card.coop.CardApplyService;
import com.mi.oa.ee.safety.application.service.common.event.ImportEventHandler;
import com.mi.oa.ee.safety.application.service.common.event.model.SafetyExportEvent;
import com.mi.oa.ee.safety.application.service.common.event.model.SafetyImportEvent;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.BatchOperateDto;
import com.mi.oa.ee.safety.common.dto.CardRemoteDto;
import com.mi.oa.ee.safety.common.dto.ImportErrorDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceParkDto;
import com.mi.oa.ee.safety.common.enums.*;
import com.mi.oa.ee.safety.common.enums.card.*;
import com.mi.oa.ee.safety.common.enums.safety.SafetyConfigUserTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyUmsConfigEnum;
import com.mi.oa.ee.safety.common.utils.Asserts;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.domain.ability.CardPersonInfoAbility;
import com.mi.oa.ee.safety.domain.ability.SafetyPersonAbility;
import com.mi.oa.ee.safety.domain.converter.CardPersonInfoDoConverter;
import com.mi.oa.ee.safety.domain.enums.ExportStatusEnum;
import com.mi.oa.ee.safety.domain.enums.ExportTypeEnum;
import com.mi.oa.ee.safety.domain.errorcode.CardApplyDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.*;
import com.mi.oa.ee.safety.domain.service.*;
import com.mi.oa.ee.safety.infra.errorcode.InfraErrorCodeEnum;
import com.mi.oa.ee.safety.infra.remote.fds.FdsRemote;
import com.mi.oa.ee.safety.infra.remote.sdk.BpmSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.IdmSdk;
import com.mi.oa.ee.safety.infra.repository.*;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.idm.api.rep.UpdatePersonDto;
import com.mi.oa.infra.oaucf.newauth.core.dto.UserInfoDto;
import com.mi.oa.infra.oaucf.newauth.core.userdetail.IdmRemote;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2022/11/21 17:11
 */
@Slf4j
@Service
public class CardApplyServiceImpl implements CardApplyService {

    @Resource
    CardApplyDomainService cardApplyDomainService;

    @Resource
    CardApplyDtoConverter cardApplyDtoConverter;

    @Resource
    CardDomainService cardDomainService;

    @Resource
    IdmSdk idmSdk;

    @Resource
    SafetySpaceConverter spaceConverter;

    @Resource
    SafetySpaceDomainService spaceDomainService;

    @Resource
    SafetyPersonDomainService safetyPersonDomainService;

    @Resource
    CardApplyRepository cardApplyRepository;

    @Resource
    SafetyUmsNotifyDomainService safetyUmsNotifyDomainService;

    @Resource
    AsyncImportTaskDomainService asyncImportTaskDomainService;

    @Resource
    private ImportEventHandler importEventHandler;

    @Resource
    SafetyUmsNotifyRepository safetyUmsNotifyRepository;

    @Resource
    IdmRemote idmRemote;

    @Resource
    private CardTimeValidateRepository cardTimeValidateRepository;

    @Autowired
    private CardPersonInfoDoConverter cardPersonInfoDoConverter;
    @Autowired
    private CardPersonInfoAbility cardPersonInfoAbility;

    @Autowired
    private CardPersonInfoDomainService cardPersonInfoDomainService;

    @Resource
    AsyncExportTaskDomainService asyncExportTaskDomainService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private FdsRemote fdsRemote;

    @Resource
    private UserBehaviorRecordRepository userBehaviorRecRepository;

    @Resource
    private SafetyPersonAbility safetyPersonAbility;

    @NacosValue(value = "${card.prefix-encrypt-code:201046}", autoRefreshed = true)
    private String prefixEncryptCode;

    @Resource
    private SafetyOperateLogRepository safetyOperateLogRepository;

    @Resource(name = "notifyExternalCallThreadPoolExecutor")
    private ThreadPoolExecutor asyncExternalCallThreadPool;

    @Resource
    private BpmSdk bpmSdk;

    @Override
    public void batchImportEmpCardApply(CardApplyImportDto importDto) {
        AsyncImportTaskDo asyncImportRecordDo = initAsyncImportRecord(importDto, ImportTypeEnum.EMP_CARD_APPLY_IMPORT);
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        asyncImportRecordDo.setOperator(userInfoDto.getUid());
        asyncImportRecordDo.setOperatorName(userInfoDto.getUserName());
        asyncImportTaskDomainService.save(asyncImportRecordDo);
        importEventHandler.handleImportEvent(new SafetyImportEvent(asyncImportRecordDo, asyncImportRecordDo.getBatchId()));
    }

    private AsyncImportTaskDo initAsyncImportRecord(CardApplyImportDto importDto, ImportTypeEnum importTypeEnum) {
        AsyncImportTaskDo asyncImportTaskDo = new AsyncImportTaskDo();
        String seq = CodeUtils.getUUID();
        asyncImportTaskDo.setOperator(importDto.getOperator());
        asyncImportTaskDo.setBatchId(seq);
        asyncImportTaskDo.setMemo(importDto.getCardType());
        asyncImportTaskDo.setFileName(importDto.getFileName());
        asyncImportTaskDo.setOperatorName(importDto.getOperatorName());
        asyncImportTaskDo.setOpTime(ZonedDateTime.now());
        asyncImportTaskDo.setOpTypeEnum(importTypeEnum);
        asyncImportTaskDo.setExpireTime(Instant.now().getEpochSecond() + SafetyConstants.Common.QUART_HOUR);
        asyncImportTaskDo.setUrl(importDto.getUrl());
        asyncImportTaskDo.setStatusEnum(ImportStatusEnum.TO_EXECUTE);
        return asyncImportTaskDo;
    }

    @Override
    public void confirmApply(CardPartnerApplyDto cardPartnerApplyDto, String nowUid) {
        CardApplyDo cardApplyDo = cardApplyDtoConverter.toDo(cardPartnerApplyDto);
        if (cardApplyDo.getId() == null) {
            throw new BizException(CardApplicationErrorCodeEnum.CARD_APPLY_ID_IS_EMPTY);
        }
        if (cardApplyDo.getConfirmStatus() == null) {
            throw new BizException(CardApplicationErrorCodeEnum.CARD_APPLY_CONFIRM_STATUS_IS_EMPTY);
        }
        //只有“待审核”可以确认
        CardApplyDo oldCardApplyDo = cardApplyDomainService.getDetailCardApply(cardApplyDo.getId());
        if (!nowUid.equals(oldCardApplyDo.getUid())) {
            throw new BizException(CardApplicationErrorCodeEnum.CARD_APPLY_ID_IS_NOT_NOW_UID);
        }
        if (!(CardApplyStatusEnum.WAIT_CONFIRMED.getCode().equals(oldCardApplyDo.getApplyStatus()) ||
                CardApplyStatusEnum.WAIT_UPLOAD.getCode().equals(oldCardApplyDo.getApplyStatus()))) {
            throw new BizException(CardApplicationErrorCodeEnum.CARD_APPLY_APPLY_STATUS_IS_ERROR);
        }
        //如果是已确认没问题，则状态改成“待审核”
        if (OAUCFCommonConstants.INT_ONE.equals(cardApplyDo.getConfirmStatus())) {
            cardApplyDo.setApplyStatus(CardApplyStatusEnum.UNDER_APPROVAL.getCode());
        }
        cardApplyRepository.updateWithCardTimeById(cardApplyDo);
        //同时更新人员信息表照片
        cardPersonInfoAbility.updatePhotoUrl(nowUid, cardApplyDo.getPhotoUrl());

    }

    @Override
    public CardRemoteDto saveCoopCardApplyForOnSite(CardPartnerApplyDto cardPartnerApplyDto) {
        //转换Do
        CardApplyDo cardApplyDo = cardApplyDtoConverter.toDo(cardPartnerApplyDto);
        //驻场创建remark需填充
        cardApplyDo.setRemark(CardApplyRemarkEnum.ONSITE.getCode());
        //创建前填充申请单领域对象 根据申请人的uid和申请类型去card_apply这张表中去找数据，还有填充一些人员信息 根据合作卡类型去找
        cardApplyDo.setApplyType(CardApplyTypeEnum.COOPERATION_CARD_APPLY.getCode());
        cardApplyDomainService.fillBeforeCreate(cardApplyDo);
        // 再根据物业卡类型去找
        CardApplyDo cardApplyDoProperty = cardApplyDtoConverter.toDo(cardPartnerApplyDto);
        cardApplyDoProperty.setApplyType(CardApplyTypeEnum.PROPERTY_CARD_APPLY.getCode());
        cardApplyDomainService.fillBeforeCreate(cardApplyDoProperty);

        //驻场合作卡申请单创建前检查
        cardApplyDomainService.checkBeforeCreateForOnSite(cardApplyDo);
        //如果当前人员已经有对应的申请单时
        if (cardApplyDo.getId() != null && cardApplyDoProperty.getId() == null) {
            //先保存有效时间再更新申请单
            cardApplyDomainService.checkValidateTimeBeforeUpdate(cardApplyDo);
            //重新填装对应的申请单状态
            fillApplyStatusAndSendUmsForOnsite(cardApplyDo, cardPartnerApplyDto.getIsNeedPhoto());
            cardApplyRepository.updateWithCardTimeById(cardApplyDo);
            //是否存在已销卡记录
            CardInfoDo cardInfoDo = cardDomainService.findCardInfoByApplyId(cardApplyDo.getId());
            if (Objects.nonNull(cardInfoDo) && CardStatusEnum.CANCELED.getCode().equals(cardInfoDo.getCardStatus())) {
                log.error("工卡已存在且状态已销卡人员:{}", cardApplyDo);
            }
        } else if (cardApplyDo.getId() == null && cardApplyDoProperty.getId() == null) {
            //填充前 若是从驻场过来 有身份证信息则 同步更新到idm
            Boolean isNeedUpdateIdm = cardApplyDomainService.judgeIsNeedUpdateIdm(cardApplyDo);
            if (isNeedUpdateIdm) {
                idmSdk.updatePersonIdCardNumber(cardApplyDo);
            }
            //填充申请单领域对象
            cardApplyDomainService.fillFullCardApply(cardApplyDo);
            //保存当前申请单
            saveCardApplyDo(cardApplyDo, cardPartnerApplyDto.getIsNeedPhoto());
        } else {
            //无合作卡有物业卡申请
            return buildCardRemoteDto(cardApplyDoProperty);
        }
        //保存人信息
        CardPersonInfoDo cardPersonInfoDo = cardPersonInfoDoConverter.toCardPersonInfoDo(cardApplyDo);
        cardPersonInfoDomainService.savePerson(cardPersonInfoDo);
        return buildCardRemoteDto(cardApplyDo);
    }

    private void fillApplyStatusAndSendUmsForOnsite(CardApplyDo cardApplyDo, Boolean isNeedPhoto) {
        if (CardApplyStatusEnum.REFUSED.getCode().equals(cardApplyDo.getApplyStatus())
                || CardApplyStatusEnum.CANCELED.getCode().equals(cardApplyDo.getApplyStatus())) {
            //保存当前申请单，更新对应的
            Integer applyStatus = isNeedPhoto ? CardApplyStatusEnum.WAIT_PHOTO.getCode() : CardApplyStatusEnum.WAIT_UPLOAD.getCode();
            cardApplyDo.setApplyStatus(applyStatus);
            //发送拍照或自行上传提醒
            chooseSendPartnerUms(cardApplyDo, isNeedPhoto);
        }
    }

    private void saveCardApplyDo(CardApplyDo cardApplyDo, Boolean isNeedPhoto) {
        //保存当前申请单
        cardApplyDo.setEmpType(AccountTypeEnum.PARTNER.getValue());
        Integer applyStatus = isNeedPhoto ? CardApplyStatusEnum.WAIT_PHOTO.getCode() : CardApplyStatusEnum.WAIT_UPLOAD.getCode();
        cardApplyDo.setApplyStatus(applyStatus);
        Long applyId = cardApplyRepository.save(cardApplyDo);
        cardApplyDo.setId(applyId);
        CardTimeValidityDo cardTimeValidityDo = buildCardTimeValidate(cardApplyDo);
        cardTimeValidateRepository.insert(cardTimeValidityDo);
        //发送拍照或自行上传提醒
        chooseSendPartnerUms(cardApplyDo, isNeedPhoto);
    }

    private CardTimeValidityDo buildCardTimeValidate(CardApplyDo cardApplyDo) {
        CardTimeValidityDo cardTimeValidityDo = new CardTimeValidityDo();
        cardTimeValidityDo.setCardApplyId(cardApplyDo.getId());
        cardTimeValidityDo.setStartTime(cardApplyDo.getStartTime());
        cardTimeValidityDo.setEndTime(cardApplyDo.getEndTime());
        cardTimeValidityDo.setUid(cardApplyDo.getUid());
        return cardTimeValidityDo;
    }

    /**
     * @param isNeedPhoto
     * @return void
     * @desc 依据驻场是否需要拍照推送 拍照提醒或自行上传提醒
     * <AUTHOR> denghui
     * @date 2024/2/2 15:27
     */
    private void chooseSendPartnerUms(CardApplyDo cardApplyDo, Boolean isNeedPhoto) {
        if (isNeedPhoto) {
            //发送拍照的UMS短息消息
            sendPhotographNotice(cardApplyDo);
        } else {
            //发送自行上传的UMS短信消息
            sendSelfUploadUmsNotify(cardApplyDo);
        }
    }

    private CardRemoteDto buildCardRemoteDto(CardApplyDo cardApplyDo) {
        CardRemoteDto cardRemoteDto = new CardRemoteDto();
        //组装工卡ID信息
        if (CardApplyStatusEnum.WAIT_OPEN_CARD.getCode().equals(cardApplyDo.getApplyStatus())
                || CardApplyStatusEnum.WAIT_PRINT_CARD.getCode().equals(cardApplyDo.getApplyStatus())) {
            cardRemoteDto.setIsAvailable(OAUCFCommonConstants.INT_ONE);
        } else if (Objects.nonNull(cardApplyDo.getCardInfo()) && Objects.nonNull(cardApplyDo.getCardInfo().getId())) {
            cardRemoteDto.setIsAvailable(OAUCFCommonConstants.INT_ONE);
            cardRemoteDto.setCardId(cardApplyDo.getCardInfo().getId());
        } else {
            cardRemoteDto.setIsAvailable(OAUCFCommonConstants.INT_ZERO);
        }
        cardRemoteDto.setCardApplyId(cardApplyDo.getId());
        return cardRemoteDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CardRemoteDto saveCoopCard(CardPartnerApplyDto cardPartnerApplyDto) {
        CardApplyDo cardApplyDo = cardApplyDtoConverter.toDo(cardPartnerApplyDto);
        //推送idm
        cardApplyDomainService.doIdmCreatePerson(cardApplyDo);
        // 在创建合作卡或者物业卡之前检查 物业卡有数据则合作卡不可创建
        cardApplyDomainService.checkBeforeSaveCoopOrPropertyCard(cardApplyDo);
        //创建前填充申请单领域对象
        cardApplyDomainService.fillBeforeCreate(cardApplyDo);
        //创建人员信息
        CardPersonInfoDo cardPersonInfoDo = cardPersonInfoDoConverter.toCardPersonInfoDo(cardApplyDo);
        cardPersonInfoDomainService.savePerson(cardPersonInfoDo);
        //驻场合作卡申请单创建前检查
        cardApplyDomainService.checkBeforeCreateForCoopCard(cardApplyDo);
        //若不是驻场 且已有人员 需校验工卡有效期时间是否超过idm合作周期
        cardApplyDomainService.checkValidateTimeIsExceedIdmTime(cardApplyDo);
        //保存当前申请单
        cardApplyDo.setEmpType(AccountTypeEnum.PARTNER.getValue());
        cardApplyDo.setStartTime(null);
        cardApplyDo.setEndTime(null);
        if (Objects.isNull(cardApplyDo.getId())) {
            cardApplyDo.setApplyStatus(CardApplyStatusEnum.BPM_APPROVAL.getCode());
            Long applyId = cardApplyRepository.save(cardApplyDo);
            cardApplyDo.setId(applyId);
        } else if (CardApplyStatusEnum.REFUSED.getCode().equals(cardApplyDo.getApplyStatus())) {
            cardApplyDo.setApplyStatus(CardApplyStatusEnum.BPM_APPROVAL.getCode());
            cardApplyRepository.updateById(cardApplyDo);
        }
        return buildCardRemoteDto(cardApplyDo);
    }

    @Override
    public CardPartnerApplyDto getDetailCardApply(Long id, String nowUid) {
        CardApplyDo cardApplyDo = cardApplyDomainService.getDetailCardApply(id);
        if (!StringUtils.isEmpty(nowUid) && cardApplyDo != null && !nowUid.equals(cardApplyDo.getUid())) {
            throw new BizException(CardApplicationErrorCodeEnum.CARD_APPLY_ID_IS_NOT_NOW_UID);
        }
        return cardApplyDtoConverter.toDto(cardApplyDo);
    }

    @Override
    public CardPartnerApplyDto getPersonInfoByPhone(String phone) {
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setMobile(phone);
        safetyPersonDo.setOrgTreeCode(SafetyConfigUserTypeEnum.EMPLOYEE.getOrgTreeCode());
        safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByPhoneAndOrgCode(safetyPersonDo);

        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardApplyDomainService.fillCoopCardApplyByPerson(cardApplyDo);
        return cardApplyDtoConverter.toDto(cardApplyDo);
    }

    @Override
    public PageModel<CardPartnerApplyDto> pageConditionApply(CardPartnerApplyDto dto, Boolean flag) {
        CardApplyDo cardApplyDo = cardApplyDtoConverter.toDo(dto);
        cardApplyDo.putExtField("isShowBpmApproval", dto.getIsShowBpmApproval());
        PageModel<CardApplyDo> pageModel = cardApplyDomainService.pageConditionApplyList(cardApplyDo, dto.getPageNum(),
                dto.getPageSize(), flag);
        if (CollectionUtils.isNotEmpty(pageModel.getList())) {
            pageModel.getList().stream().parallel().forEach(item -> {
                cardApplyDomainService.fillFullCardApply(item);
            });
            pageModel.getList().sort((o1, o2) -> ZonedDateTimeUtils.compare(o2.getCreateTime(), o1.getCreateTime()));
        }
        return PageModel.build(cardApplyDtoConverter.toDtoList(pageModel.getList()), pageModel.getPageSize(),
                pageModel.getPageNum(), pageModel.getTotal());
    }

    @Override
    public List<ImportErrorDto> batchSaveApply(MultipartFile queryData) {
        List<CardApplyDo> cardApplyDos = Lists.newArrayList();
        parsingExcel(queryData, cardApplyDos);
        return cardApplyDomainService.batchSave(cardApplyDos);
    }

    @Override
    public void uploadPhoto(Long id, String photoUrl) {
        cardApplyDomainService.uploadPhoto(id, photoUrl);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadPhotoBySupplier(Long id, String photoUrl) {
        //检查当前供应商是否可上传
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(id);
        cardApplyDomainService.fillCardApplyDoById(cardApplyDo);

        //添加当前图片，校验是否可上传
        cardApplyDo.setPhotoUrl(photoUrl);
        cardApplyDomainService.checkCanSupplierUploadPhoto(cardApplyDo);

        //更新状态为待确认
        cardApplyDo.setApplyStatus(CardApplyStatusEnum.WAIT_CONFIRMED.getCode());
        cardApplyRepository.updateWithCardTimeById(cardApplyDo);

        //更新人员信息照片
        cardPersonInfoAbility.updatePhotoUrl(cardApplyDo.getUid(), photoUrl);

        //发送消息
        uploadBySupplierReSend(cardApplyDo);

        //记录日志
        saveOperateLog(cardApplyDo);
    }

    private void saveOperateLog(CardApplyDo cardApplyDo) {
        String loginUid = idmRemote.getLoginUid();
        SafetyOperateLogDo safetyOperateLogDo = new SafetyOperateLogDo();
        safetyOperateLogDo.setAppCode(AppCodeEnum.CARD.getAppCode());
        safetyOperateLogDo.setBizId(String.valueOf(cardApplyDo.getId()));
        safetyOperateLogDo.setRequestUrl(LogOperateType.CARD_UPLOAD_PHOTO.getCode());
        safetyOperateLogDo.setOperateStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
        safetyOperateLogDo.setOperateDesc(LogOperateType.CARD_UPLOAD_PHOTO.getDesc());
        safetyOperateLogDo.setCreateTime(ZonedDateTime.now());
        safetyOperateLogDo.setUpdateTime(ZonedDateTime.now());
        safetyOperateLogDo.setUpdateUser(loginUid);
        safetyOperateLogDo.setCreateUser(loginUid);
        safetyOperateLogRepository.asyncBatchSaveOrUpdate(Lists.newArrayList(safetyOperateLogDo),
                SecurityContextHolder.getContext());
    }

    @Override
    public void uploadPhotoBySupplierReSend(Long id) {
        //检查当前供应商是否可上传
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(id);
        cardApplyDomainService.fillCardApplyDoById(cardApplyDo);

        //发送消息
        uploadBySupplierReSend(cardApplyDo);
    }

    private void uploadBySupplierReSend(CardApplyDo cardApplyDo) {
        //创建邮件通知用户确认
        SafetyUmsNotifyDo safetyUmsNotifyDo = new SafetyUmsNotifyDo();
        safetyUmsNotifyDo.setCardApplyDo(cardApplyDo);
        safetyUmsNotifyDo.setAppCode(AppCodeEnum.CARD.getAppCode());
        safetyUmsNotifyDomainService.buildCardApplyWaitConfirmUmsNotify(safetyUmsNotifyDo);

        if (CardApplyRemarkEnum.ONSITE.getCode().equals(cardApplyDo.getRemark())) {
            //发短信通知用户确认
            safetyUmsNotifyDo.setConfigId(SafetyUmsConfigEnum.WAIT_CONFIRMED_SMS.getCode());
            safetyUmsNotifyDomainService.checkSafetyUmsNotify(safetyUmsNotifyDo);
            safetyUmsNotifyRepository.createNotify(safetyUmsNotifyDo);
        } else {
            //发邮件通知用户确认
            safetyUmsNotifyDo.setConfigId(SafetyUmsConfigEnum.WAIT_CONFIRMED_EMAIL.getCode());
            safetyUmsNotifyDomainService.checkSafetyUmsNotify(safetyUmsNotifyDo);
            safetyUmsNotifyRepository.createNotify(safetyUmsNotifyDo);

            //发飞书通知用户确认
            safetyUmsNotifyDo.setConfigId(SafetyUmsConfigEnum.WAIT_CONFIRMED_LARK.getCode());
            safetyUmsNotifyDomainService.checkSafetyUmsNotify(safetyUmsNotifyDo);
            safetyUmsNotifyRepository.createNotify(safetyUmsNotifyDo);
        }
    }

    @Override
    public void batchOperate(BatchOperateDto batchOperateDto) {
        cardApplyDomainService.batchOperate(batchOperateDto);
        //更新当前的领取地园区信息
        if (StringUtils.isEmpty(batchOperateDto.getParkCode()) &&
                CardApplyOperateTypeEnum.OPERATE_NOTIFY_RECEIVE.getCode().equals(batchOperateDto.getOperateType())) {
            for (Long id : batchOperateDto.getIds()) {
                CardApplyDo cardApplyDo = new CardApplyDo();
                cardApplyDo.setId(id);
                cardApplyDo.setReceiptParkCode(batchOperateDto.getParkCode());
                cardApplyRepository.updateWithCardTimeById(cardApplyDo);
            }
        }
    }

    @Override
    public boolean hasCarrierGroups(String parkCode) {
        return cardApplyDomainService.hasCarrierGroups(parkCode);
    }

    @Override
    public String getReceiptAddress(String parkCode) {
        return cardApplyDomainService.getReceiptAddress(parkCode);
    }

    @Override
    public void updateByUid(CardPartnerApplyDto cardPartnerApplyDto, com.mi.oa.ee.safety.common.dto.UpdatePersonDto req) {
        CardApplyDo cardApplyDo = cardApplyDtoConverter.toDo(cardPartnerApplyDto);
        cardApplyDomainService.fillCardApplyByIdmNotify(cardApplyDo, req);
        cardApplyDomainService.updateWithCardTimeById(cardApplyDo);
    }

    @Override
    public PageModel<SafetySpaceParkDto> pageDistinctPark(String keyword, Integer pageNum, Integer pageSize) {
        SafetySpaceParkDo spaceParkDo = new SafetySpaceParkDo();
        spaceParkDo.setParkName(keyword);
        spaceParkDo.putExtField("pageNum", pageNum);
        spaceParkDo.putExtField("pageSize", pageSize);
        PageModel<SafetySpaceParkDo> pageModel = spaceDomainService.pageDistinctPark(spaceParkDo);
        return PageModel.build(spaceConverter.toDtoList(pageModel.getList()), pageModel.getPageSize(),
                pageModel.getPageNum(), pageModel.getTotal());
    }

    @Override
    public List<SafetySpaceParkDto> listByParkCodes(List<String> resourceCodes) {
        return cardApplyDomainService.listSpaceByParkCodes(resourceCodes);
    }

    @Override
    public Integer checkParkCodeExist(String parkCode) {
        return cardApplyDomainService.checkParkCodeExist(parkCode);
    }

    @Override
    public void createTempApply(String accountName) {

        //填充申请单数据
        CardApplyDo cardApplyDo = new CardApplyDo();

        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUserName(accountName);
        safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByAccount(safetyPersonDo);
        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardApplyDo.setPartnerAccount(accountName);

        //保存card person
        CardPersonInfoDo cardPersonInfoDo = cardPersonInfoDoConverter.toCardPersonInfoDo(safetyPersonDo);
        cardPersonInfoDomainService.savePerson(cardPersonInfoDo);

        cardApplyDomainService.fillTempApplyInfo(cardApplyDo);
        cardApplyDomainService.checkBeforeCreate(cardApplyDo);
        cardApplyRepository.save(cardApplyDo);
        //添加白名单
        addUserBehaviorRecord(safetyPersonDo.getUid());
    }

    private void addUserBehaviorRecord(String uid) {
        UserBehaviorRecordDo userBehaviorRecordDo = new UserBehaviorRecordDo();
        userBehaviorRecordDo.setUid(uid);
        userBehaviorRecordDo.setBehaviorCode(SafetyConstants.Common.LARK_WHITE_LIST);
        userBehaviorRecordDo.setBehaviorValue(SafetyConstants.Common.LARK_WHITE_LIST_FLAG);
        userBehaviorRecRepository.addUserBehaviorRecord(userBehaviorRecordDo);
        userBehaviorRecRepository.addUserBehaviorRecord(userBehaviorRecordDo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createEmpApply(String accountName, String employeeNo,
                               String phone, Boolean isAccountActive) {
        //填充申请单数据
        CardApplyDo cardApplyDo = new CardApplyDo();

        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        if (!StringUtils.isEmpty(accountName)) {
            safetyPersonDo.setUserName(accountName);
            safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByAccount(safetyPersonDo);
            cardApplyDo.setPartnerAccount(accountName);
        } else if (!StringUtils.isEmpty(employeeNo)) {
            safetyPersonDo.setEmployeeId(employeeNo);
            safetyPersonDo.setOrgTreeCode(SafetyConfigUserTypeEnum.EMPLOYEE.getOrgTreeCode());
            safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByEmpNoAndOrgCode(safetyPersonDo);
            cardApplyDo.setPartnerAccount(safetyPersonDo.getUserName());
        } else {
            // 手机来查
            safetyPersonDo.setMobile(phone);
            safetyPersonDo.setOrgTreeCode(SafetyConfigUserTypeEnum.EMPLOYEE.getOrgTreeCode());
            safetyPersonDomainService.fillPersonInfoWithBaseAndDeptByPhoneAndOrgCode(safetyPersonDo);
            cardApplyDo.setPartnerAccount(safetyPersonDo.getUserName());
        }

        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardApplyDo.setDisplayName(safetyPersonDo.getDisplayName());
        cardApplyDo.putExtField("isAccountActive", isAccountActive);
        cardApplyDomainService.fillEmpApplyInfo(cardApplyDo);

        cardApplyDomainService.checkBeforeCreate(cardApplyDo);
        Long applyId = cardApplyRepository.save(cardApplyDo);
        cardApplyDo.setId(applyId);

        //保存card person
        CardPersonInfoDo cardPersonInfoDo = cardPersonInfoDoConverter.toCardPersonInfoDo(safetyPersonDo);
        cardPersonInfoDomainService.savePerson(cardPersonInfoDo);

        //国内流程发送消息通知拍照
        if (CardApplyStatusEnum.WAIT_PHOTO.getCode().equals(cardApplyDo.getApplyStatus())) {
            sendPhotographNotice(cardApplyDo);
        }
        if (CardApplyStatusEnum.WAIT_UPLOAD.getCode().equals(cardApplyDo.getApplyStatus())) {
            sendSelfUploadUmsNotify(cardApplyDo);
        }
        //添加白名单
        addUserBehaviorRecord(safetyPersonDo.getUid());
    }

    private void sendPhotographNotice(CardApplyDo cardApplyDo) {
        boolean isActiveScenario = cardApplyDo.getExtField("isAccountActive") != null
                && (Boolean) cardApplyDo.getExtField("isAccountActive");

        ZonedDateTime sendTime = ZonedDateTime.now();
        if (isActiveScenario) {
            sendTime = sendTime.plusHours(OAUCFCommonConstants.LONG_ONE);
        }

        CardInfoDo cardInfoDo = cardApplyDtoConverter.applyDoToCardDo(cardApplyDo);
        cardInfoDo.setCardApply(cardApplyDo);
        //飞书、邮件发送拍照信息
        SafetyUmsNotifyDo safetyUmsNotifyDo = new SafetyUmsNotifyDo();
        safetyUmsNotifyDo.setCardApplyDo(cardApplyDo);
        safetyUmsNotifyDo.setAppCode(AppCodeEnum.CARD.getAppCode());
        safetyUmsNotifyDo.setSendTime(sendTime);
        safetyUmsNotifyDo.setCardInfo(cardInfoDo);
        safetyUmsNotifyDomainService.buildCardNotify(safetyUmsNotifyDo);

        if (CardApplyRemarkEnum.ONSITE.getCode().equals(cardApplyDo.getRemark())) {
            //发短信通知用户待拍照
            safetyUmsNotifyDo.setConfigId(SafetyUmsConfigEnum.TO_BE_TAKE_PHOTO_SMS.getCode());
            safetyUmsNotifyDomainService.checkSafetyUmsNotify(safetyUmsNotifyDo);
            safetyUmsNotifyRepository.createNotify(safetyUmsNotifyDo);
        } else {
            //发邮件通知用户待拍照
            safetyUmsNotifyDo.setConfigId(SafetyUmsConfigEnum.TO_BE_TAKE_PHOTO_EMAIL.getCode());
            safetyUmsNotifyDomainService.checkSafetyUmsNotify(safetyUmsNotifyDo);
            safetyUmsNotifyRepository.createNotify(safetyUmsNotifyDo);

            //发飞书通知用户待拍照
            safetyUmsNotifyDo.setConfigId(SafetyUmsConfigEnum.TO_BE_TAKE_PHOTO_LARk.getCode());
            safetyUmsNotifyDomainService.checkSafetyUmsNotify(safetyUmsNotifyDo);
            safetyUmsNotifyRepository.createNotify(safetyUmsNotifyDo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void selfUpload(Long id) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(id);
        cardApplyDomainService.fillCardApplyWithBaseAndParkById(cardApplyDo);

        //检查是否可改成自行上传
        cardApplyDomainService.checkEnableUpload(cardApplyDo);

        //更新申请单状态为待上传
        cardApplyDo.setApplyStatus(CardApplyStatusEnum.WAIT_UPLOAD.getCode());
        cardApplyRepository.updateWithCardTimeById(cardApplyDo);

        //发送自行上传的UMS消息
        sendSelfUploadUmsNotify(cardApplyDo);

    }

    @Override
    public void reSendSelfUploadUmsNotify(Long id) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(id);
        cardApplyDomainService.fillCardApplyWithBaseAndParkById(cardApplyDo);

        //发送自行上传的UMS消息
        sendSelfUploadUmsNotify(cardApplyDo);
    }

    private void sendSelfUploadUmsNotify(CardApplyDo cardApplyDo) {
        sendSelfUploadUmsNotifyWithDelay(cardApplyDo, 0);
    }

    /**
     * 发送自助上传通知（带延迟）
     * @param cardApplyDo 申请单信息
     * @param delayHours 延迟小时数
     */
    private void sendSelfUploadUmsNotifyWithDelay(CardApplyDo cardApplyDo, int delayHours) {
        //创建邮件通知用户自行上传
        SafetyUmsNotifyDo safetyUmsNotifyDo = new SafetyUmsNotifyDo();
        safetyUmsNotifyDo.setCardApplyDo(cardApplyDo);
        safetyUmsNotifyDo.setAppCode(AppCodeEnum.CARD.getAppCode());
        // 设置发送时间，如果有延迟则加上延迟时间
        if (delayHours > 0) {
            safetyUmsNotifyDo.setSendTime(ZonedDateTime.now().plusHours(delayHours));
        }
        safetyUmsNotifyDomainService.buildCardApplyWaitConfirmUmsNotify(safetyUmsNotifyDo);

        if (CardApplyRemarkEnum.ONSITE.getCode().equals(cardApplyDo.getRemark())) {
            //发短信通知用户自行上传
            safetyUmsNotifyDo.setConfigId(SafetyUmsConfigEnum.PHOTO_TO_SELF_UPLOAD_SMS.getCode());
            safetyUmsNotifyDomainService.checkSafetyUmsNotify(safetyUmsNotifyDo);
            safetyUmsNotifyRepository.createNotify(safetyUmsNotifyDo);
        } else {
            //发邮件通知用户自行上传
            safetyUmsNotifyDo.setConfigId(SafetyUmsConfigEnum.PHOTO_TO_SELF_UPLOAD_EMAIL.getCode());
            safetyUmsNotifyDomainService.checkSafetyUmsNotify(safetyUmsNotifyDo);
            safetyUmsNotifyRepository.createNotify(safetyUmsNotifyDo);

            //发飞书通知用户自行上传
            safetyUmsNotifyDo.setConfigId(SafetyUmsConfigEnum.PHOTO_TO_SELF_UPLOAD_LARK.getCode());
            safetyUmsNotifyDomainService.checkSafetyUmsNotify(safetyUmsNotifyDo);
            safetyUmsNotifyRepository.createNotify(safetyUmsNotifyDo);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void openCardByCardApply(CoopCardApplyEditDto editDto) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.setId(editDto.getApplyId());
        cardApplyDomainService.fillCardApplyDoById(cardApplyDo);
        Asserts.assertNotNull(cardApplyDo, CardApplicationErrorCodeEnum.CARD_APPLY_NOT_FOUND);

        //填充人员信息
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardApplyDo.getUid());
        safetyPersonDomainService.fillPersonInfoWithBase(safetyPersonDo);
        cardApplyDo.setPersonInfo(safetyPersonDo);

        //获取当前申请单对应的卡信息
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoByApplyId(cardApplyDo.getId());
        if (Objects.isNull(cardInfoDo)) {
            //初次开卡
            //开卡前校验人员是否关闭或禁用
            safetyPersonDomainService.checkPersonIsActive(safetyPersonDo);
            //校验申请单有效期是否过期(合作伙伴非驻场不用校验)
            cardApplyDomainService.checkIsExpire(cardApplyDo);

            cardInfoDo = initCardInfoOnOpenCard(cardApplyDo, editDto);
            cardDomainService.checkIsRepeatOpen(cardInfoDo);
            //更新申请单状态为待领取
            cardApplyDo.setApplyStatus(CardApplyStatusEnum.WAIT_RECEIVE.getCode());
            cardApplyDomainService.updateCardApply(cardApplyDo);

            //保存工卡记录
            cardInfoDo.putExtField("isInitStartTime", true);
            cardDomainService.openCard(cardInfoDo);
            //更新生效时间
            cardDomainService.saveValidateTime(cardInfoDo);
        } else {
            cardDomainService.fillSafetyRight(cardInfoDo);
            cardDomainService.fillSafetyPersonMedium(cardInfoDo);
            //如果物理卡号或者加密卡号发生变化，则更新卡信息
            if (!org.apache.commons.lang.StringUtils.equalsIgnoreCase(cardInfoDo.getMediumPhysicsCode(), editDto.getMediumPhysicsCode()) ||
                    !org.apache.commons.lang.StringUtils.equalsIgnoreCase(cardInfoDo.getMediumEncryptCode(), editDto.getMediumEncryptCode())) {
                fillCardInfoEditInfo(cardInfoDo, editDto);
                cardInfoDo.putExtField("isInitStartTime", true);
                cardDomainService.editCard(cardInfoDo);
            }
        }

        //8.异步发送notify 工卡开通完成的消息
        CardInfoDo finalCardInfoDo = cardInfoDo;
        asyncExternalCallThreadPool.submit(() -> {
            cardApplyDo.setApplyStatus(CardApplyStatusEnum.COMPLETED.getCode());
            cardApplyDo.setCardInfo(finalCardInfoDo);
            cardApplyDo.putExtField("operateType", SafetyConstants.Card.NOTIFY_IDM_OPERATE_UPDATE);
            cardApplyDomainService.notifyMessage(cardApplyDo);
        });
    }

    @Override
    public List<CardPartnerApplyDto> findAvatarUrlList(CardAvatarRemoteDto cardRemoteDto) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.putExtField("uidList", cardRemoteDto.getUidList());
        cardApplyDo.putExtField("userNameList", cardRemoteDto.getUserNameList());
        List<CardApplyDo> cardApplyDoList = cardApplyDomainService.findAvatarUrlByUserNameOrUidList(cardApplyDo);
        return cardApplyDtoConverter.toDtoList(cardApplyDoList);
    }

    @Override
    public CardPartnerApplyDto findCoopApplyByUid(String uid) {
        CardApplyDo cardApplyDo = new CardApplyDo();
        cardApplyDo.putExtField("applyTypes", Lists.newArrayList(CardApplyTypeEnum.COOPERATION_CARD_APPLY.getCode(),
                CardApplyTypeEnum.PROPERTY_CARD_APPLY.getCode()));
        cardApplyDo.setUid(uid);
        cardApplyDomainService.fillCardApplyByUidAndTypes(cardApplyDo);
        CardPartnerApplyDto dto = cardApplyDtoConverter.toDto(cardApplyDo);
        dto.setIsMakeCard(checkIsMakeCard(cardApplyDo));
        return dto;
    }

    private Boolean checkIsMakeCard(CardApplyDo cardApplyDo) {
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoByApplyId(cardApplyDo.getId());
        //卡不存在
        if (Objects.isNull(cardInfoDo) || Objects.isNull(cardInfoDo.getCardStatus())) {
            //申请单不存在或者申请单状态是拒绝或取消需要制卡
            return ObjectUtils.isEmpty(cardApplyDo.getId()) || OAUCFCommonConstants.LONG_ZERO.equals(cardApplyDo.getId())
                    || CardApplyStatusEnum.REFUSED.getCode().equals(cardApplyDo.getApplyStatus())
                    || CardApplyStatusEnum.CANCELED.getCode().equals(cardApplyDo.getApplyStatus());
        } else {
            //卡存在且状态是销卡则需制卡
            return CardStatusEnum.CANCELED.getCode().equals(cardInfoDo.getCardStatus());
        }
    }

    @Override
    public void asyncExportCardApply(String queryData) {
        // 异步导出
        // 保存信息到导出数据库
        AsyncExportTaskDo asyncExportTaskDo = new AsyncExportTaskDo();
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        initDO(queryData, asyncExportTaskDo, userInfoDto);

        asyncExportTaskDomainService.save(asyncExportTaskDo);

        // 发布导出事件
        eventPublisher.publishEvent(new SafetyExportEvent(asyncExportTaskDo, asyncExportTaskDo.getId()));

    }

    @Override
    public void asyncExportCoopCardApply(String queryData) {
        // 异步导出
        // 保存信息到导出数据库
        AsyncExportTaskDo asyncExportTaskDo = new AsyncExportTaskDo();
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        initCoopDO(queryData, asyncExportTaskDo, userInfoDto);

        asyncExportTaskDomainService.save(asyncExportTaskDo);

        // 发布导出事件
        eventPublisher.publishEvent(new SafetyExportEvent(asyncExportTaskDo, asyncExportTaskDo.getId()));
    }

    @Override
    public void batchSaveApplyV2(MultipartFile file, String applyType) {
        try {
            file = addImportTypeColumn(file, applyType);
        } catch (IOException e) {
            throw new BizException(InfraErrorCodeEnum.FILE_FORMAT_ERROR);
        }
        // 异步导入
        // 保存信息到导入数据库
        AsyncImportTaskDo asyncImportTaskDo = new AsyncImportTaskDo();
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        initImportDO(file, asyncImportTaskDo, userInfoDto, applyType);

        asyncImportTaskDomainService.save(asyncImportTaskDo);

        // 发布导入事件
        eventPublisher.publishEvent(new SafetyImportEvent(asyncImportTaskDo, String.valueOf(asyncImportTaskDo.getBatchId())));
    }

    private MultipartFile addImportTypeColumn(MultipartFile file, String importType) throws IOException {
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        // Add new column header in the first row
        Row headerRow = sheet.getRow(0);
        int newColumnIndex = headerRow.getLastCellNum();
        Cell newHeaderCell = headerRow.createCell(newColumnIndex);
        newHeaderCell.setCellValue("非必填");

        // Add "ApplyType" header in the second row
        Row headerRow1 = sheet.getRow(1);
        Cell newHeaderCell1 = headerRow1.createCell(newColumnIndex);
        newHeaderCell1.setCellValue("ApplyType");

        // Add importType value to each row
        for (int i = 2; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            Cell newCell = row.createCell(newColumnIndex);
            newCell.setCellValue(importType);
        }

        // Write the modified workbook to a ByteArrayOutputStream
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        workbook.close();

        // Create a new MultipartFile from the ByteArrayOutputStream
        ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
        return new MockMultipartFile(file.getName(), file.getOriginalFilename(), file.getContentType(), bis);
    }

    @Override
    public void asyncExportPropertyCardApply(String queryData) {
        // 异步导出
        // 保存信息到导出数据库
        AsyncExportTaskDo asyncExportTaskDo = new AsyncExportTaskDo();
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        initPropertyDO(queryData, asyncExportTaskDo, userInfoDto);

        asyncExportTaskDomainService.save(asyncExportTaskDo);

        // 发布导出事件
        eventPublisher.publishEvent(new SafetyExportEvent(asyncExportTaskDo, asyncExportTaskDo.getId()));
    }

    @Override
    public void creatCardApplyByUid(CardApplyDo cardApplyDo) {
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardApplyDo.getUid());
        safetyPersonAbility.fillPersonInfoByUid(safetyPersonDo);
        cardApplyDo.setPersonInfo(safetyPersonDo);
        cardApplyDo.setPartnerAccount(safetyPersonDo.getUserName());
        cardApplyDomainService.fillEmpApplyInfo(cardApplyDo);

        cardApplyDo.putExtField("isAccountActive", null);
        cardApplyDomainService.checkBeforeCreate(cardApplyDo);

        // 转换照片以及申请单类型
        cardApplyDo.setPhotoUrl(cardApplyDo.getPhoto());
        cardApplyDo.setApplyStatus(CardApplyStatusFromOldEnum.getByOldCode(cardApplyDo.getStatus()));

        Long applyId = cardApplyRepository.save(cardApplyDo);
        cardApplyDo.setId(applyId);

        //保存card person
        CardPersonInfoDo cardPersonInfoDo = cardPersonInfoDoConverter.toCardPersonInfoDo(safetyPersonDo);
        cardPersonInfoDomainService.savePerson(cardPersonInfoDo);
    }

    @Override
    public void batchImportPhoto(CardApplyImportDto importDto) {
        AsyncImportTaskDo asyncImportRecordDo = initAsyncImportRecord(importDto, ImportTypeEnum.CARD_APPLY_BATCH_IMPORT_PHOTO);
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        asyncImportRecordDo.setOperator(userInfoDto.getUid());
        asyncImportRecordDo.setOperatorName(userInfoDto.getUserName());
        asyncImportTaskDomainService.save(asyncImportRecordDo);
        importEventHandler.handleImportEvent(new SafetyImportEvent(asyncImportRecordDo, asyncImportRecordDo.getBatchId()));
    }

    @Override
    public void batchModifyParks(BatchModifyParkDto modifyParkDto) {
        if (CollectionUtils.isNotEmpty(modifyParkDto.getModifyParks())) {
            List<CardApplyDo> cardApplyDos = Lists.newArrayList();
            modifyParkDto.getModifyParks().forEach(item -> {
                CardApplyDo cardApplyDo = new CardApplyDo();
                cardApplyDo.setId(item.getId());
                cardApplyDo.setParkCode(item.getParkCode());
                cardApplyDos.add(cardApplyDo);
            });
            cardApplyRepository.batchUpdateById(cardApplyDos);
        }
    }

    @Override
    public void asyncExportPhoto(String queryData) {
        AsyncExportTaskDo asyncExportTaskDo = new AsyncExportTaskDo();
        UserInfoDto userInfoDto = idmRemote.getLoginUserInfo();
        initExportPhotoDO(queryData, asyncExportTaskDo, userInfoDto);

        asyncExportTaskDomainService.save(asyncExportTaskDo);

        // 发布导出事件
        eventPublisher.publishEvent(new SafetyExportEvent(asyncExportTaskDo, asyncExportTaskDo.getId()));
    }

    @Override
    public void applyBpmCallBack(BpmCallBackDto bpmCallBackDto) {
        List<CardApplyDo> cardApplyDos = cardApplyRepository.findByBpmCode(bpmCallBackDto.getBusinessKey());
        Asserts.assertNotEmpty(cardApplyDos, CardApplyDomainErrorCodeEnum.CARD_APPLY_NOT_EXIST);
        boolean isDeal = cardApplyDos.stream().allMatch(item -> CardApplyStatusEnum.BPM_APPROVAL.getCode().equals(item.getApplyStatus()));
        if (!isDeal) {
            return;
        }
        if (BpmCallBackStatusEnum.COMPLETED.getStatus().equals(bpmCallBackDto.getProcessInstanceStatus())) {
            cardApplyDos.forEach(item -> item.setApplyStatus(CardApplyStatusEnum.WAIT_PRINT_CARD.getCode()));
            cardApplyRepository.batchUpdateById(cardApplyDos);
        } else if (BpmCallBackStatusEnum.TERMINATED.getStatus().equals(bpmCallBackDto.getProcessInstanceStatus())
                || BpmCallBackStatusEnum.REJECTED.getStatus().equals(bpmCallBackDto.getProcessInstanceStatus())) {
            cardApplyDos.forEach(item -> item.setApplyStatus(CardApplyStatusEnum.REFUSED.getCode()));
            cardApplyRepository.batchUpdateById(cardApplyDos);
        }
    }

    private String buildPartnerNames(List<CardApplyDo> cardApplyDos) {
        return cardApplyDos.stream().map(CardApplyDo::getName).collect(Collectors.joining(","));
    }

    private void initExportPhotoDO(String queryData, AsyncExportTaskDo asyncExportTaskDo, UserInfoDto userInfoDto) {
        asyncExportTaskDo.setOpType(ExportTypeEnum.PHOTO_EXPORT.getType());
        asyncExportTaskDo.setUid(userInfoDto.getUid());
        asyncExportTaskDo.setOperatorName(userInfoDto.getUserName());
        asyncExportTaskDo.setOpTime(ZonedDateTime.now());
        asyncExportTaskDo.setStatus(ExportStatusEnum.TO_EXECUTE.getStatus());
        asyncExportTaskDo.setProgress(SafetyConstants.EXPORT_MAX_PAGE_SIZE_INTEGER);
        asyncExportTaskDo.setParam(queryData);
    }

    private void initImportDO(MultipartFile file, AsyncImportTaskDo asyncImportTaskDo, UserInfoDto userInfoDto, String applyType) {
        if (Integer.valueOf(applyType).equals(CardApplyTypeEnum.PROPERTY_CARD_APPLY.getCode())) {
            asyncImportTaskDo.setOpTypeEnum(ImportTypeEnum.PROPERTY_CARD_IMPORT);
        }
        if (Integer.valueOf(applyType).equals(CardApplyTypeEnum.COOPERATION_CARD_APPLY.getCode())) {
            asyncImportTaskDo.setOpTypeEnum(ImportTypeEnum.COOP_CARD_APPLY_IMPORT);
        }
        if (Objects.nonNull(userInfoDto)) {
            asyncImportTaskDo.setOperator(userInfoDto.getUid());
            asyncImportTaskDo.setOperatorName(userInfoDto.getUserName());
        }
        asyncImportTaskDo.setOpTime(ZonedDateTime.now());
        asyncImportTaskDo.setStatusEnum(ImportStatusEnum.TO_EXECUTE);
        // 导入文件名
        asyncImportTaskDo.setFileName(file.getOriginalFilename());
        //生成batchId
        asyncImportTaskDo.setBatchId(CodeUtils.getUUID());
        // 文件上传到云，获取url
        // file转ByteArrayInputStream
        try {
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";

            if (originalFilename != null && originalFilename.lastIndexOf('.') > 0) {
                // Extract the extension by substring from the last dot.
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf('.'));
            }
            byte[] bytes = file.getBytes();
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
            String url = fdsRemote.uploadExcel(CodeUtils.getUUID() + fileExtension, byteArrayInputStream);
            asyncImportTaskDo.setUrl(url);
        } catch (IOException e) {
            log.error("文件上传失败", e);
            asyncImportTaskDo.setStatusEnum(ImportStatusEnum.IMPORT_FAILED);
            asyncImportTaskDo.setMessage(InfraErrorCodeEnum.INFRA_FDS_UPLOAD_ERROR.getErrDesc());
            throw new BizException(InfraErrorCodeEnum.INFRA_FDS_UPLOAD_ERROR);
        }

    }

    private static void initDO(String queryData, AsyncExportTaskDo asyncExportTaskDo, UserInfoDto userInfoDto) {
        asyncExportTaskDo.setOpType(ExportTypeEnum.EMP_CARD_APPLY_LIST_EXPORT.getType());
        asyncExportTaskDo.setUid(userInfoDto.getUid());
        asyncExportTaskDo.setOperatorName(userInfoDto.getUserName());
        asyncExportTaskDo.setOpTime(ZonedDateTime.now());
        asyncExportTaskDo.setStatus(ExportStatusEnum.TO_EXECUTE.getStatus());
        asyncExportTaskDo.setProgress(SafetyConstants.EXPORT_MAX_PAGE_SIZE_INTEGER);
        asyncExportTaskDo.setParam(queryData);
    }

    private static void initCoopDO(String queryData, AsyncExportTaskDo asyncExportTaskDo, UserInfoDto userInfoDto) {
        asyncExportTaskDo.setOpType(ExportTypeEnum.COOP_CARD_APPLY_LIST_EXPORT.getType());
        asyncExportTaskDo.setUid(userInfoDto.getUid());
        asyncExportTaskDo.setOperatorName(userInfoDto.getUserName());
        asyncExportTaskDo.setOpTime(ZonedDateTime.now());
        asyncExportTaskDo.setStatus(ExportStatusEnum.TO_EXECUTE.getStatus());
        asyncExportTaskDo.setProgress(SafetyConstants.EXPORT_MAX_PAGE_SIZE_INTEGER);
        asyncExportTaskDo.setParam(queryData);
    }

    private static void initPropertyDO(String queryData, AsyncExportTaskDo asyncExportTaskDo, UserInfoDto userInfoDto) {
        asyncExportTaskDo.setOpType(ExportTypeEnum.PROPERTY_CARD_APPLY_LIST_EXPORT.getType());
        asyncExportTaskDo.setUid(userInfoDto.getUid());
        asyncExportTaskDo.setOperatorName(userInfoDto.getUserName());
        asyncExportTaskDo.setOpTime(ZonedDateTime.now());
        asyncExportTaskDo.setStatus(ExportStatusEnum.TO_EXECUTE.getStatus());
        asyncExportTaskDo.setProgress(SafetyConstants.EXPORT_MAX_PAGE_SIZE_INTEGER);
        asyncExportTaskDo.setParam(queryData);
    }

    private void fillCardInfoEditInfo(CardInfoDo cardInfoDo, CoopCardApplyEditDto editDto) {
        cardInfoDo.setMediumCode(CodeUtils.getUUID());
        cardInfoDo.setCardNum(editDto.getCardNum());
        if (prefixEncryptCode.equals(editDto.getMediumEncryptCode().substring(0, 6))) {
            String[] split = editDto.getMediumEncryptCode().split(prefixEncryptCode);
            cardInfoDo.setPrefixEncryptCode(prefixEncryptCode);
            cardInfoDo.setSuffixEncryptCode(split[1]);
        }
        cardInfoDo.setMediumPhysicsCode(editDto.getMediumPhysicsCode());
        cardInfoDo.setMediumEncryptCode(editDto.getMediumEncryptCode());
    }

    private CardInfoDo initCardInfoOnOpenCard(CardApplyDo cardApplyDo, CoopCardApplyEditDto editDto) {
        CardInfoDo cardInfoDo = new CardInfoDo();
        cardInfoDo.setCardApplyId(cardApplyDo.getId());
        cardInfoDo.setCardType(cardApplyDo.getApplyType());
        cardInfoDo.setCardStatus(CardStatusEnum.NOT_ACTIVE.getCode());
        cardInfoDo.setUid(cardApplyDo.getUid());
        cardInfoDo.setMediumCode(CodeUtils.getUUID());
        cardInfoDo.setCardNum(editDto.getCardNum());
        cardInfoDo.setMediumEncryptCode(editDto.getMediumEncryptCode());
        cardInfoDo.setMediumPhysicsCode(editDto.getMediumPhysicsCode());
        if (prefixEncryptCode.equals(editDto.getMediumEncryptCode().substring(0, 6))) {
            String[] split = editDto.getMediumEncryptCode().split(prefixEncryptCode);
            cardInfoDo.setPrefixEncryptCode(prefixEncryptCode);
            cardInfoDo.setSuffixEncryptCode(split[1]);
        }
        //驻场来的 开卡时间填充申请单时间 其他统统填永久
        if (ObjectUtils.isEmpty(cardApplyDo.getStartTime()) ||
                OAUCFCommonConstants.LONG_ZERO.equals(cardApplyDo.getStartTime().toEpochSecond()) ||
                OAUCFCommonConstants.LONG_ZERO.equals(cardApplyDo.getEndTime().toEpochSecond())) {
            cardInfoDo.setStartTime(cardApplyDo.getPersonInfo().getStartTime());
            cardInfoDo.setEndTime(SafetyConstants.Card.DEFAULT_END_TIME);
        } else {
            cardInfoDo.setStartTime(cardApplyDo.getStartTime());
            cardInfoDo.setEndTime(cardApplyDo.getEndTime());
        }
        return cardInfoDo;
    }

    private void parsingExcel(MultipartFile queryData, List<CardApplyDo> cardApplyDos) {
        try {
            int initialRow = 2;
            ExcelReader reader = ExcelUtil.getReader(queryData.getInputStream());
            List<Map<String, Object>> readAll;
            Map<String, String> map = new LinkedHashMap<>();
            buildExcelMap(map);
            reader.setHeaderAlias(map);
            readAll = reader.read(1, 2, reader.getRowCount());
            if (CollectionUtils.isNotEmpty(readAll)) {
                for (Map<String, Object> stringObjectMap : readAll) {
                    initialRow++;
                    CardApplyDo cardApplyDo = new CardApplyDo();
                    //判断是否有空行
                    Set<Object> set = stringObjectMap.values().stream().filter(item -> item != null).collect(Collectors.toSet());
                    if (set.size() == 0) {
                        //跳过空行
                        continue;
                    }
                    for (Map.Entry<String, Object> entry : stringObjectMap.entrySet()) {
                        if (!ExcelAttributeMapperEnum.PARTNER_ACCOUNT.getTarget().equals(entry.getKey()) &&
                                !ExcelAttributeMapperEnum.EMAIL.getTarget().equals(entry.getKey()) &&
                                !ExcelAttributeMapperEnum.REMARK.getTarget().equals(entry.getKey()) &&
                                !ExcelAttributeMapperEnum.RESPONSIBLE_NAME.getTarget().equals(entry.getKey()) &&
                                ObjectUtils.isEmpty(entry.getValue())) {
                            if (!ExcelAttributeMapperEnum.NULL_STRING.getTarget().equals(entry.getKey())) {
                                throw new BizException(CardApplyDomainErrorCodeEnum.IMPORT_PARAM_ERROR,
                                        String.valueOf(initialRow), ExcelAttributeMapperEnum.getSource(entry.getKey()));
                            } else {
                                stringObjectMap.remove(entry.getKey(), entry.getValue());
                            }
                        }
                        if (ExcelAttributeMapperEnum.START_TIME.getTarget().equals(entry.getKey())) {
                            DateTime startTime = (DateTime) entry.getValue();
                            ZonedDateTime start = ZonedDateTimeUtils.toZonedDateTime(startTime.toString());
                            if (!ObjectUtils.isEmpty(start)) {
                                stringObjectMap.put(entry.getKey(), ZonedDateTimeUtils.getZonedDateTimeBegin(start));
                            }
                        }
                        if (ExcelAttributeMapperEnum.END_TIME.getTarget().equals(entry.getKey())) {
                            DateTime endTime = (DateTime) entry.getValue();
                            ZonedDateTime end = ZonedDateTimeUtils.toZonedDateTime(endTime.toString());
                            stringObjectMap.put(entry.getKey(), ZonedDateTimeUtils.getZonedDateTimeEnd(end));
                        }
                    }
                    BeanUtils.populate(cardApplyDo, stringObjectMap);
                    cardApplyDo.setExcelRows(initialRow);
                    cardApplyDos.add(cardApplyDo);
                }
            }
        } catch (IOException e) {
            throw new BizException(CardApplyDomainErrorCodeEnum.EXCEL_EXPORT_ERROR);
        } catch (IllegalAccessException e) {
            throw new BizException(CardApplyDomainErrorCodeEnum.ILLEGAL_ACCESS_ERROR);
        } catch (InvocationTargetException e) {
            throw new BizException(CardApplyDomainErrorCodeEnum.INVOCATION_TARGET);
        }
    }

    private void buildExcelMap(Map<String, String> map) {
        map.put(ExcelAttributeMapperEnum.ZONE_CODE.getSource(), ExcelAttributeMapperEnum.ZONE_CODE.getTarget());
        map.put(ExcelAttributeMapperEnum.PHONE.getSource(), ExcelAttributeMapperEnum.PHONE.getTarget());
        map.put(ExcelAttributeMapperEnum.COMPANY.getSource(), ExcelAttributeMapperEnum.COMPANY.getTarget());
        map.put(ExcelAttributeMapperEnum.NATION.getSource(), ExcelAttributeMapperEnum.NATION.getTarget());
        map.put(ExcelAttributeMapperEnum.DISPLAY_NAME.getSource(), ExcelAttributeMapperEnum.DISPLAY_NAME.getTarget());
        map.put(ExcelAttributeMapperEnum.SURNAME.getSource(), ExcelAttributeMapperEnum.SURNAME.getTarget());
        map.put(ExcelAttributeMapperEnum.PINYIN_NAME.getSource(), ExcelAttributeMapperEnum.PINYIN_NAME.getTarget());
        map.put(ExcelAttributeMapperEnum.ID_NUMBER_TYPE.getSource(), ExcelAttributeMapperEnum.ID_NUMBER_TYPE.getTarget());
        map.put(ExcelAttributeMapperEnum.ID_NUMBER.getSource(), ExcelAttributeMapperEnum.ID_NUMBER.getTarget());
        map.put(ExcelAttributeMapperEnum.PARTNER_ACCOUNT.getSource(), ExcelAttributeMapperEnum.PARTNER_ACCOUNT.getTarget());
        map.put(ExcelAttributeMapperEnum.EMAIL.getSource(), ExcelAttributeMapperEnum.EMAIL.getTarget());
        map.put(ExcelAttributeMapperEnum.START_TIME.getSource(), ExcelAttributeMapperEnum.START_TIME.getTarget());
        map.put(ExcelAttributeMapperEnum.END_TIME.getSource(), ExcelAttributeMapperEnum.END_TIME.getTarget());
        map.put(ExcelAttributeMapperEnum.RESPONSIBLE_NAME.getSource(), ExcelAttributeMapperEnum.RESPONSIBLE_NAME.getTarget());
        map.put(ExcelAttributeMapperEnum.RESPONSIBLE_ACCOUNT.getSource(), ExcelAttributeMapperEnum.RESPONSIBLE_ACCOUNT.getTarget());
        map.put(ExcelAttributeMapperEnum.REMARK.getSource(), ExcelAttributeMapperEnum.REMARK.getTarget());
        map.put(ExcelAttributeMapperEnum.PARK_NAME.getSource(), ExcelAttributeMapperEnum.PARK_NAME.getTarget());
    }

    /**
     * 根据场景发送通知消息的统一入口
     * @param cardApplyDo 申请单信息
     * @param notificationType 通知类型
     */
    private void sendNotificationWithScenario(CardApplyDo cardApplyDo, NotificationType notificationType) {
        // 根据是否为账号激活场景决定延迟时间
        int delayHours = Boolean.TRUE.equals(cardApplyDo.getIsAccountActive()) ? 1 : 0;

        switch (notificationType) {
            case PHOTOGRAPH:
                sendPhotographNoticeWithDelay(cardApplyDo, delayHours);
                break;
            case SELF_UPLOAD:
                sendSelfUploadUmsNotifyWithDelay(cardApplyDo, delayHours);
                break;
            default:
                log.warn("Unknown notification type: {}", notificationType);
        }
    }
}
