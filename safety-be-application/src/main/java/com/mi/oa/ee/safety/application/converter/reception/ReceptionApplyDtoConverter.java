package com.mi.oa.ee.safety.application.converter.reception;

import com.mi.oa.ee.safety.application.dto.reception.ReceptionApplyDto;
import com.mi.oa.ee.safety.application.dto.reception.ReceptionApplyEvaluateDto;
import com.mi.oa.ee.safety.application.dto.reception.ReceptionApplyHandlerDto;
import com.mi.oa.ee.safety.application.dto.reception.ReceptionApplyServiceDto;
import com.mi.oa.ee.safety.application.dto.safety.SafetyPersonDto;
import com.mi.oa.ee.safety.application.dto.visitor.ApplyDto;
import com.mi.oa.ee.safety.application.dto.visitor.ApplyUserInfoDto;
import com.mi.oa.ee.safety.application.dto.visitor.UserInfoDto;
import com.mi.oa.ee.safety.common.dto.DeptDto;
import com.mi.oa.ee.safety.domain.model.*;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2023/5/11 16:16
 */
@Mapper(componentModel = "spring")
public interface ReceptionApplyDtoConverter {

    ReceptionApplyDto toDto(VisitorReceptionApplyDo visitorReceptionApplyDo);

    @Mapping(source = "receptionConfig", target = "receptionConfig")
    VisitorReceptionApplyDo toReceptionDo(ReceptionApplyDto receptionApplyDto);

    void toReceptionDto(VisitorReceptionApplyDo receptionApplyDo, @MappingTarget ReceptionApplyDto receptionApplyDto);

    VisitorApplyDO toVisitorDo(ReceptionApplyDto receptionApplyDto);

    VisitorReceptionApplyEvaluateDo toEvaluateDo(ReceptionApplyEvaluateDto evaluateDto);

    VisitorReceptionApplyServiceDo toApplyServiceDo(ReceptionApplyDto receptionApplyDto);

    List<VisitorReceptionApplyServiceDo> toApplyServiceListDo(List<ReceptionApplyServiceDto> receptionApplyServiceDtos);

    List<ReceptionApplyHandlerDto> toApplyHandlerListDto(List<VisitorReceptionApplyHandlerDo> applyHandlerDos);

    List<ReceptionApplyDto> visitorToApplyDtoList(List<VisitorApplyDO> visitorApplyDOS);

    @Mapping(source = "id", target = "applyId")
    @Mapping(source = "visitorReasonDO.name", target = "visitReasonName")
    ReceptionApplyDto visitorToApplyDto(VisitorApplyDO visitorApplyDO);

    ApplyDto toDto(VisitorApplyDO visitorApplyDO);

    default List<ReceptionApplyDto> visitorAndReceptionToDtoList(List<VisitorApplyDO> visitorApplyDOS,
                                                                 List<VisitorReceptionApplyDo> receptionApplyDos) {
        Map<Long, VisitorReceptionApplyDo> receptionApplyDoMap = receptionApplyDos.stream().collect(
                Collectors.toMap(VisitorReceptionApplyDo::getApplyId, item -> item, (key1, key2) -> key1));
        List<ReceptionApplyDto> receptionApplyDtos = this.visitorToApplyDtoList(visitorApplyDOS);
        for (ReceptionApplyDto receptionApplyDto : receptionApplyDtos) {
            VisitorReceptionApplyDo currentReceptionDo = receptionApplyDoMap.get(receptionApplyDto.getApplyId());
            this.toReceptionDto(currentReceptionDo, receptionApplyDto);
        }
        return receptionApplyDtos;
    }

    default ReceptionApplyDto toDetailDto(VisitorApplyDO visitorApplyDO,
                                          VisitorReceptionApplyDo visitorReceptionApplyDo) {
        ReceptionApplyDto receptionApplyDto = this.visitorToApplyDto(visitorApplyDO);
        this.toReceptionDto(visitorReceptionApplyDo, receptionApplyDto);
        SafetyPersonDo applyUserInfo = visitorApplyDO.getApplyUserInfo();
        receptionApplyDto.setApplicantName(applyUserInfo.getDisplayName());
        receptionApplyDto.setReceiver(applyUserInfo.getUid());
        receptionApplyDto.setApplyUserInfo(this.personDoToDto(applyUserInfo));
        receptionApplyDto.setParkId(visitorApplyDO.getVisitorPark().getId());
        String parkAdmins = visitorApplyDO.getVisitorPark().getParkAdmins().stream()
                .map(SafetyPersonDo::getUid).collect(Collectors.toList()).stream()
                .collect(Collectors.joining(","));
        receptionApplyDto.setParkAdmins(parkAdmins);
        VisitorParkDo visitorPark = visitorApplyDO.getVisitorPark();
        visitorPark.setParkCode(visitorApplyDO.getParkCode());
        visitorPark.setParkName(visitorApplyDO.getParkName());
        visitorPark.setBuildingCode(visitorApplyDO.getBuildingCode());
        visitorPark.setBuildingName(visitorApplyDO.getBuildingName());
        visitorPark.setFloorCode(visitorApplyDO.getFloorCode());
        visitorPark.setFloorName(visitorApplyDO.getFloorName());
        visitorApplyDO.setVisitorPark(visitorPark);
        receptionApplyDto.setVisitorApply(this.toDto(visitorApplyDO));
        return receptionApplyDto;
    }

    SafetyPersonDto personDoToDto(SafetyPersonDo safetyPersonDo);

    default ApplyUserInfoDto safetyPersonDoToApplyUserInfoDto(SafetyPersonDo safetyPersonDo) {
        if (safetyPersonDo == null) {
            return null;
        }
        ApplyUserInfoDto.ApplyUserInfoDtoBuilder applyUserInfoDto = ApplyUserInfoDto.builder();
        applyUserInfoDto.userId(safetyPersonDo.getUserName());
        applyUserInfoDto.uid(safetyPersonDo.getUid());
        applyUserInfoDto.userName(safetyPersonDo.getDisplayName());
        applyUserInfoDto.email(safetyPersonDo.getEmail());
        applyUserInfoDto.avatarUrl(safetyPersonDo.getAvatarUrl());
        applyUserInfoDto.deptInfos(deptDoToDtoList(safetyPersonDo.getDeptInfoDoList()));
        applyUserInfoDto.firstDepart(safetyPersonDo.getFirstDeptName());
        return applyUserInfoDto.build();
    }

    default UserInfoDto safetyPersonDoToUserInfoDto(SafetyPersonDo safetyPersonDo) {
        if (safetyPersonDo == null) {
            return null;
        }
        UserInfoDto.UserInfoDtoBuilder userInfoDto = UserInfoDto.builder();
        userInfoDto.uid(safetyPersonDo.getUid());
        userInfoDto.userNameId(safetyPersonDo.getUserName());
        userInfoDto.userName(safetyPersonDo.getDisplayName());
        userInfoDto.avatarUrl(safetyPersonDo.getAvatarUrl());
        userInfoDto.email(safetyPersonDo.getEmail());
        userInfoDto.phone(safetyPersonDo.getMobile());
        userInfoDto.deptInfo(deptDoToDtoList(safetyPersonDo.getDeptInfoDoList()));
        return userInfoDto.build();
    }

    default List<DeptDto> deptDoToDtoList(List<DeptInfoDo> deptInfoDoList) {
        List<DeptDto> res = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deptInfoDoList)) {
            for (DeptInfoDo deptInfoDo : deptInfoDoList) {
                DeptDto deptDto = new DeptDto();
                deptDto.setDeptId(deptInfoDo.getDeptId());
                deptDto.setLevel(deptInfoDo.getLevel());
                deptDto.setDeptName(deptInfoDo.getDeptName());
                deptDto.setDeptEnName(deptInfoDo.getDeptEnName());
                res.add(deptDto);
            }
        }
        return res;
    }
}
