package com.mi.oa.ee.safety.application.impl.card.permission;

import static com.mi.oa.ee.safety.common.enums.card.CardTypeEnum.EMPLOYEE_CARD;
import static com.mi.oa.ee.safety.common.enums.card.CardTypeEnum.TEMP_CARD;

import java.net.URLEncoder;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.application.converter.card.CardGroupConfigDtoConverter;
import com.mi.oa.ee.safety.application.converter.card.CardPermissionApplyDtoConverter;
import com.mi.oa.ee.safety.application.dto.card.CardInfoDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigQueryDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardPermissionApplyDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionAddDto;
import com.mi.oa.ee.safety.application.dto.card.shared.PermissionAddDto.PermissionDetailDto;
import com.mi.oa.ee.safety.application.dto.visitor.BpmCallBackDto;
import com.mi.oa.ee.safety.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.application.errorcode.CardApplicationErrorCodeEnum;
import com.mi.oa.ee.safety.domain.ability.CardApplyAbility;
import com.mi.oa.ee.safety.application.service.card.coop.CardService;
import com.mi.oa.ee.safety.application.service.card.perssion.CardPermissionApplyService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.constants.SafetyConstants.Card;
import com.mi.oa.ee.safety.common.constants.SafetyConstants.Common;
import com.mi.oa.ee.safety.common.dto.BpmLinkDto;
import com.mi.oa.ee.safety.common.dto.DeptDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceParkDto;
import com.mi.oa.ee.safety.common.enums.AppCodeEnum;
import com.mi.oa.ee.safety.common.enums.BpmCallBackStatusEnum;
import com.mi.oa.ee.safety.common.enums.RocketMqTopicEnum;
import com.mi.oa.ee.safety.common.enums.card.CardClassEnum;
import com.mi.oa.ee.safety.common.enums.card.CardPermissionApplyGroupStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardPermissionApplyStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyUmsConfigEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyUmsSendStatusEnum;
import com.mi.oa.ee.safety.common.utils.Asserts;
import com.mi.oa.ee.safety.common.utils.GsonUtils;
import com.mi.oa.ee.safety.common.utils.SeqUtil;
import com.mi.oa.ee.safety.domain.errorcode.CardPermissionApplyDomainErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.CardGroupConfigAdminDo;
import com.mi.oa.ee.safety.domain.model.CardGroupConfigDo;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.CardPermissionApplyDo;
import com.mi.oa.ee.safety.domain.model.CardPermissionApplyGroupDo;
import com.mi.oa.ee.safety.domain.model.DeptInfoDo;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDo;
import com.mi.oa.ee.safety.domain.model.SafetyClassDo;
import com.mi.oa.ee.safety.domain.model.SafetyPersonDo;
import com.mi.oa.ee.safety.domain.model.SafetyUmsNotifyDo;
import com.mi.oa.ee.safety.domain.query.card.CardGroupConfigQuery;
import com.mi.oa.ee.safety.domain.query.card.CardPermissionApplyQuery;
import com.mi.oa.ee.safety.domain.service.CardDomainService;
import com.mi.oa.ee.safety.domain.service.CardGroupConfigDomainService;
import com.mi.oa.ee.safety.domain.service.CardPermissionApplyDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyCarrierGroupDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyPersonDomainService;
import com.mi.oa.ee.safety.infra.remote.enums.BpmCodeEnum;
import com.mi.oa.ee.safety.infra.remote.enums.BpmPermissionApplyDetailFormEnum;
import com.mi.oa.ee.safety.infra.remote.enums.BpmPermissionApplyFormEnum;
import com.mi.oa.ee.safety.infra.remote.enums.BpmPermissionGrantDetailFormEnum;
import com.mi.oa.ee.safety.infra.remote.enums.BpmPermissionGrantFormEnum;
import com.mi.oa.ee.safety.infra.remote.enums.BpmPermissionProcessEnum;
import com.mi.oa.ee.safety.infra.remote.model.BpmCancelDto;
import com.mi.oa.ee.safety.infra.remote.model.BpmCreateDto;
import com.mi.oa.ee.safety.infra.remote.mq.CommonProducer;
import com.mi.oa.ee.safety.infra.remote.sdk.BpmSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.IdmSdk;
import com.mi.oa.ee.safety.infra.remote.sdk.SpaceSdk;
import com.mi.oa.ee.safety.infra.repository.CardGroupConfigRepository;
import com.mi.oa.ee.safety.infra.repository.CardPermissionApplyRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyClassRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyUmsNotifyRepository;
import com.mi.oa.ee.safety.infra.repository.query.SafetyClassQuery;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/4/2 21:51
 */
@Slf4j
@Service
public class CardPermissionApplyServiceImpl implements CardPermissionApplyService {

    private static final String VALID_PERIOD = "%s 至 %s";

    @Resource
    private SafetyClassRepository safetyClassRepository;

    @Resource
    private CardDomainService cardDomainService;

    @Resource
    private CardPermissionApplyDtoConverter cardPermissionApplyDtoConverter;

    @Resource
    private CardPermissionApplyDomainService cardPermissionApplyDomainService;

    @Resource
    private CardGroupConfigDomainService cardGroupConfigDomainService;

    @Resource
    private SafetyCarrierGroupDomainService safetyCarrierGroupDomainService;

    @Resource
    private CardPermissionApplyRepository cardPermissionApplyRepository;

    @Resource
    private CardGroupConfigRepository cardGroupConfigRepository;

    @Resource
    private CardGroupConfigDtoConverter cardGroupConfigDtoConverter;

    @Resource
    private CommonProducer commonProducer;

    @Resource
    private BpmSdk bpmSdk;

    @Resource
    private IdmSdk idmSdk;

    @Resource
    private SafetyUmsNotifyRepository safetyUmsNotifyRepository;

    @Value("${card.lark-app-id}")
    public String cardLarkAppId;

    @Value("${card.host}")
    public String cardWorkBench;

    @Resource
    private SafetyPersonDomainService safetyPersonDomainService;

    @Autowired
    private SpaceSdk spaceSdk;

    @Autowired
    private CardService cardService;

    @Override
    public List<CardPermissionApplyDto> getControlTypeList(CardPermissionApplyDto dto) {
        SafetyClassQuery safetyClassQuery = cardPermissionApplyDtoConverter.toClassQuery(dto);
        PageModel<SafetyClassDo> pageModel = safetyClassRepository.queryPage(safetyClassQuery);
        return cardPermissionApplyDtoConverter.classToDtoList(pageModel.getList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createApply(CardPermissionApplyDto cardPermissionApplyDto) {
        CardPermissionApplyDo cardPermissionApplyDo = cardPermissionApplyDtoConverter.toDo(cardPermissionApplyDto);
        cardPermissionApplyDo.putExtField("isForever", cardPermissionApplyDto.getIsForever());

        //创建前填充
        cardPermissionApplyDomainService.fillPermissionApplyBeforeCreate(cardPermissionApplyDo);
        Set<String> groupCodeList = cardPermissionApplyDo.getPermissionApplyGroupList().stream()
                .map(CardPermissionApplyGroupDo::getCardGroupCode).collect(Collectors.toSet());
        List<CardGroupConfigDo> groupList = cardGroupConfigRepository.findListByCode(groupCodeList);
        cardGroupConfigDomainService.fillCardGroupConfigList(groupList);
        cardPermissionApplyDo.setGroupConfigList(groupList);
        //创建前检查
        cardPermissionApplyDomainService.checkPermissionApplyBeforeCreate(cardPermissionApplyDo);

        //保存
        cardPermissionApplyRepository.create(cardPermissionApplyDo);

        //发送第一段bpm请求
        buildBpmRequest(cardPermissionApplyDo, cardPermissionApplyDto.getIsForever());

        return cardPermissionApplyDo.getId();
    }

    private void buildBpmRequest(CardPermissionApplyDo cardPermissionApplyDo, Boolean isForever) {
        BpmCreateDto bpmCreateDto = new BpmCreateDto();
        bpmCreateDto.setBpmCodeEnum(BpmCodeEnum.SPECIFIC_PERMISSION_APPLY);
        bpmCreateDto.setBusinessKey(cardPermissionApplyDo.getBpmCode());
        bpmCreateDto.setStartUserId(cardPermissionApplyDo.getPersonInfo().getUserName());
        String title = BpmCodeEnum.SPECIFIC_PERMISSION_APPLY.getDesc();
        if (Objects.nonNull(cardPermissionApplyDo.getSafetyClass())) {
            title += "-" + cardPermissionApplyDo.getSafetyClass().getName();
        }

        bpmCreateDto.setProcessName(title);
        Map<String, Object> formData = Maps.newHashMap();
        formData.put(BpmPermissionApplyFormEnum.APPLY_USER.getFieldName(),
                cardPermissionApplyDo.getPersonInfo().getUserName());
        formData.put(BpmPermissionApplyFormEnum.CONTROL_TYPE.getFieldName(),
                Objects.nonNull(cardPermissionApplyDo.getSafetyClass()) ? cardPermissionApplyDo.getSafetyClass().getName() : Common.CONCAT);
        if (isForever) {
            formData.put(BpmPermissionApplyFormEnum.FOREVER_VALID_PERIOD.getFieldName(),
                    BpmPermissionApplyFormEnum.FOREVER_VALID_PERIOD.getFieldDesc());
        } else {
            formData.put(BpmPermissionApplyFormEnum.VALID_PERIOD.getFieldName(),
                    String.format(VALID_PERIOD, ZonedDateTimeUtils.formatChinaYMD(cardPermissionApplyDo.getStartTime()),
                            ZonedDateTimeUtils.formatChinaYMD(cardPermissionApplyDo.getEndTime())));
        }
        formData.put(BpmPermissionApplyFormEnum.APPLY_REASON.getFieldName(),
                cardPermissionApplyDo.getReasonName());
        if (CollectionUtils.isNotEmpty(cardPermissionApplyDo.getPersonInfo().getDeptInfoDoList())) {
            for (DeptInfoDo deptInfoDo : cardPermissionApplyDo.getPersonInfo().getDeptInfoDoList()) {
                formData.put(BpmPermissionApplyFormEnum.APPLY_USER_DEPT.getFieldName(),
                        deptInfoDo.getDeptId());
            }
        }
        List<Map<String, Object>> detailList = Lists.newArrayListWithCapacity(cardPermissionApplyDo.getGroupConfigList().size());
        for (CardGroupConfigDo config : cardPermissionApplyDo.getGroupConfigList()) {
            Map<String, Object> detailMap = Maps.newHashMap();
            detailMap.put(BpmPermissionApplyDetailFormEnum.CARD_GROUP_CONFIG_NAME.getFieldName(), config.getCardGroupName());
            detailMap.put(BpmPermissionApplyDetailFormEnum.PARK_NAME.getFieldName(), config.getParkName());
            detailMap.put(BpmPermissionApplyDetailFormEnum.DEPARTMEN.getFieldName(),
                    Objects.nonNull(config.getDeptInfo()) ? config.getDeptInfo().getDeptName() : Common.CONCAT);
            String admin = config.getAdminList().stream().map(CardGroupConfigAdminDo::getAdminUserName)
                    .collect(Collectors.joining(Common.COMMA));
            detailMap.put(BpmPermissionApplyDetailFormEnum.ADMIN.getFieldName(), admin);
            detailList.add(detailMap);
        }
        formData.put(BpmPermissionApplyFormEnum.GROUP_FORM.getFieldName(), detailList);
        bpmCreateDto.setFormData(formData);
        Map<String, Object> variables = Maps.newHashMap();
        variables.put(BpmPermissionApplyFormEnum.CONTROL_TYPE.getFieldName(),
                Objects.nonNull(cardPermissionApplyDo.getSafetyClass()) ? cardPermissionApplyDo.getSafetyClass().getName() : Common.CONCAT);
        List<CardGroupConfigDo> groupConfigList = cardPermissionApplyDo.getGroupConfigList();
        if (Objects.isNull(groupConfigList)) {
            groupConfigList = Lists.newArrayList();
        }
        fillBpmCardGroup(groupConfigList, groupConfigList.size(), variables, cardPermissionApplyDo.getPersonInfo());
        bpmCreateDto.setVariable(variables);
        log.info("first create bpm req:{}", bpmCreateDto);
        bpmSdk.createBpm(bpmCreateDto);
    }

    private void fillBpmCardGroup(List<CardGroupConfigDo> cardGroupConfigDoList, Integer num,
                                  Map<String, Object> variables, SafetyPersonDo applyPerson) {
        if (CollectionUtils.isNotEmpty(cardGroupConfigDoList)) {
            variables.put(BpmPermissionApplyFormEnum.CARD_GROUP_NUM.getFieldName(), num);
            Boolean isCrossDept = checkIsCrossDept(cardGroupConfigDoList, applyPerson);
            variables.put(BpmPermissionApplyFormEnum.IS_CROSS_DEPT.getFieldName(), isCrossDept);
            String parentDeptIdList = buildGroupParentDeptId(cardGroupConfigDoList);
            variables.put(BpmPermissionApplyFormEnum.CARD_GROUP_PARENT_DEPT.getFieldName(), parentDeptIdList);
        } else {
            variables.put(BpmPermissionApplyFormEnum.CARD_GROUP_NUM.getFieldName(),
                    OAUCFCommonConstants.INT_ZERO);
            variables.put(BpmPermissionApplyFormEnum.IS_CROSS_DEPT.getFieldName(), Boolean.FALSE);
        }
    }

    private String buildGroupParentDeptId(List<CardGroupConfigDo> cardGroupConfigDoList) {
        StringBuilder stringBuilder = new StringBuilder();
        for (CardGroupConfigDo item : cardGroupConfigDoList) {
            if (Objects.nonNull(item.getDeptInfo())) {
                DeptDto dept = new DeptDto();
                DeptDto withOwner;
                dept.setDeptId(item.getDeptInfo().getDeptId());
                String initDeptId = item.getDeptInfo().getDeptId();
                do {
                    idmSdk.loadDeptInfoByDeptIdFromLocalCache(dept);
                    if (dept.getLevel() == null) {
                        log.error("部门ID信息变更，查询数据为空，部门ID：{}", dept.getDeptId());
                        // 通知群里消息
                        throw new BizException(CardApplicationErrorCodeEnum.APPLY_ADDRESS_CHANGE);
                    }
                    withOwner = idmSdk.getDeptDetail(initDeptId);
                    if (Integer.parseInt(dept.getLevel()) > SafetyConstants.Card.FOURTH_DEPART_LEVEL
                            || StringUtils.isEmpty(withOwner.getDeptOwner())) {
                        dept.setDeptId(dept.getParentDeptId());
                        initDeptId = dept.getParentDeptId();
                    }
                } while ((Integer.parseInt(dept.getLevel()) > SafetyConstants.Card.FOURTH_DEPART_LEVEL
                        || StringUtils.isEmpty(withOwner.getDeptOwner()))
                        && Integer.parseInt(dept.getLevel()) > OAUCFCommonConstants.INT_ZERO);
                if (StringUtils.isNotEmpty(dept.getDeptId())
                        && !SafetyConstants.Card.MI_DEPART_ID.equals(dept.getDeptId())) {
                    stringBuilder.append(dept.getDeptId());
                    stringBuilder.append(",");
                }
            }
        }
        if (stringBuilder.length() > OAUCFCommonConstants.INT_ZERO) {
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        }
        return stringBuilder.toString();
    }

    private Boolean checkIsCrossDept(List<CardGroupConfigDo> groupConfigList,
                                     SafetyPersonDo applyPerson) {
        Set<String> deptIdList = groupConfigList.stream()
                .map(item -> item.getDeptInfo().getDeptId()).collect(Collectors.toSet());
        if (!OAUCFCommonConstants.INT_ONE.equals(deptIdList.size())) {
            return true;
        }
        return Objects.nonNull(applyPerson) && !deptIdList.contains(applyPerson.getDeptId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(Long permissionApplyId) {
        //取消前填充
        CardPermissionApplyDo cardPermissionApplyDo = new CardPermissionApplyDo();
        cardPermissionApplyDo.setId(permissionApplyId);
        cardPermissionApplyDomainService.fillPermissionApplyBeforeCancel(cardPermissionApplyDo);

        //取消前检查
        cardPermissionApplyDomainService.checkPermissionApplyBeforeCancel(cardPermissionApplyDo);

        //取消
        cardPermissionApplyDomainService.cancel(cardPermissionApplyDo);

        //更新申请单及关系状态
        cardPermissionApplyRepository.updateByCancel(cardPermissionApplyDo);
        //取消bpm流程
        buildBpmCancelRequest(cardPermissionApplyDo);
    }

    private void buildBpmCancelRequest(CardPermissionApplyDo cardPermissionApplyDo) {
        BpmCancelDto bpmCancelDto = new BpmCancelDto();
        bpmCancelDto.setBusinessKey(cardPermissionApplyDo.getBpmCode());
        bpmCancelDto.setUserId(cardPermissionApplyDo.getPersonInfo().getUserName());
        bpmSdk.cancelBpm(bpmCancelDto);
    }

    @Override
    public CardPermissionApplyDto findDetail(Long permissionApplyId) {
        CardPermissionApplyDo cardPermissionApplyDo = new CardPermissionApplyDo();
        cardPermissionApplyDo.setId(permissionApplyId);
        cardPermissionApplyDomainService.fillPermissionApplyBeforeDetail(cardPermissionApplyDo);
        cardPermissionApplyDomainService.checkPermissionApplyBeforeDetail(cardPermissionApplyDo);
        return cardPermissionApplyDtoConverter.toDto(cardPermissionApplyDo);
    }

    @Override
    public PageModel<CardGroupConfigDto> pageGroup(CardGroupConfigQueryDto configDto) {
        CardGroupConfigQuery cardGroupConfigQuery = cardGroupConfigDtoConverter.toQuery(configDto);
        PageVO<CardGroupConfigDo> page = cardGroupConfigRepository.pageForApplet(cardGroupConfigQuery);
        if (CollectionUtils.isNotEmpty(page.getList())) {
            page.getList().forEach(cardGroupConfigDo -> cardPermissionApplyDomainService.fillGroupSpace(cardGroupConfigDo));
        }
        return PageModel.build(cardGroupConfigDtoConverter.toDtoList(page.getList()), page.getPageSize(), page.getPageNum(),
                page.getTotal());
    }

    @Override
    public PageModel<CardPermissionApplyDto> pageApply(CardPermissionApplyDto cardPermissionApplyDto) {
        CardPermissionApplyQuery cardPermissionApplyQuery =
                cardPermissionApplyDtoConverter.toApplyQuery(cardPermissionApplyDto);
        PageModel<CardPermissionApplyDo> pageModel = cardPermissionApplyRepository.pageApply(cardPermissionApplyQuery);
        if (CollectionUtils.isNotEmpty(pageModel.getList())) {
            pageModel.getList().forEach(item -> cardPermissionApplyDomainService.fillPermissionApplyBeforeAdminPage(item));
        }
        return PageModel.build(cardPermissionApplyDtoConverter.toDtoList(pageModel.getList()),
                pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyBpmCallBack(BpmCallBackDto bpmCallBackDto) {
        CardPermissionApplyDo cardPermissionApplyDo = cardPermissionApplyRepository
                .findByBpmCode(bpmCallBackDto.getBusinessKey());
        Asserts.assertNotNull(cardPermissionApplyDo, ApplicationErrorCodeEnum.APPLICATION_NORMAL, "权限申请不存在");
        if (!CardPermissionApplyStatusEnum.LEADER_AUDIT.getCode().equals(cardPermissionApplyDo.getPermissionApplyStatus())) {
            return;
        }
        cardPermissionApplyDomainService.fillPermissionApplyBeforeDetail(cardPermissionApplyDo);
        if (BpmCallBackStatusEnum.COMPLETED.getStatus().equals(bpmCallBackDto.getProcessInstanceStatus())) {
            //审批通过更新表单状态
            cardPermissionApplyDomainService.pass(cardPermissionApplyDo);
            cardPermissionApplyRepository.updateByPass(cardPermissionApplyDo);
            //发起新流程
            cardPermissionApplyDomainService.groupPermissionRelation(cardPermissionApplyDo);
            batchCreateGrantBpm(cardPermissionApplyDo);
        } else if (BpmCallBackStatusEnum.TERMINATED.getStatus().equals(bpmCallBackDto.getProcessInstanceStatus())) {
            //自己终止
            cardPermissionApplyDomainService.cancel(cardPermissionApplyDo);
            cardPermissionApplyRepository.updateByCancel(cardPermissionApplyDo);
        } else if (BpmCallBackStatusEnum.REJECTED.getStatus().equals(bpmCallBackDto.getProcessInstanceStatus())) {
            //拒绝
            cardPermissionApplyDomainService.reject(cardPermissionApplyDo);
            cardPermissionApplyRepository.updateByReject(cardPermissionApplyDo);
        }
    }

    private void batchCreateGrantBpm(CardPermissionApplyDo cardPermissionApplyDo) {

        cardPermissionApplyDo.getRelationMap().forEach((admin, relationList) -> {
            try {
                String seq = buildGrantBpm(cardPermissionApplyDo, relationList, admin);
                relationList.forEach(relation -> relation.setBpmCode(seq));
                //标记记录为已发送
                cardPermissionApplyRepository.updateRelation(relationList);
            } catch (Exception e) {
                //这里只捕捉异常，不抛出异常，对于失败的记录后续定时任务补偿
                log.error("create grant bpm error. param:{}", GsonUtils.toJsonFilterNullField(relationList));
            }
        });
        //删除code
        RedisUtils.delete(cardPermissionApplyDo.getBpmCode());
    }

    private String buildGrantBpm(CardPermissionApplyDo cardPermissionApplyDo,
                                 List<CardPermissionApplyGroupDo> relationList, String admin) {
        Boolean isForever = checkIsForeverTime(cardPermissionApplyDo);
        String seq = SeqUtil.seq(Card.CARD_PERMISSION_GRANT_BPM_SEQ, Card.CARD_PERMISSION_GRANT_BPM_LOCK_CODE);
        String title = BpmCodeEnum.SPECIFIC_PERMISSION_GRANT.getDesc();
        if (Objects.nonNull(cardPermissionApplyDo) && Objects.nonNull(cardPermissionApplyDo.getSafetyClass())) {
            title += "-" + cardPermissionApplyDo.getSafetyClass().getName();
        }
        BpmCreateDto bpmCreateDto = new BpmCreateDto();
        bpmCreateDto.setProcessName(title);
        bpmCreateDto.setBusinessKey(seq);
        bpmCreateDto.setBpmCodeEnum(BpmCodeEnum.SPECIFIC_PERMISSION_GRANT);
        bpmCreateDto.setStartUserId(cardPermissionApplyDo.getPersonInfo().getUserName());
        Map<String, Object> formData = Maps.newHashMap();
        formData.put(BpmPermissionGrantFormEnum.APPLY_USER.getFieldName(),
                cardPermissionApplyDo.getPersonInfo().getUserName());
        formData.put(BpmPermissionGrantFormEnum.CONTROL_TYPE.getFieldName(),
                Objects.nonNull(cardPermissionApplyDo.getSafetyClass()) ? cardPermissionApplyDo.getSafetyClass().getName() : Common.CONCAT);
        if (isForever) {
            formData.put(BpmPermissionGrantFormEnum.FOREVER_VALID_PERIOD.getFieldName(),
                    BpmPermissionGrantFormEnum.FOREVER_VALID_PERIOD.getFieldDesc());
        } else {
            formData.put(BpmPermissionGrantFormEnum.VALID_PERIOD.getFieldName(),
                    String.format(VALID_PERIOD, ZonedDateTimeUtils.formatChinaYMD(cardPermissionApplyDo.getStartTime()),
                            ZonedDateTimeUtils.formatChinaYMD(cardPermissionApplyDo.getEndTime())));
        }
        formData.put(BpmPermissionGrantFormEnum.APPLY_REASON.getFieldName(),
                cardPermissionApplyDo.getReasonName());
        if (CollectionUtils.isNotEmpty(cardPermissionApplyDo.getPersonInfo().getDeptInfoDoList())) {
            for (DeptInfoDo deptInfoDo : cardPermissionApplyDo.getPersonInfo().getDeptInfoDoList()) {
                formData.put(BpmPermissionGrantFormEnum.APPLY_USER_DEPT.getFieldName(),
                        deptInfoDo.getDeptId());
            }
        }

        List<Map<String, Object>> detailList = Lists.newArrayList();
        for (CardPermissionApplyGroupDo relation : relationList) {
            Map<String, Object> detailMap = Maps.newHashMap();
            detailMap.put(BpmPermissionGrantDetailFormEnum.CARD_GROUP_CONFIG_NAME.getFieldName(), relation.getCardGroupConfigDo().getCardGroupName());
            detailMap.put(BpmPermissionGrantDetailFormEnum.PARK_NAME.getFieldName(), relation.getCardGroupConfigDo().getParkName());
            CardGroupConfigDo config = relation.getCardGroupConfigDo();
            detailMap.put(BpmPermissionGrantDetailFormEnum.DEPARTMEN.getFieldName(),
                    Objects.nonNull(config.getDeptInfo()) ? config.getDeptInfo().getDeptName() : Common.CONCAT);
            String admin1 = config.getAdminList().stream().map(CardGroupConfigAdminDo::getAdminUserName)
                    .collect(Collectors.joining(Common.COMMA));
            detailMap.put(BpmPermissionGrantDetailFormEnum.ADMIN.getFieldName(), admin1);
            detailList.add(detailMap);
        }
        formData.put(BpmPermissionGrantFormEnum.GROUP_FORM.getFieldName(), detailList);

        List<Map<String, Object>> processList = Lists.newArrayList();
        Map<String, Object> firstProcessMap = Maps.newHashMap();
        String taskId = (String) RedisUtils.get(cardPermissionApplyDo.getBpmCode());
        firstProcessMap.put(BpmPermissionProcessEnum.TASK_ID.getKey(), taskId);
        firstProcessMap.put(BpmPermissionProcessEnum.BUSINESS_KEY.getKey(), cardPermissionApplyDo.getBpmCode());
        processList.add(firstProcessMap);
        formData.put(BpmPermissionGrantFormEnum.ASSOCIATED_PROCESS.getFieldName(), processList);
        bpmCreateDto.setFormData(formData);
        Map<String, Object> variables = Maps.newHashMap();
        variables.put(BpmPermissionApplyFormEnum.CONTROL_TYPE.getFieldName(),
                Objects.nonNull(cardPermissionApplyDo.getSafetyClass()) ? cardPermissionApplyDo.getSafetyClass().getName() : Common.CONCAT);
        List<CardGroupConfigDo> partGroupConfigList =
                relationList.stream().map(CardPermissionApplyGroupDo::getCardGroupConfigDo).collect(Collectors.toList());
        List<CardGroupConfigDo> allGroupConfigList = cardPermissionApplyDo.getGroupConfigList();
        if (Objects.isNull(allGroupConfigList)) {
            allGroupConfigList = Lists.newArrayList();
        }
        fillBpmCardGroup(partGroupConfigList, allGroupConfigList.size(), variables, cardPermissionApplyDo.getPersonInfo());
        variables.put(BpmPermissionGrantFormEnum.PROCESS_DEF.getFieldName(), admin);
        if (CollectionUtils.isNotEmpty(partGroupConfigList)) {
            variables.put(BpmPermissionApplyFormEnum.CARD_GROUP_PARK_CODE.getFieldName(),
                    partGroupConfigList.get(0).getParkCode());
        }
        bpmCreateDto.setVariable(variables);
        log.info("second create bpm req:{}", bpmCreateDto);
        bpmSdk.createBpm(bpmCreateDto);
        return seq;
    }

    private Boolean checkIsForeverTime(CardPermissionApplyDo cardPermissionApplyDo) {
        return Objects.nonNull(cardPermissionApplyDo) && Objects.nonNull(cardPermissionApplyDo.getEndTime())
                && OAUCFCommonConstants.INT_ZERO.equals(ZonedDateTimeUtils.compare(Card.DEFAULT_END_TIME,
                cardPermissionApplyDo.getEndTime()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void grantCallBack(BpmCallBackDto bpmCallBackDto) {
        List<CardPermissionApplyGroupDo> cardPermissionApplyGroupDoList = cardPermissionApplyRepository
                .findRelationByBpmCode(bpmCallBackDto.getBusinessKey());
        Asserts.assertNotEmpty(cardPermissionApplyGroupDoList, ApplicationErrorCodeEnum.APPLICATION_NORMAL, "授权申请不存在");
        boolean deal = cardPermissionApplyGroupDoList.stream().anyMatch(item ->
                CardPermissionApplyGroupStatusEnum.AUDIT_REFUSED.getCode().equals(item.getCardPermissionStatus()) ||
                        CardPermissionApplyGroupStatusEnum.AUDIT_COMPLETED.getCode().equals(item.getCardPermissionStatus()));
        if (deal) {
            return;
        }
        String applyCode = cardPermissionApplyGroupDoList.get(0).getPermissionApplyCode();
        CardPermissionApplyDo applicantApplyDo = cardPermissionApplyRepository.findByApplyCode(applyCode);
        Asserts.assertNotNull(applicantApplyDo, ApplicationErrorCodeEnum.APPLICATION_NORMAL, "权限申请不存在");
        cardPermissionApplyDomainService.fillPermissionApplyBeforeDetail(applicantApplyDo);
        if (BpmCallBackStatusEnum.COMPLETED.getStatus().equals(bpmCallBackDto.getProcessInstanceStatus())) {
            //审批通过更新表单状态
            cardPermissionApplyDomainService.passGrantApply(cardPermissionApplyGroupDoList);
            //更新授权表状态
            cardPermissionApplyRepository.updateRelation(cardPermissionApplyGroupDoList);
            //执行添加权限逻辑
            addPermission(applicantApplyDo, cardPermissionApplyGroupDoList);
        } else if (BpmCallBackStatusEnum.TERMINATED.getStatus().equals(bpmCallBackDto.getProcessInstanceStatus())
                || BpmCallBackStatusEnum.REJECTED.getStatus().equals(bpmCallBackDto.getProcessInstanceStatus())) {
            cardPermissionApplyDomainService.rejectGrantApply(cardPermissionApplyGroupDoList);
            //更新授权表状态
            cardPermissionApplyRepository.updateRelation(cardPermissionApplyGroupDoList);
        }
        computeStatusAfterGrant(applicantApplyDo);
    }

    @Override
    public CardPermissionApplyDto findAdminDetail(Long permissionApplyId) {
        CardPermissionApplyDo cardPermissionApplyDo = new CardPermissionApplyDo();
        cardPermissionApplyDo.setId(permissionApplyId);
        cardPermissionApplyDomainService.fillPermissionApplyBeforeDetailForAdmin(cardPermissionApplyDo);
        return cardPermissionApplyDtoConverter.toDto(cardPermissionApplyDo);
    }

    @Override
    public CardPermissionApplyDto findCardGroupList(String uid) {
        CardPermissionApplyDo cardPermissionApplyDo = new CardPermissionApplyDo();
        cardPermissionApplyDo.setUid(uid);
        cardPermissionApplyDomainService.loadCardPermissionGroupForOnceOpened(cardPermissionApplyDo);
        return cardPermissionApplyDtoConverter.toDto(cardPermissionApplyDo);
    }

    @Override
    public List<CardPermissionApplyDto> findExpiredPermissionList() {
        List<CardPermissionApplyDo> expiredPermissionList = cardPermissionApplyDomainService.findExpiredPermissionList();
        return cardPermissionApplyDtoConverter.toDtoList(expiredPermissionList);
    }

    @Override
    public List<CardPermissionApplyDto> findWillExpiredPermissionList() {
        List<CardPermissionApplyDo> expiredPermissionList = cardPermissionApplyDomainService.findWillExpiredPermissionList();
        return cardPermissionApplyDtoConverter.toDtoList(expiredPermissionList);
    }

    @Override
    public void doPermissionExpireNotify(CardPermissionApplyDto permissionApplyDto) {
        if (Objects.isNull(RedisUtils.get(SafetyConstants.IS_SEND_MESSAGE_KEY + permissionApplyDto.getPermissionApplyCode()))
                || (Boolean) RedisUtils.get(SafetyConstants.IS_SEND_MESSAGE_KEY + permissionApplyDto.getPermissionApplyCode())) {
            CardPermissionApplyDo cardPermissionApplyDo = cardPermissionApplyDtoConverter.toDo(permissionApplyDto);
            // 填充人员信息
            cardPermissionApplyDomainService.fillPermissionApplyBeforeDetail(cardPermissionApplyDo);
            //  加载卡信息
            CardInfoDto cardInfo = cardService.getDetailCardInfo(cardPermissionApplyDo.getCardId());

            // 只有正式卡和临时卡并且在使用中，才发送消息
            if (cardInfo != null 
                && cardInfo.getCardStatus().equals(CardStatusEnum.USING.getCode()) 
                && (CardTypeEnum.EMPLOYEE_CARD.getNumber().equals(cardInfo.getCardType()) 
                    || CardTypeEnum.TEMP_CARD.getNumber().equals(cardInfo.getCardType()))) {
                SafetyUmsNotifyDo safetyUmsNotifyDo = new SafetyUmsNotifyDo();
                safetyUmsNotifyDo.setConfigId(SafetyUmsConfigEnum.CARD_PERMISSION_EXPIRED_NOTIFY_SEND_LARK.getCode());
                buildPermissionExpireNotify(cardPermissionApplyDo, safetyUmsNotifyDo);
                safetyUmsNotifyRepository.createNotify(safetyUmsNotifyDo);
            }
        }
    }

    @Override
    public void editNotifyStatus(String uid, String permissionApplyCode) {
        CardPermissionApplyDo cardPermissionApplyDo = new CardPermissionApplyDo();
        cardPermissionApplyDo.setUid(uid);
        cardPermissionApplyDo.setPermissionApplyCode(permissionApplyCode);
        cardPermissionApplyRepository.editNotifyStatus(cardPermissionApplyDo);
    }

    @Override
    public List<CardPermissionApplyDto> findPreExpireApplyList() {
        return cardPermissionApplyDtoConverter.toDtoList(cardPermissionApplyRepository.findPreExpireApplyList());
    }

    @Override
    public void doPermissionExpireClose(CardPermissionApplyDto cardPermissionApplyDto) {
        //临时卡为空或已归还,查询正式卡
        CardInfoDto cardInfo = cardService.getCardInfoByUid(cardPermissionApplyDto.getUid(), TEMP_CARD.getNumber());
        if (cardInfo == null || cardInfo.getCardStatus().equals(CardStatusEnum.RETURNED.getCode())) {
            cardInfo = cardService.getCardInfoByUid(cardPermissionApplyDto.getUid(), EMPLOYEE_CARD.getNumber());
        }
        CardPermissionApplyDo cardPermissionApplyDo = cardPermissionApplyDtoConverter.toDo(cardPermissionApplyDto);
        // 填充信息
        cardPermissionApplyDomainService.fillPermissionApplyBeforeDetail(cardPermissionApplyDo);
        // 只有正式卡和临时卡并且在使用中，才发送消息
        if (cardInfo != null 
            && cardInfo.getCardStatus().equals(CardStatusEnum.USING.getCode()) 
            && (CardTypeEnum.EMPLOYEE_CARD.getNumber().equals(cardInfo.getCardType()) 
                || CardTypeEnum.TEMP_CARD.getNumber().equals(cardInfo.getCardType()))) {
            SafetyUmsNotifyDo safetyUmsNotifyDo = new SafetyUmsNotifyDo();
            safetyUmsNotifyDo.setConfigId(SafetyUmsConfigEnum.CARD_PERMISSION_EXPIRED_CLOSE_SEND_LARK.getCode());
            buildPermissionExpireNotify(cardPermissionApplyDo, safetyUmsNotifyDo);
            safetyUmsNotifyRepository.createNotify(safetyUmsNotifyDo);
        }
        //更新状态为已过期
        cardPermissionApplyDo.setPermissionApplyStatus(CardPermissionApplyStatusEnum.EXPIRED.getCode());
        cardPermissionApplyRepository.updateById(cardPermissionApplyDo);
    }

    @Override
    public BpmLinkDto goToDetailBpm(String bpmCode) {
        //根据bpmCode获取权限申请单
        CardPermissionApplyDo cardPermissionApplyDo = cardPermissionApplyRepository.findByBpmCode(bpmCode);
        //断言不为空
        Asserts.assertNotNull(cardPermissionApplyDo, CardPermissionApplyDomainErrorCodeEnum.CARD_PERMISSION_APPLY_NOT_EXIST);
        //根据uid填充人员信息
        SafetyPersonDo safetyPersonDo = new SafetyPersonDo();
        safetyPersonDo.setUid(cardPermissionApplyDo.getUid());
        safetyPersonDomainService.fillPersonInfoWithBase(safetyPersonDo);
        return bpmSdk.allTypeLink(bpmCode, safetyPersonDo.getUserName());
    }

    private void buildPermissionExpireNotify(CardPermissionApplyDo cardPermissionApplyDo, SafetyUmsNotifyDo safetyUmsNotifyDo) {
        safetyUmsNotifyDo.setReceiver(cardPermissionApplyDo.getUid());
        safetyUmsNotifyDo.setAppCode(AppCodeEnum.CARD.getAppCode());
        safetyUmsNotifyDo.setSendTime(ZonedDateTime.now());
        safetyUmsNotifyDo.setSendStatus(SafetyUmsSendStatusEnum.SEND_WAIT.getStatus());
        Map<String, String> params = Maps.newHashMap();
        SafetySpaceParkDto spaceParkDto = new SafetySpaceParkDto();
        try {
            spaceParkDto = spaceSdk.getParkByCode(cardPermissionApplyDo.getPermissionApplyGroupList().get(0).getCardGroupConfigDo().getParkCode());
        } catch (Exception e) {
            log.error("获取园区信息失败：{}", e);
        }
        String parkName = spaceParkDto != null ? spaceParkDto.getParkName() : "";
        params.put("createTime", ZonedDateTimeUtils.formatChinaYMD(cardPermissionApplyDo.getCreateTime()));
        params.put("controlTypeDesc", CardClassEnum.getName(cardPermissionApplyDo.getControlType()));
        params.put("parkName", parkName);
        params.put("startTime", ZonedDateTimeUtils.formatChinaYMD(cardPermissionApplyDo.getStartTime()));
        params.put("name", cardPermissionApplyDo.getPersonInfo().getDisplayName());
        params.put("endTime", ZonedDateTimeUtils.formatChinaYMD(cardPermissionApplyDo.getEndTime()));

        // createTimeEn
        params.put("createTimeEn", ZonedDateTimeUtils.formatYMDWithRod(cardPermissionApplyDo.getCreateTime()));
        // startTimeEn
        params.put("startTimeEn", ZonedDateTimeUtils.formatYMDWithRod(cardPermissionApplyDo.getStartTime()));
        // endTimeEn
        params.put("endTimeEn", ZonedDateTimeUtils.formatYMDWithRod(cardPermissionApplyDo.getEndTime()));
        try {
            params.put("link", String.format(SafetyConstants.LARK_URL, cardLarkAppId) + "&path=" +
                    URLEncoder.encode(String.format(SafetyConstants.CARD_PERMISSION_SHOW_BACK_PATH,
                            cardPermissionApplyDo.getId()), "UTF-8"));
            params.put("uid", cardPermissionApplyDo.getUid());
            params.put("permissionApplyCode", cardPermissionApplyDo.getPermissionApplyCode());
            String linkIgnore = cardWorkBench + String.format("/api/v1/applet/common/edit/notify/status?uid=%s&permissionApplyCode=%s",
                    cardPermissionApplyDo.getUid(),
                    cardPermissionApplyDo.getPermissionApplyCode());
            params.put("link_ignore", linkIgnore);

        } catch (Exception e) {
            log.error("----- buildPermissionExpireNotify buildParamMap link error : {}", cardPermissionApplyDo.getId());
        }
        safetyUmsNotifyDo.setParams(JacksonUtils.bean2Json(params));
    }

    private void addPermission(CardPermissionApplyDo applicantApplyDo, List<CardPermissionApplyGroupDo> cardPermissionApplyGroupDoList) {
        //查询工卡信息
        CardInfoDo cardInfoDo = cardDomainService.findCardInfoById(applicantApplyDo.getCardId());
        Asserts.assertNotNull(cardInfoDo, CardApplicationErrorCodeEnum.CARD_INFO_NOT_FOUND);
        Set<String> cardGroupConfigCodeList = cardPermissionApplyGroupDoList.stream()
                .map(CardPermissionApplyGroupDo::getCardGroupCode).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(cardGroupConfigCodeList)) {
            return;
        }
        List<CardGroupConfigDo> cardGroupConfigDoList = cardGroupConfigRepository.findListByCode(cardGroupConfigCodeList);
        List<String> carrierGroupCodeList = Lists.newArrayList();
        for (CardGroupConfigDo cardGroupConfigDo : cardGroupConfigDoList) {
            carrierGroupCodeList.addAll(cardGroupConfigDo.getCarrierGroupCodeList());
        }
        List<SafetyCarrierGroupDo> safetyCarrierGroupDoList = safetyCarrierGroupDomainService.findCarrierGroupByCodeList(carrierGroupCodeList);
        if (CollectionUtils.isNotEmpty(safetyCarrierGroupDoList)) {
            PermissionAddDto permissionAddDto = new PermissionAddDto();
            permissionAddDto.setCardId(cardInfoDo.getId());
            List<PermissionDetailDto> permissionDetailDtoList = Lists.newArrayList();

            Map<String, List<String>> carrierCodeList = safetyCarrierGroupDoList.stream()
                    .collect(Collectors.groupingBy(SafetyCarrierGroupDo::getClassCode,
                            Collectors.mapping(SafetyCarrierGroupDo::getCarrierGroupCode, Collectors.toList())));
            for (Entry<String, List<String>> entry : carrierCodeList.entrySet()) {
                PermissionDetailDto permissionDetailDto = new PermissionDetailDto();
                permissionDetailDto.setCarrierGroupClass(entry.getKey());
                permissionDetailDto.setCarrierGroupCodeList(entry.getValue());
                permissionDetailDto.setStartTime(applicantApplyDo.getStartTime());
                permissionDetailDto.setEndTime(applicantApplyDo.getEndTime());
                permissionDetailDtoList.add(permissionDetailDto);
            }
            permissionAddDto.setPermissionDetailList(permissionDetailDtoList);
            commonProducer.sendDelay(RocketMqTopicEnum.ADD_PERMISSION.getTopicName(), GsonUtils.toJsonFilterNullField(permissionAddDto), 2);
        }
    }

    private void computeStatusAfterGrant(CardPermissionApplyDo applicantApplyDo) {
        cardPermissionApplyDomainService.fillPermissionApplyBeforeDetail(applicantApplyDo);
        cardPermissionApplyDomainService.computeStatusAfterGrant(applicantApplyDo);
        cardPermissionApplyRepository.updateByFinal(applicantApplyDo);
    }
}
