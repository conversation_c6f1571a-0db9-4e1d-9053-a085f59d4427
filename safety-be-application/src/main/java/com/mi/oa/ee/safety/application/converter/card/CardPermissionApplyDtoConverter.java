package com.mi.oa.ee.safety.application.converter.card;

import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardPermissionApplyDto;
import com.mi.oa.ee.safety.application.dto.safety.SafetyClassDto;
import com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto;
import com.mi.oa.ee.safety.common.enums.card.CardClassEnum;
import com.mi.oa.ee.safety.common.enums.card.CardPermissionApplyGroupStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardPermissionApplyStatusEnum;
import com.mi.oa.ee.safety.domain.model.CardPermissionApplyDo;
import com.mi.oa.ee.safety.domain.model.CardPermissionApplyGroupDo;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupCarrierDo;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDo;
import com.mi.oa.ee.safety.domain.model.SafetyClassDo;
import com.mi.oa.ee.safety.domain.query.card.CardPermissionApplyQuery;
import com.mi.oa.ee.safety.infra.repository.query.SafetyClassQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> denghui
 * @desc
 * @date 2024/4/2 22:17
 */
@Mapper(componentModel = "spring")
public interface CardPermissionApplyDtoConverter {

    SafetyClassQuery toClassQuery(CardPermissionApplyDto dto);

    List<CardPermissionApplyDto> classToDtoList(List<SafetyClassDo> list);

    @Mapping(target = "controlType", source = "classCode")
    @Mapping(target = "controlTypeName", source = "name", qualifiedByName = "toControlTypeName")
    CardPermissionApplyDto toGroupType(SafetyClassDo classDo);

    @Named("toControlTypeName")
    default String toControlTypeName(String name) {
        return CardClassEnum.getDescByName(name);
    }


    @Mapping(target = "cardApply.applyType", source = "cardType")
    @Mapping(target = "cardInfo.cardType", source = "cardType")
    @Mapping(target = "id", source = "permissionApplyId")
    @Mapping(target = "parkCode", source = "parkCode")
    CardPermissionApplyDo toDo(CardPermissionApplyDto cardPermissionApplyDto);

    @Mapping(target = "permissionApplyId", source = "id")
    @Mapping(target = "photoUrl", source = "cardApply.photoUrl")
    @Mapping(target = "applyStatus", source = "permissionApplyStatus")
    @Mapping(target = "companyName", source = "personInfo.companyName")
    @Mapping(target = "applyBpmCode", source = "bpmCode")
    @Mapping(target = "cardType", source = "cardInfo.cardType")
    @Mapping(target = "cardId", source = "cardInfo.id")
    @Mapping(target = "displayName", source = "personInfo.displayName")
    @Mapping(target = "userName", source = "personInfo.userName")
    @Mapping(target = "controlTypeName", source = "controlType", qualifiedByName = "toControlTypeDesc")
    @Mapping(target = "firstDeptName", source = "personInfo.firstDeptName")
    @Mapping(target = "secondDeptName", source = "personInfo.secondDeptName")
    @Mapping(target = "thirdDeptName", source = "personInfo.thirdDeptName")
    @Mapping(target = "fourthDeptName", source = "personInfo.fourthDeptName")
    @Mapping(target = "cardGroupList", source = "permissionApplyGroupList", qualifiedByName = "toCardGroupList")
    @Mapping(target = "existCardGroupList", source = "existPermissionApplyGroupList", qualifiedByName = "toCardGroupList")
    CardPermissionApplyDto toDto(CardPermissionApplyDo cardPermissionApplyDo);

    @Named("toCardGroupList")
    default List<CardGroupConfigDto> toCardGroupList(List<CardPermissionApplyGroupDo> applyGroupList) {
        List<CardGroupConfigDto> res = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(applyGroupList)) {
            applyGroupList.forEach(item -> {
                CardGroupConfigDto cardGroupConfigDto = toCardGroupDto(item);
                cardGroupConfigDto.setRecordStatusEnum(item.getCardGroupConfigDo().getRecordStatusEnum());
                cardGroupConfigDto.setApplyFlag(item.getCardGroupConfigDo().getApplyFlag());
                res.add(cardGroupConfigDto);
            });
        }
        return res;
    }

    @Mapping(target = "cardGroupName", source = "cardGroupConfigDo.cardGroupName")
    @Mapping(target = "controlType", source = "cardGroupConfigDo.controlType")
    @Mapping(target = "parkCode", source = "cardGroupConfigDo.parkCode")
    @Mapping(target = "parkName", source = "cardGroupConfigDo.parkName")
    @Mapping(target = "cityId", source = "cardGroupConfigDo.cityId")
    @Mapping(target = "cityName", source = "cardGroupConfigDo.cityName")
    @Mapping(target = "deptName", source = "cardGroupConfigDo.deptInfo.deptName")
    @Mapping(target = "provinceId", source = "cardGroupConfigDo.provinceId")
    @Mapping(target = "provinceName", source = "cardGroupConfigDo.provinceName")
    @Mapping(target = "carrierGroupList", source = "cardGroupConfigDo.carrierGroupList", qualifiedByName =
            "toCarrierGroupList")
    @Mapping(target = "cardPermissionStatusDesc", source = "cardPermissionStatus", qualifiedByName = "toDesc")
    @Mapping(target = "bpmCode", expression = "java(dealBpmCode(item.getBpmCode(), item.getCardPermissionStatus()))")
    CardGroupConfigDto toCardGroupDto(CardPermissionApplyGroupDo item);

    default String dealBpmCode(String bpmCode, Integer applyStatus) {
        if (CardPermissionApplyGroupStatusEnum.getExistBpmLinkStatusList().contains(applyStatus)) {
            return bpmCode;
        }
        return null;
    }

    @Named("toCarrierGroupList")
    default List<SafetyCarrierGroupDto> toCarrierGroupList(List<SafetyCarrierGroupDo> carrierGroupList) {
        List<SafetyCarrierGroupDto> res = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(carrierGroupList)) {
            carrierGroupList.forEach(item -> {
                SafetyCarrierGroupDto safetyCarrierGroupDto = toSafetyCarrierGroupDto(item);
                res.add(safetyCarrierGroupDto);
            });
        }
        return res;
    }

    @Mapping(target = "carrierNum", source = "changeSafetyCarrierGroupCarrierDoList", qualifiedByName = "toNum")
    SafetyCarrierGroupDto toSafetyCarrierGroupDto(SafetyCarrierGroupDo item);

    @Named("toNum")
    default Integer toNum(List<SafetyCarrierGroupCarrierDo> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            return list.size();
        }
        return 0;
    }

    @Named("toDesc")
    default String toDesc(Integer applyStatus) {
        return CardPermissionApplyGroupStatusEnum.codeToDesc(applyStatus);
    }

    @Named("toControlTypeDesc")
    default String toControlTypeDesc(String controlType) {
        return CardClassEnum.getDesc(controlType);
    }

    @Mapping(target = "permissionApplyStatus", source = "applyStatus")
    CardPermissionApplyQuery toApplyQuery(CardPermissionApplyDto cardPermissionApplyDto);

    List<CardPermissionApplyDto> toDtoList(List<CardPermissionApplyDo> list);
}
