package com.mi.oa.ee.safety.application.impl.safety.monitor;

import com.alibaba.excel.ExcelWriter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.ee.safety.application.converter.safety.SafetyRightDtoConverter;
import com.mi.oa.ee.safety.application.dto.safety.SafetyPersonDto;
import com.mi.oa.ee.safety.application.dto.safety.SafetyRightDto;
import com.mi.oa.ee.safety.application.dto.safety.excel.RightExcelDto;
import com.mi.oa.ee.safety.common.dto.SafetySupplierDataMonitorDto;
import com.mi.oa.ee.safety.common.enums.card.CardStatusEnum;
import com.mi.oa.ee.safety.common.enums.card.CardTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyModelTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySupplierCodeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySupplierDataMonitorUmsTypeEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetySyncStatusEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyUmsConfigEnum;
import com.mi.oa.ee.safety.domain.ability.CardPersonInfoAbility;
import com.mi.oa.ee.safety.domain.errorcode.SafetyPersonErrorCodeEnum;
import com.mi.oa.ee.safety.domain.model.CardElectronRecordDo;
import com.mi.oa.ee.safety.domain.model.CardInfoDo;
import com.mi.oa.ee.safety.domain.model.CardPersonInfoDo;
import com.mi.oa.ee.safety.domain.model.SafetyRightDo;
import com.mi.oa.ee.safety.domain.model.SafetyUmsNotifyDo;
import com.mi.oa.ee.safety.domain.service.CardDomainService;
import com.mi.oa.ee.safety.domain.service.SafetyRightDomainService;
import com.mi.oa.ee.safety.infra.remote.model.supplier.BaseQuery;
import com.mi.oa.ee.safety.infra.remote.model.supplier.SupplierCarrierGroupDto;
import com.mi.oa.ee.safety.infra.remote.model.supplier.SupplierRightDto;
import com.mi.oa.ee.safety.infra.remote.sdk.supplier.SafetySupplierStrategySdk;
import com.mi.oa.ee.safety.infra.repository.CardElectronRecordRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyRightRepository;
import com.mi.oa.ee.safety.infra.repository.SafetyUmsNotifyRepository;
import com.mi.oa.ee.safety.infra.repository.query.CardElectronRecordQuery;
import com.mi.oa.ee.safety.infra.repository.query.SafetyRightQuery;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.ZonedDateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> denghui
 * @desc,
 * @date 2024/1/18 17:06
 */
@Slf4j
@Service
public class SafetySupplierRightDataMonitorService
        extends SafetyToSupplierDataMonitorAbstractFactory<SafetyRightDto, SupplierRightDto, RightExcelDto> {

    @Resource
    private SafetyRightRepository safetyRightRepository;

    @Resource
    private SafetyRightDomainService safetyRightDomainService;

    @Resource
    private SafetyRightDtoConverter safetyRightDtoConverter;

    @Resource
    private SafetyUmsNotifyRepository safetyUmsNotifyRepository;

    @Resource
    private CardDomainService cardDomainService;

    @Resource
    private CardElectronRecordRepository cardElectronRecordRepository;
    @Autowired
    private CardPersonInfoAbility cardPersonInfoAbility;

    @Override
    protected String getSyncDoActionName() {
        return SafetyModelTypeEnum.RIGHT.getCode();
    }

    @Override
    protected void dealAbnormalData(List<SafetyRightDto> abnormalDataList) {
        log.info("todo ......");
    }

    @Override
    protected void doUmsMessageWarning(List<SafetyRightDto> abnormalDataList, ExcelWriter excelWriter,
                                       String excelUri) {
        if (CollectionUtils.isNotEmpty(abnormalDataList)) {
            //excel写入本地
            writeExcel(abnormalDataList, RightExcelDto.class, excelWriter, OAUCFCommonConstants.INT_THREE);
            //构建载体数据异常消息
            SafetyUmsNotifyDo safetyUmsNotifyDo = buildUmsNotify(abnormalDataList,
                    SafetyUmsConfigEnum.SAFETY_DATA_MONITOR_RIGHT_LARK, excelUri);
            //保存消息
            safetyUmsNotifyRepository.createNotify(safetyUmsNotifyDo);
        }
    }

    @Override
    protected List<SafetyRightDto> findAbnormalDataList(List<SafetyRightDto> dtoList, List<SupplierRightDto> supplierDtoList) {
        List<SafetyRightDto> results = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dtoList)) {
            List<String> mediumCodeList = dtoList.stream().map(SafetyRightDto::getMediumCode).collect(Collectors.toList());
            log.info("query medium codes : {}", mediumCodeList);
            //查询卡信息
            List<CardInfoDo> cardInfoList = cardDomainService.findListByMediumCodeList(mediumCodeList);
            //查询电子卡信息
            CardElectronRecordQuery recordQuery = new CardElectronRecordQuery();
            recordQuery.setMediumCodeList(mediumCodeList);
            List<CardElectronRecordDo> electronRecordList =
                    cardElectronRecordRepository.queryElectronRecordList(recordQuery);
            if (CollectionUtils.isEmpty(cardInfoList)) {
                dtoList.forEach(item -> item.setSafetySupplierDataMonitorUmsTypeEnum(SafetySupplierDataMonitorUmsTypeEnum.SAFETY_TO_SUPPLIER_EXTRA_LARK));
                return dtoList;
            }
            List<String> safetyEncryptCodeList = cardInfoList.stream().map(CardInfoDo::getMediumEncryptCode).collect(Collectors.toList());
            Map<String, CardInfoDo> mediumCardMap = cardInfoList.stream().collect(Collectors.toMap(CardInfoDo::getMediumCode, Function.identity(),
                    (k1, k2) -> k1));
            Map<String, CardElectronRecordDo> mediumElectronCardMap = Maps.newHashMap();
            List<String> electronEncryptCodeList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(electronRecordList)) {
                mediumElectronCardMap = electronRecordList.stream().collect(Collectors.toMap(CardElectronRecordDo::getMediumCode, Function.identity(),
                        (k1, k2) -> k1));
                electronEncryptCodeList =
                        electronRecordList.stream().map(CardElectronRecordDo::getMediumEncryptCode).collect(Collectors.toList());
            }
            Map<String, List<SafetyRightDto>> mediumCodeGroups = dtoList.stream().collect(Collectors.groupingBy(SafetyRightDto::getMediumCode));
            List<String> safetyEncryptPhoneList = convertPersonAndGetEncryptPhone(mediumCodeGroups);
            for (Map.Entry<String, List<SafetyRightDto>> entry : mediumCodeGroups.entrySet()) {
                //当前介质对应的卡信息
                CardInfoDo cardInfoDo = mediumCardMap.get(entry.getKey());
                //当前介质对应电子卡信息
                CardElectronRecordDo cardElectronRecordDo = mediumElectronCardMap.get(entry.getKey());
                List<SafetyRightDto> value = entry.getValue();
                if (ObjectUtils.isEmpty(cardInfoDo) && ObjectUtils.isEmpty(cardElectronRecordDo)) {
                    //若卡信息为空 则认为全是安防侧多出
                    log.error("----- current mediumCode : {} card not exist", entry.getKey());
                    for (SafetyRightDto safetyRightDto : value) {
                        safetyRightDto.setSafetySupplierDataMonitorUmsTypeEnum(SafetySupplierDataMonitorUmsTypeEnum.SAFETY_TO_SUPPLIER_EXTRA_LARK);
                        results.add(safetyRightDto);
                    }
                    continue;
                }
                if (CollectionUtils.isNotEmpty(supplierDtoList)) {
                    for (SupplierRightDto supplierRightDto : supplierDtoList) {
                        if (supplierRightDto.getSupplierMediumDto() != null) {
                            //供应商侧加密卡号在安防侧是否存在 存在则校验权限组是否一致 不存在则根据卡状态确定异常类型
                            if (safetyEncryptCodeList.contains(supplierRightDto.getSupplierMediumDto().getMediumEncryptCode())
                                    || electronEncryptCodeList.contains(supplierRightDto.getSupplierMediumDto().getMediumEncryptCode())) {
                                //霍泥2.0 medium不为空 mediumEncryptCode是加密卡号
                                if (supplierRightDto.getSupplierMediumDto().getMediumEncryptCode().equals(cardInfoDo.getMediumEncryptCode())) {
                                    checkCarrierGroupIsConsistent(supplierRightDto, results, value);
                                }
                            } else {
                                if (ObjectUtils.isNotEmpty(cardInfoDo)) {
                                    buildAbnormalDataListByCardStatus(results, cardInfoDo, value);
                                } else {
                                    buildAbnormalDataListForElectron(results, value);
                                }
                            }
                            //熵基person不为空 且employId是手机号加密
                        } else if (supplierRightDto.getSupplierPersonDto() != null) {
                            //供应商侧工号在安防侧是否存在 存在则校验权限组是否一致 不存在则根据卡状态确定异常类型
                            if (safetyEncryptPhoneList.contains(supplierRightDto.getSupplierPersonDto().getEmployeeId())) {
                                if (supplierRightDto.getSupplierPersonDto().getEmployeeId().equals(getCurrentEncryptPhone(entry))) {
                                    checkCarrierGroupIsConsistent(supplierRightDto, results, value);
                                }
                            } else {
                                if (ObjectUtils.isNotEmpty(cardInfoDo)) {
                                    buildAbnormalDataListByCardStatus(results, cardInfoDo, value);
                                } else {
                                    buildAbnormalDataListForElectron(results, value);
                                }
                            }
                        }
                    }
                } else {
                    if (ObjectUtils.isNotEmpty(cardInfoDo)) {
                        buildAbnormalDataListByCardStatus(results, cardInfoDo, value);
                    } else {
                        buildAbnormalDataListForElectron(results, value);
                    }
                }
            }
        }
        return results;
    }

    private void buildAbnormalDataListForElectron(List<SafetyRightDto> results, List<SafetyRightDto> value) {
        value.forEach(item -> item.setSafetySupplierDataMonitorUmsTypeEnum(SafetySupplierDataMonitorUmsTypeEnum.SUPPLIER_TO_SAFETY_LACK_LARK));
        results.addAll(value);
    }

    /**
     * @param supplierRightDto
     * @return void
     * @desc 检查权限组是否一致
     * <AUTHOR> denghui
     * @date 2024/2/1 15:07
     */
    private void checkCarrierGroupIsConsistent(SupplierRightDto supplierRightDto,
                                               List<SafetyRightDto> results, List<SafetyRightDto> value) {
        //获取供应商侧 当前介质的权限组
        List<SupplierCarrierGroupDto> supplierCarrierGroupDtoList = supplierRightDto.getSupplierCarrierGroupDtoList();
        //若供应商侧权限组为空
        if (CollectionUtils.isEmpty(supplierCarrierGroupDtoList)) {
            value.forEach(item -> item.setSafetySupplierDataMonitorUmsTypeEnum(SafetySupplierDataMonitorUmsTypeEnum.SUPPLIER_TO_SAFETY_LACK_LARK));
            results.addAll(value);
        } else {
            Map<String, SupplierCarrierGroupDto> supplierAccessCodeMap =
                    supplierCarrierGroupDtoList.stream()
                            .collect(Collectors.toMap(SupplierCarrierGroupDto::getSupplierId, Function.identity(),
                                    (k1, k2) -> k1));
            for (SafetyRightDto safetyRightDto : value) {
                SupplierCarrierGroupDto supplierCarrierGroupDto = supplierAccessCodeMap.get(safetyRightDto.getSupplierAccessCode());
                if (ObjectUtils.isEmpty(supplierCarrierGroupDto) && ObjectUtils.isNotEmpty(safetyRightDto.getSafetyCarrierGroup())) {
                    supplierCarrierGroupDto = supplierAccessCodeMap.get(safetyRightDto.getSafetyCarrierGroup().getSupplierAccessCode());
                }
                //供应商存在 跳过
                if (ObjectUtils.isNotEmpty(supplierCarrierGroupDto)) {
                    continue;
                }
                safetyRightDto.setSafetySupplierDataMonitorUmsTypeEnum(SafetySupplierDataMonitorUmsTypeEnum.SUPPLIER_TO_SAFETY_LACK_LARK);
                results.add(safetyRightDto);
            }
        }
    }

    private List<String> convertPersonAndGetEncryptPhone(Map<String, List<SafetyRightDto>> mediumCodeGroups) {
        //安防侧加密电话号码
        List<String> safetyEncryptPhoneList = Lists.newArrayList();
        for (Map.Entry<String, List<SafetyRightDto>> entry : mediumCodeGroups.entrySet()) {
            safetyEncryptPhoneList.add(getCurrentEncryptPhone(entry));
        }
        return safetyEncryptPhoneList;
    }

    private String getCurrentEncryptPhone(Map.Entry<String, List<SafetyRightDto>> entry) {
        //当前介质对应人是唯一的
        SafetyPersonDto safetyPersonDto = entry.getValue().get(0).getSafetyPersonDto();
        CardPersonInfoDo cardPersonInfoDo = cardPersonInfoAbility.getCardPerson(safetyPersonDto.getUid());
        if (cardPersonInfoDo == null) {
            throw new BizException(SafetyPersonErrorCodeEnum.PERSON_INFO_NOT_EXIST);
        }
        return cardPersonInfoDo.getZktecoEmployeeId();
    }

    private void buildAbnormalDataListByCardStatus(List<SafetyRightDto> results, CardInfoDo cardInfoDo,
                                                   List<SafetyRightDto> value) {
        if (CardStatusEnum.CANCELED.getCode().equals(cardInfoDo.getCardStatus()) ||
                CardStatusEnum.RETURNED.getCode().equals(cardInfoDo.getCardStatus())) {
            //已销卡或已还卡 安防多出预警
            value.forEach(item -> item.setSafetySupplierDataMonitorUmsTypeEnum(SafetySupplierDataMonitorUmsTypeEnum.SAFETY_TO_SUPPLIER_EXTRA_LARK));
            results.addAll(value);
        } else if (CardStatusEnum.USING.getCode().equals(cardInfoDo.getCardStatus())) {
            //使用中或时间未到未激活状态 供应商缺失预警
            value.forEach(item -> item.setSafetySupplierDataMonitorUmsTypeEnum(SafetySupplierDataMonitorUmsTypeEnum.SUPPLIER_TO_SAFETY_LACK_LARK));
            results.addAll(value);
        } else if (CardStatusEnum.NOT_ACTIVE.getCode().equals(cardInfoDo.getCardStatus())
                && !CardTypeEnum.EMPLOYEE_CARD.getNumber().equals(cardInfoDo.getCardType())) {
            //安防存在 供应商不存在 且卡状态是未激活 卡类型非正式卡 供应商缺失预警
            value.forEach(item -> item.setSafetySupplierDataMonitorUmsTypeEnum(SafetySupplierDataMonitorUmsTypeEnum.SUPPLIER_TO_SAFETY_LACK_LARK));
            results.addAll(value);
        }
    }

    @Override
    protected List<SupplierRightDto> findAndBuildSupplierDtoList(SafetySupplierDataMonitorDto<SafetyRightDto> monitorDto,
                                                                 SafetySupplierStrategySdk safetySupplierStrategySdk) {
        List<SupplierRightDto> supplierRightDtoList =
                safetyRightDtoConverter.dtoToSupplierRightDtoList(monitorDto.getWaitComparisonData());
        supplierRightDtoList.forEach(it -> {
            if (SafetySupplierCodeEnum.ZKTECO.getSupplierCode().equals(it.getSupplierCode())) {
                //装填ZktecoEmployeeId
                CardPersonInfoDo cardPerson = cardPersonInfoAbility.getCardPerson(it.getUid());
                if (cardPerson == null) {
                    throw new BizException(SafetyPersonErrorCodeEnum.PERSON_INFO_NOT_EXIST);
                }
                if (it.getSupplierPersonDto() != null) {
                    it.getSupplierPersonDto().setEmployeeId(cardPerson.getZktecoEmployeeId());
                }
            }
        });
        List<SupplierRightDto> res = Lists.newArrayList();
        BaseQuery<SupplierRightDto> baseQuery = new BaseQuery<>();
        baseQuery.setData(supplierRightDtoList);
        baseQuery.setQueryStartTime(monitorDto.getStartTime() != null ?
                ZonedDateTimeUtils.formatYMDHMSWithRod(monitorDto.getStartTime()) :
                ZonedDateTimeUtils.formatYMDHMSWithRod(ZonedDateTimeUtils.getZonedDateTimeBegin(ZonedDateTime.now())));
        baseQuery.setQueryEndTime(monitorDto.getEndTime() != null ?
                ZonedDateTimeUtils.formatYMDHMSWithRod(monitorDto.getEndTime()) :
                ZonedDateTimeUtils.formatYMDHMSWithRod(ZonedDateTimeUtils.getZonedDateTimeEnd(ZonedDateTime.now())));
        baseQuery.setPageSize(10000);
        PageModel<SupplierRightDto> pageModel = null;
        Integer pageNum = OAUCFCommonConstants.INT_ONE;
        try {
            do {
                baseQuery.setPageNum(pageNum);
                pageModel = safetySupplierStrategySdk.querySupplierRightByCondition(baseQuery);
                if (ObjectUtils.isNotEmpty(pageModel) && CollectionUtils.isNotEmpty(pageModel.getList())) {
                    res.addAll(pageModel.getList());
                } else {
                    log.info("----- bizId:{} query rightList supplierCode : {}  pageNum : {}  is end",
                            monitorDto.getLogBizId(), monitorDto.getSupplierCode(), pageNum);
                }
                pageNum++;
            } while (pageModel != null && CollectionUtils.isNotEmpty(pageModel.getList()));
        } catch (Exception e) {
            log.error("----- bizId:{} query batch right list is error :{}", monitorDto.getLogBizId(), e.toString());
        }
        return res;
    }

    @Override
    protected List<SafetyRightDto> getWaitComparisonDtoList(SafetySupplierDataMonitorDto<SafetyRightDto> monitorDto) {
        SafetyRightQuery safetyRightQuery = new SafetyRightQuery();
        safetyRightQuery.setQueryStartTime(monitorDto.getStartTime() != null ? monitorDto.getStartTime().toEpochSecond() : null);
        safetyRightQuery.setQueryEndTime(monitorDto.getEndTime() != null ? monitorDto.getEndTime().toEpochSecond() : null);
        safetyRightQuery.setSyncStatus(SafetySyncStatusEnum.SUCCESS_SYNC.getCode());
        safetyRightQuery.setIsDeleted(OAUCFCommonConstants.LONG_ZERO);
        //获取所有的安防权限
        List<SafetyRightDo> safetyRightDoList = safetyRightRepository.listByConditions(safetyRightQuery);
        if (CollectionUtils.isNotEmpty(safetyRightDoList)) {
            safetyRightDoList.stream().forEach(item -> safetyRightDomainService.fillSafetyRight(item));
        }
        //安防介质和安防人员都不为空的
        safetyRightDoList = safetyRightDoList.stream().filter(item -> item.getSafetyMediumDo() != null && item.getSafetyPersonDo() != null)
                .collect(Collectors.toList());
        return safetyRightDtoConverter.toDtoList(safetyRightDoList);
    }

    @Override
    protected List<SafetyRightDto> preDealWaitComparisonDataList(SafetySupplierDataMonitorDto<SafetyRightDto> monitorDto) {
        return monitorDto.getWaitComparisonData();
    }

    @Override
    protected String getSupplierCode(SafetyRightDto doItem) {
        return doItem.getSupplierCode();
    }

    @Override
    protected String getSheetName() {
        return "safety right";
    }

    @Override
    protected List<RightExcelDto> convertToExcelDataList(List<SafetyRightDto> supplierDataList) {
        List<RightExcelDto> excelDtoList = Lists.newArrayList();
        for (SafetyRightDto safetyRightDto : supplierDataList) {
            RightExcelDto rightExcelDto = new RightExcelDto();
            rightExcelDto.setCarrierGroupCode(safetyRightDto.getCarrierGroupCode());
            rightExcelDto.setMediumCode(safetyRightDto.getMediumCode());
            rightExcelDto.setSupplierCode(safetyRightDto.getSupplierCode());
            if (ObjectUtils.isNotEmpty(safetyRightDto.getSafetySupplierDataMonitorUmsTypeEnum())) {
                rightExcelDto.setSafetyOrSupplier(safetyRightDto.getSafetySupplierDataMonitorUmsTypeEnum().getSafetyOrSupplier());
                rightExcelDto.setMoreOrLess(safetyRightDto.getSafetySupplierDataMonitorUmsTypeEnum().getMoreOrLess());
            }
            excelDtoList.add(rightExcelDto);
        }
        return excelDtoList;
    }
}
