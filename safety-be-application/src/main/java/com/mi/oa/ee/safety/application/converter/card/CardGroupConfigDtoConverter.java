package com.mi.oa.ee.safety.application.converter.card;

import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigAddDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigEditDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigGroupApplyUserDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigGroupApplyUserQueryDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigQueryDto;
import com.mi.oa.ee.safety.application.dto.card.permission.CardGroupConfigSyncDto;
import com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto;
import com.mi.oa.ee.safety.common.enums.card.CardClassEnum;
import com.mi.oa.ee.safety.domain.model.CardGroupConfigDo;
import com.mi.oa.ee.safety.domain.model.CardPermissionApplyGroupDo;
import com.mi.oa.ee.safety.domain.model.CardPermissionApplyGroupUserDo;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierDo;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupCarrierDo;
import com.mi.oa.ee.safety.domain.model.SafetyCarrierGroupDo;
import com.mi.oa.ee.safety.domain.query.card.CardGroupConfigGroupApplyUserQuery;
import com.mi.oa.ee.safety.domain.query.card.CardGroupConfigQuery;
import com.mi.oa.infra.oaucf.constant.OAUCFCommonConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/2 20:17
 */
@Mapper(componentModel = "spring")
public interface CardGroupConfigDtoConverter {
    CardGroupConfigDo toDo(CardGroupConfigAddDto addDto);

    CardGroupConfigDo editToDo(CardGroupConfigEditDto editDto);

    @Mapping(source = "safetyClassDo.name", target = "controlTypeName", qualifiedByName = "toControlTypeName")
    @Mapping(source = "spaceList", target = "parkList")
    @Mapping(source = "deptInfo.deptName", target = "deptName")
    @Mapping(source = "carrierGroupList", target = "carrierGroupList", qualifiedByName = "toGroupDtoList")
    CardGroupConfigDto toDto(CardGroupConfigDo cardGroupConfigDo);

    @Named("toControlTypeName")
    default String toControlTypeName(String name) {
        return CardClassEnum.getDescByName(name);
    }

    @Named("toGroupDtoList")
    default List<SafetyCarrierGroupDto> toGroupDtoList(List<SafetyCarrierGroupDo> groupList) {
        return toCarrierGroupDtoList(groupList);
    }

    List<SafetyCarrierGroupDto> toCarrierGroupDtoList(List<SafetyCarrierGroupDo> doList);

    @Mapping(source = "safetyCarrierDoList", target = "carrierNum", qualifiedByName = "toCarrierNum")
    SafetyCarrierGroupDto toCarrierGroupDto(SafetyCarrierGroupDo safetyCarrierGroupDo);

    @Named("toCarrierNum")
    default Integer toCarrierNum(List<SafetyCarrierDo> carrierDoList) {
        if (CollectionUtils.isNotEmpty(carrierDoList)) {
            return carrierDoList.size();
        } else {
            return OAUCFCommonConstants.INT_ZERO;
        }
    }

    List<CardGroupConfigDto> toDtoList(List<CardGroupConfigDo> cardGroupConfigList);

    CardGroupConfigQuery toQuery(CardGroupConfigQueryDto queryDto);

    CardGroupConfigDo syncDtoToDo(CardGroupConfigSyncDto cardGroupConfigSyncDto);

    CardGroupConfigGroupApplyUserQuery toCardGroupConfigGroupApplyUserDto(CardGroupConfigGroupApplyUserQueryDto query);

    @Mapping(source = "safetyPersonDo.uid", target = "uid")
    @Mapping(source = "safetyPersonDo.userName", target = "userName")
    @Mapping(source = "safetyPersonDo.displayName", target = "displayName")
    @Mapping(source = "safetyPersonDo.deptInfoDoList", target = "deptInfoDoList")
    @Mapping(source = "startTime", target = "startTime")
    @Mapping(source = "endTime", target = "endTime")
    CardGroupConfigGroupApplyUserDto toCardGroupConfigGroupApplyUserDto(CardPermissionApplyGroupUserDo cardPermissionApplyGroupUserDo);

    List<CardGroupConfigGroupApplyUserDto> toCardGroupConfigGroupApplyUserDtoList(List<CardPermissionApplyGroupUserDo> list);

    List<CardPermissionApplyGroupUserDo> toCardGroupConfigGroupApplyUserDo(List<CardPermissionApplyGroupDo> list);
}
