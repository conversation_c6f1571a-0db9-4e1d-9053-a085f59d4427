package com.mi.oa.ee.safety.application.impl.card.permission;

import com.mi.oa.ee.safety.application.service.card.permission.CardPermissionDataRefreshService;
import com.mi.oa.ee.safety.domain.model.CardGroupConfigDo;
import com.mi.oa.ee.safety.domain.model.CardPermissionApplyDo;
import com.mi.oa.ee.safety.domain.model.CardPermissionApplyGroupDo;
import com.mi.oa.ee.safety.infra.repository.CardGroupConfigRepository;
import com.mi.oa.ee.safety.infra.repository.CardPermissionApplyRepository;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 权限申请单数据刷新服务实现
 * <AUTHOR>
 * @date 2024-07-30
 */
@Slf4j
@Service
public class CardPermissionDataRefreshServiceImpl implements CardPermissionDataRefreshService {

    @Resource
    private CardPermissionApplyRepository cardPermissionApplyRepository;

    @Resource
    private CardGroupConfigRepository cardGroupConfigRepository;

    @Override
    public String refreshParkCode(String permissionApplyCode, Integer pageSize) {
        log.info("开始刷新权限申请单园区编码，permissionApplyCode: {}, pageSize: {}", permissionApplyCode, pageSize);
        
        if (pageSize == null || pageSize <= 0) {
            pageSize = 100; // 默认分页大小
        }
        
        if (StringUtils.isNotBlank(permissionApplyCode)) {
            // 单个申请单刷新
            return refreshSingleApply(permissionApplyCode);
        } else {
            // 全量异步刷新
            return refreshAllAppliesAsync(pageSize);
        }
    }

    /**
     * 刷新单个申请单
     */
    private String refreshSingleApply(String permissionApplyCode) {
        try {
            log.info("开始刷新单个申请单: {}", permissionApplyCode);
            
            // 查询申请单
            CardPermissionApplyDo applyDo = cardPermissionApplyRepository.findByApplyCode(permissionApplyCode);
            if (applyDo == null) {
                return "申请单不存在: " + permissionApplyCode;
            }
            
            // 如果已经有园区编码，跳过
            if (StringUtils.isNotBlank(applyDo.getParkCode())) {
                return "申请单已有园区编码，跳过: " + permissionApplyCode;
            }
            
            // 刷新园区编码
            String result = refreshApplyParkCode(applyDo);
            log.info("单个申请单刷新完成: {}, 结果: {}", permissionApplyCode, result);
            return result;
            
        } catch (Exception e) {
            log.error("刷新单个申请单失败: {}", permissionApplyCode, e);
            return "刷新失败: " + e.getMessage();
        }
    }

    /**
     * 异步全量刷新
     */
    private String refreshAllAppliesAsync(Integer pageSize) {
        // 异步执行全量刷新
        CompletableFuture.runAsync(() -> {
            try {
                refreshAllApplies(pageSize);
            } catch (Exception e) {
                log.error("异步全量刷新失败", e);
            }
        });
        
        return "已启动异步全量刷新任务，请查看日志获取进度";
    }

    /**
     * 全量刷新所有申请单
     */
    public void refreshAllApplies(Integer pageSize) {
        log.info("开始全量刷新权限申请单园区编码，分页大小: {}", pageSize);
        
        int pageNum = 1;
        int totalProcessed = 0;
        int successCount = 0;
        int skipCount = 0;
        int errorCount = 0;
        
        while (true) {
            try {
                // 分页查询申请单
                PageModel<CardPermissionApplyDo> pageResult = cardPermissionApplyRepository.pageForRefresh(
                        pageNum, pageSize, null);
                
                List<CardPermissionApplyDo> applyList = pageResult.getList();
                if (CollectionUtils.isEmpty(applyList)) {
                    break; // 没有更多数据
                }
                
                log.info("处理第{}页，共{}条记录", pageNum, applyList.size());
                
                // 处理当前页的数据
                for (CardPermissionApplyDo applyDo : applyList) {
                    try {
                        // 如果已经有园区编码，跳过
                        if (StringUtils.isNotBlank(applyDo.getParkCode())) {
                            skipCount++;
                            continue;
                        }
                        
                        String result = refreshApplyParkCode(applyDo);
                        if (result.startsWith("成功")) {
                            successCount++;
                        } else {
                            errorCount++;
                        }
                        
                    } catch (Exception e) {
                        log.error("处理申请单失败: {}", applyDo.getPermissionApplyCode(), e);
                        errorCount++;
                    }
                    
                    totalProcessed++;
                }
                
                // 如果是最后一页，退出循环
                if (pageNum >= pageResult.getPageSize()) {
                    break;
                }
                
                pageNum++;
                
                // 避免过于频繁的数据库操作
                Thread.sleep(100);
                
            } catch (InterruptedException e) {
                log.error("线程被中断", e);
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                log.error("分页查询申请单失败，页码: {}", pageNum, e);
                break;
            }
        }
        
        log.info("全量刷新完成，总处理: {}, 成功: {}, 跳过: {}, 失败: {}", 
                totalProcessed, successCount, skipCount, errorCount);
    }

    /**
     * 刷新单个申请单的园区编码
     */
    private String refreshApplyParkCode(CardPermissionApplyDo applyDo) {
        try {
            // 查询申请单的权限包列表
            List<CardPermissionApplyGroupDo> groupList = cardPermissionApplyRepository
                    .findPermissionApplyGroupListByCodeWithIsDeleted(applyDo.getPermissionApplyCode());
            
            if (CollectionUtils.isEmpty(groupList)) {
                return "申请单无权限包: " + applyDo.getPermissionApplyCode();
            }
            
            // 获取所有权限包编码
            Set<String> groupCodes = groupList.stream()
                    .map(CardPermissionApplyGroupDo::getCardGroupCode)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());
            
            if (groupCodes.isEmpty()) {
                return "申请单权限包编码为空: " + applyDo.getPermissionApplyCode();
            }
            
            // 查询权限包配置，获取园区编码
            List<CardGroupConfigDo> configList = cardGroupConfigRepository.findListByCode(groupCodes);
            if (CollectionUtils.isEmpty(configList)) {
                return "未找到权限包配置: " + applyDo.getPermissionApplyCode();
            }
            
            // 获取第一个权限包的园区编码（假设同一申请单的权限包都在同一园区）
            String parkCode = configList.get(0).getParkCode();
            if (StringUtils.isBlank(parkCode)) {
                return "权限包配置中园区编码为空: " + applyDo.getPermissionApplyCode();
            }
            
            // 更新申请单的园区编码
            applyDo.setParkCode(parkCode);
            cardPermissionApplyRepository.updateById(applyDo);
            
            log.info("成功更新申请单园区编码: {} -> {}", applyDo.getPermissionApplyCode(), parkCode);
            return "成功更新园区编码: " + parkCode;
            
        } catch (Exception e) {
            log.error("刷新申请单园区编码失败: {}", applyDo.getPermissionApplyCode(), e);
            return "刷新失败: " + e.getMessage();
        }
    }
}
