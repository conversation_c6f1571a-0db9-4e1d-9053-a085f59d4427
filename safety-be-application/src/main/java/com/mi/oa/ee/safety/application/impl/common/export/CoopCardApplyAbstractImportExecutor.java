package com.mi.oa.ee.safety.application.impl.common.export;

import com.google.common.collect.Lists;
import com.mi.info.comb.neptune.client.NeptuneClient;
import com.mi.oa.ee.safety.application.dto.card.event.CardApplyCreateEvent;
import com.mi.oa.ee.safety.application.dto.card.shared.CoopCardApplyImportExcelDto;
import com.mi.oa.ee.safety.application.impl.common.exception.ImportErrorException;
import com.mi.oa.ee.safety.application.impl.common.export.data.CoopCardApplyImportFunction;
import com.mi.oa.ee.safety.common.enums.ImportResultEnum;
import com.mi.oa.ee.safety.common.enums.ImportStatusEnum;
import com.mi.oa.ee.safety.common.enums.RocketMqTopicEnum;
import com.mi.oa.ee.safety.common.enums.card.CardSeqEnum;
import com.mi.oa.ee.safety.common.excel.function.ImportFunction;
import com.mi.oa.ee.safety.common.utils.GsonUtils;
import com.mi.oa.ee.safety.domain.model.AsyncImportTaskDo;
import com.mi.oa.ee.safety.infra.remote.mq.CommonProducer;
import com.mi.oa.ee.safety.infra.seq.generator.SeqGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

@Slf4j
@Service
public abstract class CoopCardApplyAbstractImportExecutor
        extends AsyncImportAbstractExecutor<CoopCardApplyImportExcelDto> {

    @Resource
    private CoopCardApplyImportFunction coopCardApplyImportFunction;

    @Resource
    private SeqGenerator seqGenerator;

    @Resource
    private CommonProducer commonProducer;

    @Override
    protected Class<CoopCardApplyImportExcelDto> getClassType() {
        return CoopCardApplyImportExcelDto.class;
    }

    @Override
    protected ImportFunction<CoopCardApplyImportExcelDto> getImportFunction() {
        return coopCardApplyImportFunction;
    }

    @Override
    protected ImportStatusEnum doBusiness(List<CoopCardApplyImportExcelDto> importExcelDtoList, AsyncImportTaskDo asyncImportTaskDo) {
        ImportStatusEnum statusEnum = ImportStatusEnum.UNDEFINED;
        Map<String, List<CoopCardApplyImportExcelDto>> responsibleAccount = importExcelDtoList.stream()
                .collect(Collectors.groupingBy(this::buildKey));
        for (Map.Entry<String, List<CoopCardApplyImportExcelDto>> entry : responsibleAccount.entrySet()) {
            List<CoopCardApplyImportExcelDto> nowList = entry.getValue();
            String seq = seqGenerator.next(CardSeqEnum.COOP_CARD_BPM_CODE_CREATE.getCode());
            for (CoopCardApplyImportExcelDto coopCardApplyImportExcelDto : nowList) {
                try {
                    if (checkImportExcelDto(coopCardApplyImportExcelDto)) {
                        coopCardApplyImportExcelDto.setBpmCode(seq);
                        doBusinessForCard(coopCardApplyImportExcelDto, null);
                        statusEnum = (statusEnum == ImportStatusEnum.FAILED || statusEnum == ImportStatusEnum.PART_SUCCESS ?
                                ImportStatusEnum.PART_SUCCESS : ImportStatusEnum.SUCCESS);
                        coopCardApplyImportExcelDto.setResult(NeptuneClient.getInstance().parseEntryTemplate(ImportResultEnum.SUCCESS.getResult())
                                .replace("{", "").replace("}", ""));
                    } else {
                        //失败
                        coopCardApplyImportExcelDto.setResult(NeptuneClient.getInstance().parseEntryTemplate(ImportResultEnum.FAIL.getResult())
                                .replace("{", "").replace("}", ""));
                        statusEnum = (statusEnum == ImportStatusEnum.SUCCESS || statusEnum == ImportStatusEnum.PART_SUCCESS ?
                                ImportStatusEnum.PART_SUCCESS : ImportStatusEnum.FAILED);
                    }
                } catch (Exception e) {
                    coopCardApplyImportExcelDto.setResult(NeptuneClient.getInstance().parseEntryTemplate(ImportResultEnum.FAIL.getResult())
                            .replace("{", "").replace("}", ""));
                    coopCardApplyImportExcelDto.setException(StringUtils.substring(e.getMessage()
                            .replace("{", "").replace("}", ""), 0, 200));
                    statusEnum = (statusEnum == ImportStatusEnum.SUCCESS || statusEnum == ImportStatusEnum.PART_SUCCESS ?
                            ImportStatusEnum.PART_SUCCESS : ImportStatusEnum.FAILED);
                }
            }
            //过滤没有id的
            List<Long> applyIds = nowList.stream().map(CoopCardApplyImportExcelDto::getApplyId)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(applyIds)) {
                //制卡单创建成功消息发送
                CardApplyCreateEvent event = CardApplyCreateEvent.builder()
                        .applyIdList(Lists.newArrayList(applyIds)).build();
                try {
                    commonProducer.send(RocketMqTopicEnum.APPLY_CREATE.getTopicName(), GsonUtils.toJsonFilterNullField(event));
                } catch (Exception e) {
                    log.error("rocket mq send {} error", event, e);
                }
            }
        }
        // 批量导入
        return statusEnum;
    }

    private String buildKey(CoopCardApplyImportExcelDto item) {
        return item.getResponsibleAccount() + item.getOfficePark();
    }

    abstract void doBusinessForCard(CoopCardApplyImportExcelDto businessParam, Map<String, String> accountEmpNoMap)
            throws ImportErrorException;

    abstract Boolean checkImportExcelDto(CoopCardApplyImportExcelDto coopCardApplyImportExcelDto);
}
