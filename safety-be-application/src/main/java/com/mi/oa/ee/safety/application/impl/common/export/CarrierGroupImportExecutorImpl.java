package com.mi.oa.ee.safety.application.impl.common.export;

import com.mi.info.comb.neptune.client.NeptuneClient;
import com.mi.oa.ee.safety.application.dto.card.shared.CarrierGroupImportExcelDto;
import com.mi.oa.ee.safety.application.impl.common.exception.ImportErrorException;
import com.mi.oa.ee.safety.application.service.safety.SafetyCarrierGroupService;
import com.mi.oa.ee.safety.common.constants.SafetyConstants;
import com.mi.oa.ee.safety.common.dto.SafetyCarrierGroupDto;
import com.mi.oa.ee.safety.common.dto.SafetySpaceParkDto;
import com.mi.oa.ee.safety.common.enums.ImportResultEnum;
import com.mi.oa.ee.safety.common.enums.card.CardClassEnum;
import com.mi.oa.ee.safety.common.enums.safety.SafetyCarrierGroupClassEnum;
import com.mi.oa.ee.safety.common.utils.CodeUtils;
import com.mi.oa.ee.safety.infra.remote.sdk.SpaceSdk;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service(CarrierGroupImportExecutorImpl.SERVICE_NAME)
public class CarrierGroupImportExecutorImpl extends CarrierGroupAbstractImportExecutor {

    public static final String SERVICE_NAME = "carrierGroupImportExecutor";

    @Resource
    SafetyCarrierGroupService safetyCarrierGroupService;


    @Resource
    SpaceSdk spaceSdk;

    @Override
    void doBusinessForCard(CarrierGroupImportExcelDto businessParam) throws ImportErrorException {
        //进行插入或者更新操作
        String name = businessParam.getName();
        SafetyCarrierGroupDto carrierGroupByName = new SafetyCarrierGroupDto();
        carrierGroupByName.setName(name);
        carrierGroupByName.setPageNum(1L);
        carrierGroupByName.setPageSize(10L);
        PageModel<SafetyCarrierGroupDto> safetyCarrierGroupDtoPageModel = safetyCarrierGroupService.pageCondition(carrierGroupByName);
        if (safetyCarrierGroupDtoPageModel == null || safetyCarrierGroupDtoPageModel.getList().isEmpty()) {
            // 说明不存在，return
            businessParam.setResult(ImportResultEnum.FAIL.getResult());
            businessParam.setException("权限组不存在，不做处理");
            throw new ImportErrorException("权限组不存在，不做处理");
        }
        carrierGroupByName = safetyCarrierGroupDtoPageModel.getList().stream()
                .filter(safetyCarrierGroupDto -> safetyCarrierGroupDto.getName().equals(name)).findFirst().orElse(null);

        if (carrierGroupByName != null) {
            // 说明存在，进行修改
            BeanUtils.copyProperties(businessParam, carrierGroupByName);
            List<Long> idlist = new ArrayList<>();
            idlist.add(carrierGroupByName.getId());
            carrierGroupByName.setIdList(idlist);
            safetyCarrierGroupService.updateOne(carrierGroupByName);
        } else {
            // 说明不存在，return
            businessParam.setResult(ImportResultEnum.FAIL.getResult());
            businessParam.setException("权限组不存在，不做处理");
            throw new ImportErrorException("权限组不存在，不做处理");
        }

    }

    @Override
    Boolean checkImportExcelDto(CarrierGroupImportExcelDto carrierGroupImportExcelDto) {
        String failStr = ImportResultEnum.FAIL.getResult().replace("{", "").replace("}", "");
        String successStr = ImportResultEnum.SUCCESS.getResult().replace("{", "").replace("}", "");

        String className = carrierGroupImportExcelDto.getClassName();

        // 普通 特殊 VIP 虚拟闸机 CardClassEnum
        if (!CardClassEnum.NORMAL.getName().equals(className) &&
                !CardClassEnum.SPECIAL.getName().equals(className) &&
                !CardClassEnum.VIP.getName().equals(className) &&
                !CardClassEnum.VIRTUAL.getName().equals(className)) {
            carrierGroupImportExcelDto.setResult(failStr);
            carrierGroupImportExcelDto.setException("权限组类型错误，只有：普通/特殊/VIP/虚拟闸机");
            return false;
        }
        String classCode = SafetyCarrierGroupClassEnum.getClassCodeByDesc(className);
        carrierGroupImportExcelDto.setClassCode(classCode);

        //状态 只有启用和禁用
        String status = carrierGroupImportExcelDto.getStatusName();
        if (!SafetyConstants.Common.STATUS_ENABLE.equals(status) && !SafetyConstants.Common.STATUS_DISABLE.equals(status)) {
            carrierGroupImportExcelDto.setResult(failStr);
            carrierGroupImportExcelDto.setException("状态错误，只有：启用/禁用");
            return false;
        }
        carrierGroupImportExcelDto.setStatus(SafetyConstants.Common.STATUS_ENABLE.equals(status) ? 0 : 1);
        // 根据parkName去找parkId 只有模糊匹配
        List<SafetySpaceParkDto> parkCodesByFuzzyName = spaceSdk.getParkCodesByFuzzyName(carrierGroupImportExcelDto.getParkName());
        if (parkCodesByFuzzyName.isEmpty()) {
            carrierGroupImportExcelDto.setResult(failStr);
            carrierGroupImportExcelDto.setException("园区名称不存在");
            return false;
        }
        if (parkCodesByFuzzyName.size() == 1) {
            carrierGroupImportExcelDto.setParkCode(parkCodesByFuzzyName.get(0).getParkCode());
            carrierGroupImportExcelDto.setResult(successStr);
            return true;
        }
        //只取和parkName 相同的
        List<SafetySpaceParkDto> resultList = parkCodesByFuzzyName.stream()
                .filter(park -> park.getParkName().equals(carrierGroupImportExcelDto.getParkName())).collect(Collectors.toList());
        if (resultList.isEmpty()) {
            carrierGroupImportExcelDto.setResult(failStr);
            carrierGroupImportExcelDto.setException("园区名称不存在");
            return false;
        }
        carrierGroupImportExcelDto.setParkCode(resultList.get(0).getParkCode());
        carrierGroupImportExcelDto.setResult(successStr);
        return true;

    }

    @Override
    protected String getFileName() {
        return NeptuneClient.getInstance().parseEntryTemplate("{{{权限组导入结果}}}") + SafetyConstants.Common.UNDER_LINE
                + CodeUtils.generateVisitorCode(6, true) + SafetyConstants.Common.POINT + SafetyConstants.Common.EXCEL_SUFFIX;
    }
}
